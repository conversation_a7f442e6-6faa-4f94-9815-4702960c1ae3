import { NgT<PERSON>plateOutlet, DatePipe, <PERSON><PERSON><PERSON>cy<PERSON>ipe, CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { FormBuilder, FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { VehicleCard } from '../../../../models/x_models/tracking/livetrack';
import { LivetrackService } from '../../../../services/x_apis/tracking/livetrack.service';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { MatCardModule } from '@angular/material/card';
import { AlertService } from '../../../../services/alert.service';
import { LandApprovalComponent } from './land-approval.component';
import { FarmerService } from '../../../../services/x_apis/farmer/farmer.service';
import { Farmers, VaoApprovalList, VoaApprovalDetail } from '../../../../models/x_models/farmer/farmer';
import { ApproveStatusType } from '../../../../enums/x_enums/dotnet/ApproveStatusType.enum';
import { Land, LandDetails } from '../../../../models/x_models/farmer/land';

@Component({
  selector: 'app-land-details',
  imports: [MatIcon,
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatOption,
    MatSelect,
    TranslateModule,
    CommonModule,
    MatCardModule,
    CloseFilterOnBlurDirective, CurrencyPipe, MatFormFieldModule,
    LandApprovalComponent
  ],
  templateUrl: './land-details.component.html',
  styleUrl: './land-details.component.scss'
})
export class LandDetailsComponent {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);
  lookupService = inject(LookupService);
  farmerService = inject(FarmerService);
  alertService = inject(AlertService);
  fb=inject(FormBuilder)
  form=this.fb.group({
    'villageId':[0],
    'search':['']
  })
  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);
  dataSource = signal(new MatTableDataSource<VoaApprovalDetail>([]))
  //  private static livetrackData: Farmers[]
  // VoaApprovalResource = resource({
  //   loader: async () => this.farmerService.getVoaApprovalList(this.form.value.villageId),
  // })
  // dataSource = linkedSignal(() => new MatTableDataSource<VoaApprovalDetail>(this.VoaApprovalResource?.value()));

  villageList=resource({loader:()=>this.lookupService.getVillageByVao()}).value;

  approvalList=signal<VaoApprovalList>([])

  displayedColumns: string[] = [
    'farmerCode',
    // "farmerName",
    "mobileNo",
     'village',
    "noOfLands",
    'date',
    //  'talukName',
    //  'talukName',
    //  'blockName',
    'vaoApprovalStatusTypeName',
    'actions',
  ];

  
  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  // villageId=new FormControl('');
  // search = new FormControl("");
  showGrid = signal(true);
  mapView = signal(false);
  status = StatusType;
  vehicleData = signal<number>(0);
  mapId = signal<number>(0);
  farmerId = signal<number>(0)
  selectedStatus=signal<number>(0)
  farmerName=signal<string>('');

  public ApproveStatus = ApproveStatusType
  vehicleCards = computed(() => {
    const list = this.approvalList();

    return [
      {
        title: 'NEW REQUEST',
        total: list?.filter(x => x.vaoApprovalStatusType == this.ApproveStatus.Pending).length,
        imageUrl: 'images/tracking/running.png',
        status: ApproveStatusType.Pending,
      },
      {
        title: 'PARTIAL APPROVED',
        total: list?.filter(x => x.vaoApprovalStatusType == this.ApproveStatus.PartiallyApproved).length,
        imageUrl: 'images/tracking/stoped.png',
        status: ApproveStatusType.PartiallyApproved,
      },
      {
        title: 'APPROVED',
        total: list?.filter(x => x.vaoApprovalStatusType == this.ApproveStatus.Approved).length,
        imageUrl: 'images/tracking/idle.png',
        status: ApproveStatusType.Approved,
      },
      {
        title: 'SEND BACK',
        total: list?.filter(x => x.vaoApprovalStatusType == this.ApproveStatus.Rejected).length,
        imageUrl: 'images/tracking/stoped.png',
        status: ApproveStatusType.Rejected,
      }
    ]
  });


  readonly search1 = signal('');

  readonly filteredVillages = computed(() =>
    this.villageList()?.filter(region =>
      region.value.toLowerCase().includes(this.search1().toLowerCase())
    )
  );

   filter(value: any) {
    this.search1.set(value.target.value);
  }


  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {

    this.getApprovedList(0)
   this.form.get('villageId')?.valueChanges.subscribe(async res=>{
     this.getApprovedList(res)

   })
  }

  async getApprovedList(villageId:any)
  {
    const res=await this.farmerService.getVoaApprovalList(villageId);
    this.approvalList.set(res);
    this.dataSource=linkedSignal(() => new MatTableDataSource<VoaApprovalDetail>(res))
      this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
  }

  filterCards(status: number) {
    this.selectedStatus.set(status)
    let list = this.approvalList()?.filter((x: any) => x.vaoApprovalStatusType == status)
    this.dataSource.set(new MatTableDataSource(list))

  }


  onRefresh() {
    //  this.getVehicleDetails(Number(this.form.value.regionId));
    this.form.controls['search'].setValue("");
    // this.VoaApprovalResource.reload()
    this.getApprovedList(0)
    this.selectedStatus.set(0)
    this.form.controls['villageId'].setValue(0)
  }


  onView(rowData: VoaApprovalDetail) {
    //  this.list.set(rowData);
    this.mode.set('edit');
    this.showGrid.set(false)
    this.farmerId.set(rowData.id)
    
    this.farmerName.set(rowData.farmerNameCode.charAt(0).toLocaleUpperCase()+rowData.farmerNameCode.slice(1))
    console.log(this.farmerId(), 'jjjjjjjjjjj');

  }

  onClose() {
    this.showGrid.set(true)
    // this.VoaApprovalResource.reload()
    this.form.controls['search'].setValue("");
    this.getApprovedList(0)

  }



  async onDelete(rowData: Farmers) {
    //   await confirmAndDelete(rowData, rowData.vahicle, 'Region', this.livetrackService, () => this.livetrackResource.reload());
  }

  onSearch() {
    const filterValue = this.form.controls['search'].value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
