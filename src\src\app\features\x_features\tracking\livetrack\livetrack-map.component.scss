  .mapcard {
    background-color: #fff;
    box-shadow: 0 0 10px 0 rgb(9 51 116 / 20%);
    border-radius: var(--4px);
    padding: 10px;
  }

  .map-grid {
    border: 1px solid lightgray;
    border-radius: 5px;
    box-shadow: 0 0 10px 0 rgb(9 51 116 / 20%);
    margin: 10px
  }

  .content {
    height: calc(100% - 180px);
  }

  .content {
    display: flex;
    margin-top: 12px;

    #mapId {
      width: 100%;
      min-height: 100%;
    }
  }

  #mapId {
    display: block;
    position: relative;
    width: 100%;
  }

  .resender_sec {
    margin-top: 68px;
    right: 10px;
    width: 44px;
    z-index: 999;
    position: absolute;
  }

  .status-icon {
    width: 24px;
    height: 24px;
  }

  .vehicle-lbl {
    color: gray;
    font-size: 15px;
    // margin:10px;
    text-align: left;
  }

  .vehicle-txt {
    color: black;
    font-size: 15px;
    // margin:10px;
    text-align: left;
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1rem;
  }

  .gauge-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  ngx-gauge {
    width: 300px;
    height: 200px;
    margin: 20px;
  }

  .row-12 {
    grid-column: span 12;
  }

  .row-8 {
    grid-column: span 8;
  }

  .row-4 {
    grid-column: span 4;
  }

  .row-6 {
    grid-column: span 6;
  }

  @media (max-width: 768px) {
    .grid-container {
      grid-template-columns: repeat(12, 1fr);
    }

    .row-8 {
      grid-column: span 12;
      text-align: left;
    }

    .row-4 {
      grid-column: span 12;
    }

    .row-6 {
      grid-column: span 12;
    }

    .actions {
      justify-content: flex-start;
    }

    .card-container {
      grid-template-columns: 1fr;
    }
  }