body {
  // --mat-menu-item-label-text-color: var(--sys-on-surface);
  // --mat-menu-item-icon-color: var(--sys-on-surface-variant);
  --mat-menu-item-hover-state-layer-color: var(--theme-10);
  --mat-menu-item-focus-state-layer-color: var(--theme-10);
  --mat-menu-container-color: #fff;
  --mat-menu-divider-color: var(--sys-surface-variant);
  --mat-menu-item-label-text-font: Roboto;
  --mat-menu-item-label-text-size: 14px;
  --mat-menu-item-label-text-tracking: 0.006rem;
  --mat-menu-item-label-text-line-height: 20px;
  --mat-menu-item-label-text-weight: 400;
  --mat-menu-container-shape: 8px;
  --mat-menu-divider-bottom-spacing: 5px;
  --mat-menu-divider-top-spacing: 5px;
  --mat-menu-item-spacing: 12px;
  --mat-menu-item-icon-size: 16px;
  --mat-menu-item-leading-spacing: 12px;
  --mat-menu-item-trailing-spacing: 12px;
  --mat-menu-item-with-icon-leading-spacing: 10px;
  --mat-menu-item-with-icon-trailing-spacing: 10px;
  --mat-menu-container-elevation-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25), 0px 6px 14px -6px rgba(19, 25, 39, 0.12);
}

.mat-mdc-menu-item {
  min-height: 30px !important;
  border-radius: 8px;
  min-width:180px !important;
  transition: .2s;

  .mat-icon {
    font-size: 18px;
  }
}

.mat-mdc-menu-content {
  padding-inline: 10px !important;
  display: flex;
  flex-direction: column;
  row-gap: 10px;
}

.mat-mdc-menu-item:not([disabled]):hover {
  --mat-menu-item-label-text-weight: 700;
}

.mat-mdc-menu-item-text, .mat-mdc-menu-item {
  transition: all .05s ease-in;
}
