{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/select.mjs"], "sourcesContent": ["export { Mat<PERSON>ptgroup, MatOption } from './option-B6mQ8PwE.mjs';\nexport { <PERSON><PERSON><PERSON><PERSON>, <PERSON>FormField, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>refix, MatSuffix } from './form-field-BPX7ZLIc.mjs';\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger } from './module-DBJe6lvw.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/keycodes';\nimport '@angular/core';\nimport 'rxjs';\nimport './ripple-BPguEKwi.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-Dy35mUmj.mjs';\nimport './structural-styles-B2ekkpE5.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/forms';\nimport './error-options-BPhcAVoK.mjs';\nimport './error-state-DAicm3pw.mjs';\nimport './module-BOQEdUAz.mjs';\nimport '@angular/cdk/observers';\nimport './common-module-DoCSSHRt.mjs';\nimport './index-BU5avYQW.mjs';\nimport './index-eRSoE0yr.mjs';\nimport './pseudo-checkbox-module-CUFRN-kl.mjs';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSelectAnimations = {\n  // Represents\n  // trigger('transformPanelWrap', [\n  //   transition('* => void', query('@transformPanel', [animateChild()], {optional: true})),\n  // ])\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: {\n    type: 7,\n    name: 'transformPanelWrap',\n    definitions: [{\n      type: 1,\n      expr: '* => void',\n      animation: {\n        type: 11,\n        selector: '@transformPanel',\n        animation: [{\n          type: 9,\n          options: null\n        }],\n        options: {\n          optional: true\n        }\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents\n  // trigger('transformPanel', [\n  //   state(\n  //     'void',\n  //     style({\n  //       opacity: 0,\n  //       transform: 'scale(1, 0.8)',\n  //     }),\n  //   ),\n  //   transition(\n  //     'void => showing',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       style({\n  //         opacity: 1,\n  //         transform: 'scale(1, 1)',\n  //       }),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),\n  // ])\n  /** This animation transforms the select's overlay panel on and off the page. */\n  transformPanel: {\n    type: 7,\n    name: 'transformPanel',\n    definitions: [{\n      type: 0,\n      name: 'void',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(1, 0.8)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => showing',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 1,\n            transform: 'scale(1, 1)'\n          },\n          offset: null\n        },\n        timings: '120ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '100ms linear'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { matSelectAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU1B,oBAAoB;AAAA,IAClB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW,CAAC;AAAA,UACV,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,QACD,SAAS;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,SAAS;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": []}