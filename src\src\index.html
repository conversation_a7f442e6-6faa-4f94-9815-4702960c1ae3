<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>TNCSC</title>
    <base href="/">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    
    <!-- google icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Sharp:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200&" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    
    <!-- google fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">

    <!-- leaflet map -->
     <link href="./leaflet/leaflet.css" rel="stylesheet">
     <link href="./leaflet/L.Icon.Pulse.css" rel="stylesheet">
     <link href="./leaflet/MarkerCluster.Default.css" rel="stylesheet">
     <link href="./leaflet/MarkerCluster.css" rel="stylesheet">
     <link href="./leaflet/leaflet.fullscreen.css" rel="stylesheet">
     <link rel="stylesheet" href="https://unpkg.com/leaflet@1.3.1/dist/leaflet.css" />

     <script src="./leaflet/leaflet.js"></script>
     <script src="./leaflet/MovingMarker.js"></script>
     <script src="./leaflet/L.Icon.Pulse.js"></script>
     <script src="./leaflet/MarkerCluster.js"></script>
     <script src="./leaflet/Leaflet.fullscreen.min.js"></script>
     <script src="./leaflet/leaflet.polylineDecorator.js"></script>
     <script src="./leaflet/leaflet.motion.js"></script>
     
  </head>
  
  <body class="mat-typography mat-app-background th-default">
    <app-root></app-root>
  </body>
</html>
