export interface HullingPricecut {
    id: number,
    hullingPriceCutType: number,
    productId: number,
    seasonId: number,
    regionId: number,
    minRange: number,
    maxRange: number,
    priceCut: number
}

export interface HullingPricecuts {
    id: number,
    hullingPriceCutTypeName: string,
    productName: string,
    seasonName: string,
    regionName: string,
    minRange: number,
    maxRange: number,
    priceCut: number
}

export type HullingPricecutResponse = HullingPricecuts[];