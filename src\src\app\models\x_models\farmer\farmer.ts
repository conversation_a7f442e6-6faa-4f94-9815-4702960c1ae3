export interface Farmers {
  id: number,
  farmerCode: string,
  farmerName: string,
  dateOfBirth?: string,
  mobileNo: string,
  genderType?: number,
  location?: string,
  noOfLands: number,
  isEkycVerified?: boolean
}
export interface VehicleCard {
  title: string;
  total: number;
  imageUrl: string;
}

export type FarmerResponse = Farmers[]

export interface VoaApprovalDetail {
  id: number,
  // farmerCode: string,
  // farmerName: string,
  mobileNo: string,
  alternateMobileNo: string,
  registeredTs: string,
  noOfLands: number,
  genderTypeName: string,
  regionName: string,
  talukName: string,
  blockName: string,
  villageName: string,
  vaoApprovalStatusType: number,
  vaoApprovalStatusTypeName: string,
  farmerNameCode:string
}

export type VaoApprovalList=VoaApprovalDetail[];