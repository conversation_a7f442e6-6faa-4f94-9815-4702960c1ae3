.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 20px;
  
  .page-title {
    text-align: center;
    color: #203664;
    margin-bottom: 30px;
    font-weight: 300;
    font-size: 2.5rem;
  }
}

// Form Section Styles
.form-section {
  margin-bottom: 30px;
  
  mat-card-header {
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #203664;
      
      mat-icon {
        font-size: 24px;
      }
    }
  }
  
  .employee-form {
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 10px;
      }
      
      .form-field {
        flex: 1;
        min-width: 200px;
      }
    }
    
    .form-actions {
      display: flex;
      gap: 15px;
      justify-content: flex-start;
      margin-top: 20px;
      
      button {
        min-width: 120px;
        
        mat-icon {
          margin-right: 8px;
        }
        
        mat-spinner {
          margin-right: 8px;
        }
      }
    }
  }
}

// Table Section Styles
.table-section {
  mat-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #203664;
      
      mat-icon {
        font-size: 24px;
      }
    }
    
    .header-actions {
      button {
        mat-icon {
          animation: spin 2s linear infinite;
        }
      }
    }
  }
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    
    mat-spinner {
      margin-bottom: 20px;
    }
    
    p {
      color: #666;
      font-size: 16px;
    }
  }
  
  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
    
    mat-icon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #ccc;
    }
    
    p {
      font-size: 16px;
      text-align: center;
    }
  }
  
  .table-container {
    overflow-x: auto;
    
    .employee-table {
      width: 100%;
      
      .mat-mdc-header-cell {
        font-weight: 600;
        color: #1976d2;
        background-color: #f8f9fa;
      }
      
      .mat-mdc-cell {
        padding: 16px 8px;
        
        .role-cell {
          display: flex;
          align-items: center;
          gap: 8px;
          
          mat-icon {
            color: #666;
            font-size: 18px;
          }
        }
        
        .matric-id {
          font-family: 'Courier New', monospace;
          background-color: #e3f2fd;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 600;
        }
        
        .level-badge {
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
          
          &.level-junior {
            background-color: #e8f5e8;
            color: #2e7d32;
          }
          
          &.level-senior {
            background-color: #e3f2fd;
            color: #1976d2;
          }
          
          &.level-lead {
            background-color: #fff3e0;
            color: #f57c00;
          }
          
          &.level-manager {
            background-color: #fce4ec;
            color: #c2185b;
          }
          
          &.level-director {
            background-color: #f3e5f5;
            color: #7b1fa2;
          }
        }
        
        .pay-amount {
          font-weight: 600;
          color: #2e7d32;
          font-size: 14px;
        }
      }
      
      .mat-mdc-row {
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

// Global Snackbar Styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }
  
  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }
}

// Animation for refresh button
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive Design
@media (max-width: 768px) {
  .container {
    padding: 10px;
    
    .page-title {
      font-size: 2rem;
    }
  }
  
  .table-section {
    .table-container {
      .employee-table {
        font-size: 14px;
        
        .mat-mdc-cell,
        .mat-mdc-header-cell {
          padding: 8px 4px;
        }
      }
    }
  }
}