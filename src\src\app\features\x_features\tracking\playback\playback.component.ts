import { Component, ElementRef, EventEmitter, inject, Input, input, Output, output, QueryList, signal, ViewChild, ViewChildren } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError, MatInputModule } from '@angular/material/input';
import { MatOption, MatSelect, MatSelectModule } from '@angular/material/select';
import {TranslateModule, TranslateService } from '@ngx-translate/core';
import { AlertService } from '../../../../services/alert.service';
import { PlaybackService } from '../../../../services/x_apis/tracking/playback.service';
import { MatTooltip, MatTooltipModule } from '@angular/material/tooltip';
import { NgxGauge, NgxGaugeModule } from 'ngx-gauge';
import { CommonModule, DatePipe } from '@angular/common';
import { Coordinate, PlayBackDetails } from '../../../../models/x_models/tracking/playback';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../../../../environments/environment';
import moment, { invalid } from 'moment';
import _ from 'lodash';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatDialogModule } from '@angular/material/dialog';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';

declare var L: any;

@Component({
  selector: 'app-playback',
  imports: [
    TranslateModule,
    MatIconModule,
    MatTooltip,
    NgxGaugeModule,
    DatePipe,
    CommonModule,
    FormsModule,
    FormsModule,
    TranslateModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatInputModule,
    MatFormFieldModule,
    MatDialogModule,
    MatButtonModule,
    MatButtonToggleModule,
    MatSelectModule,
    NgxGaugeModule
  ],
  templateUrl: './playback.component.html',
  styleUrl: './playback.component.scss'
})
export class PlaybackComponent {


  vehicleIdList = input.required<any>()
  alertService = inject(AlertService)
  closed = output<boolean>()
  translate = inject(TranslateService);
  playbackService = inject(PlaybackService);
  router = inject(Router);
  environment = signal(environment);
  route = inject(ActivatedRoute);


  @ViewChildren('scrollListRef') scrollListRef!: QueryList<any>;
  @ViewChildren('scrollTripsRef') scrollTripsRef: QueryList<any> | undefined;
  @Input() mapData: any;
  map: any;
  @ViewChild('mapId', { static: true }) mapcontainer!: ElementRef;
  options = {
    size: 15,
    align: 'left',
    watch: true,
  };
  currLength: any = 1;
  dataLength: any = 1;
  currlatlag: any;
  mapview: any = L.tileLayer('http://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', { maxZoom: 19, subdomains: ['mt0', 'mt1', 'mt2', 'mt3'] });
  terrainview: any = L.tileLayer('http://{s}.google.com/vt/lyrs=p&x={x}&y={y}&z={z}', { maxZoom: 19, subdomains: ['mt0', 'mt1', 'mt2', 'mt3'] });
  satelliteview: any = L.tileLayer('http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', { maxZoom: 19, subdomains: ['mt0', 'mt1', 'mt2', 'mt3'] });
  playbackData: any;
  vechicalId: any;
  // tripid: any;
  regNo: any;
  // showback: boolean = false;
  // tripback: boolean = false;
  transportCategoryName: any;
  vehicalDetails: any;
  daystart: any;
  dayend: any;
  PlayBackDetails = signal<PlayBackDetails | null>(null);
  totalDistance: number | undefined;
  duration: any;
  startTs: string | undefined;
  endTs: string | undefined;
  avgSpeed: number | undefined;
  ispause = true;
  fastforwardInterval = 1;
  isTrackingStarted = false;
  isPlay: boolean | undefined;
  intervalId: any;
  intervalIndex = 0;
  prevmarker: any;
  prevLatLng: any[] = [];
  trackingList: any[] = [];
  rtrackList: any[] = [];
  polylines: any[] = [];
  devicetime: any;
  seqGroup: any;
  indexChanged: boolean = false;
  recentervalue: any;
  isTampered: string | undefined;
  signalStrength: any;
  timelineEvents: any[] = [];
  odometer = 0.0;
  startodometer: any;
  gaugeValue: any;
  movingDuration: number = 1000;
  geojson: any;
  arrowHead: any;
  data: any[] = [];
  vehicleId: number | undefined;
  trackingId: any;
  markers: any[] = [];
  trackingInfo: any = {};
  allTrackingList: any;
  tripsEvents: any[] = [];
  trips: any[] = [];
  timelineEventsResponse: any[] = [];
  tripEventsResponse: any[] = [];
  timelineEventsFromApi: any[] = [];
  selectedTrip: any = 0;
  noTripsButDataAvailable: boolean | undefined;
  durationObj: { days: number; hours: number; minutes: number; seconds: number; milliseconds: number; } | undefined;
  totalRunTimeList: any[] = [];
  totalIdleTimeList: any[] = [];
  totalStoppedTimeList: any[] = [];
  totalRunTime: any;
  totalIdleTime: any;
  totalStoppedTime: any;
  geoJsonResult: any[] = [];
  maxSpeed: number = 0;
  coordinate: any;
  PlayOrtrip: any;
  bgColor: any = '#AEAEAE';
  thresholdConfig = {
    '0': { color: 'green' },
    '60': { color: 'orange' },
    '100': { color: 'red' }
  };
  startIcon = L.icon({
    iconUrl: '../../assets/images/status/start.png', iconSize: [40, 40],
  });
  endIcon = L.icon({
    iconUrl: '../../assets/images/status/end.png', iconSize: [40, 40],
  });
  stopageIcon = L.icon({
    iconUrl: '../../assets/images/status/bluepin.png', iconSize: [40, 40],
  });



  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.playbackData = {
        vehicleId: params['vId'],
        regNo: params['regNo'],
        transportCategoryName: params['transportCategoryName'],
        startdate: params['sts'],
        endDate: params['ets'],
        showback: params['sb'],
        // tripId: params['tId'],
        // tripback: params['td'],
      };
      console.log(this.playbackData);
    });

    var PlayorTrip = this.route.snapshot.paramMap.get('PlayorTrip');
    if (PlayorTrip != null && PlayorTrip != '') {
      this.PlayOrtrip = PlayorTrip;
    }

    //byme
    // const id = this.route.snapshot.paramMap.get('id');
    // const vehicleId = parseInt(id || '0', 10);
    // this.vechicalId = vehicleId;

    this.vechicalId = this.playbackData.vehicleId;
    // this.showback = this.playbackData.showback;
    // this.tripback = this.playbackData.tripback;
    // this.tripid = this.playbackData.tripId;

    this.regNo = this.playbackData.regNo;
    this.transportCategoryName = this.playbackData.transportCategoryName;
    const now = new Date();
    if (this.playbackData.startdate != null && this.playbackData.startdate != "Invalid date") {
      const playbackStartDate = new Date(this.playbackData.startdate);
      if (!isNaN(playbackStartDate.getTime())) {
        const dayStart = new Date(
          playbackStartDate.getFullYear(),
          playbackStartDate.getMonth(),
          playbackStartDate.getDate(),
          0, 0, 0
        );
        const dayStartISOString = new Date(
          dayStart.getTime() - (dayStart.getTimezoneOffset() * 60000)
        ).toISOString();
        // this.daystart = dayStartISOString;
        this.daystart = this.playbackData.startdate;

      }
    }
    else {
      this.daystart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();
    }
    if (this.playbackData.endDate != null && this.playbackData.endDate != "Invalid date") {
      const playbackEndDate = new Date(this.playbackData.endDate);
      if (!isNaN(playbackEndDate.getTime())) {
        const dayend = new Date(
          playbackEndDate.getFullYear(),
          playbackEndDate.getMonth(),
          playbackEndDate.getDate(),
          23, 59, 59
        );
        const dayStartISOString = new Date(
          dayend.getTime() - (dayend.getTimezoneOffset() * 60000)
        ).toISOString();
        // this.dayend = dayStartISOString;
        this.dayend = this.playbackData.endDate;

      }
    }
    else {
      this.dayend = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999).toISOString();
    }
    this.recentervalue = null;
    this.loadMap();
    this.getPlayBackDetails();
  }

  onCancel() {
    this.closed.emit(true)
  }

  recenter() {
    if (this.map)
      this.map.panTo(this.currlatlag);
  }

  back() {
    if (this.PlayOrtrip == "trip") {
      this.router.navigate(['/mapview/trip']);
    } else {
      this.router.navigate(['/mapview/live']);
    }
  }
  refresh() {
    this.markers = [];
    // this.getPlayBackDetails();
    location.reload();
  }
  loadMap() {
    const center = [13.0827, 80.2707];
    const baseMaps = {
      'Map': this.mapview,
      'Terrain': this.terrainview,
      'Satellite': this.satelliteview,
    };
    this.map = L.map('mapId', {
      fullscreenControl: {
        pseudoFullscreen: false,
        position: 'topleft',
      },
    }, this.mapcontainer.nativeElement, { minZoom: 1, tilt: true }).setView(center, 15, { heading: 100.0, tilt: 10.0 });
    L.control.layers(baseMaps).addTo(this.map).setPosition('topright');
    this.mapview.addTo(this.map);

  }



  play() {
    if (this.ispause && !this.isTrackingStarted) {
      this.start();
      this.isPlay = true;
    } else {
      this.ispause = false;
      this.run(this.rtrackList);
    }
  }

  pause() {
    this.ispause = true;
  }

  clear() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  stop() {
    this.clear();
    // this.onScroll(0);
    this.fastforwardInterval = 1;
    this.intervalIndex = 0;
    this.ispause = true;
    this.isPlay = false;
    this.isTrackingStarted = false;
    if (this.prevmarker) { this.map.removeLayer(this.prevmarker); }
    this.removeAllLines();
    this.prevLatLng = [];
  }

  fastforward() {
    if (!this.ispause && (this.trackingList && this.trackingList.length > 5)) {
      this.fastforwardInterval = (this.fastforwardInterval < 5) ? this.fastforwardInterval + 1 : 1;
    }
  }

  start() {
    this.clear();
    this.polylines = []
    if (this.trackingList && this.trackingList.length > 5) {
      this.ispause = false;
      this.devicetime = this.trackingList[0].gpsDeviceTs
      this.rtrackList = [];
      const runningTrackingList = this.trackingList
      this.rtrackList = runningTrackingList;
      this.run(runningTrackingList);
    }
  }

  removeAllLines() {
    if (this.polylines) { this.polylines.forEach(m => { this.map.removeLayer(m); }); this.polylines = []; }
    if (this.seqGroup) { this.map.removeLayer(this.seqGroup); }
  }


  backward() {
    this.timelineEvents = []
    if (this.geojson) {
      this.map.removeLayer(this.geojson);
    }
    if (this.arrowHead) {
      this.map.removeLayer(this.arrowHead)
    }
    const index = this.data.map(e => {
      return e.id;
    }).indexOf(this.vehicleId);
    if (index > 0) {
      const nextIndex = index - 1;
      if (!this.data[nextIndex] || this.data[nextIndex] === undefined) {
        // this.alertService.info('This is the last record');
      } else {
        this.trackingId = this.data[nextIndex].trackingId;
        this.vehicleId = this.data[nextIndex].id;
        this.regNo = this.data[nextIndex].regNo;
        this.getPlayBackDetails();
        this.currLength = nextIndex + 1;
      }
    } else {
      // this.alertService.info('This is the first record');
    }
  }

  forward() {
    this.timelineEvents = []
    if (this.geojson) {
      this.map.removeLayer(this.geojson);
    }
    if (this.arrowHead) {
      this.map.removeLayer(this.arrowHead)
    }
    const index = this.data.map(e => {
      return e.id;
    }).indexOf(this.vehicleId);
    if (index < this.data.length) {
      const nextIndex = index + 1;
      if (this.data[nextIndex] === undefined) {
        // this.alertService.info('This is the last record');
      } else {
        this.vehicleId = this.data[nextIndex].id;
        this.regNo = this.data[nextIndex].regNo;
        this.getPlayBackDetails();
        this.currLength = nextIndex + 1;
      }
    }
  }


  run(runningTrackingList: string | any[]) {
    setTimeout(() => {
      this.isTrackingStarted = true;
      if (!this.ispause) {
        if (this.indexChanged) {
          this.indexChanged = false;
        }

        if (this.intervalIndex < runningTrackingList.length) {
          this.recentervalue = runningTrackingList[this.intervalIndex];
          const currentTs = this.recentervalue.gpsDeviceTs
          const prevTs = this.devicetime;
          this.isTampered = this.recentervalue.isTampered ? '../../../../assets/icons/tampered-red.png' : '../../../../assets/icons/tampered-gray.png'

          const signalStrengthPercent = this.recentervalue.signalStrength * 100 / 31
          this.signalStrength = this.getSignalStrengthImage(signalStrengthPercent);

          this.timelineEvents.every((event, index) => {
            const currentEventMilliseconds = event.timeInMilliseconds
            const prevMarkerMilliseconds = moment(prevTs).valueOf()

            if (currentEventMilliseconds >= prevMarkerMilliseconds) {
              const selectedIndex = index === 0 ? index : index - 1;
              this.onScroll(selectedIndex);
              return false;
            }
            return true;
          })
          this.createMovingMarker(runningTrackingList[this.intervalIndex]);
          this.intervalIndex += 1;

          if (this.intervalIndex < runningTrackingList.length) {
            this.odometer = runningTrackingList[this.intervalIndex].odometer - this.startodometer;
            this.devicetime = runningTrackingList[this.intervalIndex].gpsDeviceTs;
            this.gaugeValue = runningTrackingList[this.intervalIndex].speed;
          } else {
            this.onScroll(this.timelineEvents.length - 1)
            this.odometer = runningTrackingList[runningTrackingList.length - 1].odometer - this.startodometer;
            this.devicetime = runningTrackingList[runningTrackingList.length - 1].gpsDeviceTs;
            this.gaugeValue = runningTrackingList[runningTrackingList.length - 1].speed;
          }
          this.run(runningTrackingList);
        } else {
          this.stop();
        }
      }

    }, 1000 / this.fastforwardInterval);
  }

  getSignalStrengthImage(strength: number) {
    let image = '../../../../assets/images/signal/';
    image += (strength <= 25) ? 'lowstrength.png'
      : (strength > 25 && strength <= 50) ? 'belowavgstrength.png'
        : (strength > 50 && strength <= 75) ? 'aboveavgstrength.png'
          : (strength > 75 && strength <= 100) ? 'fullstrength.png'
            : 'lowstrength.png';
    return image;
  };

  onScroll(scrollIndex: number) {
    console.log(scrollIndex)
    this.scrollListRef.forEach((element, index) => {
      if (index === scrollIndex) {
        element.nativeElement.scrollIntoView({ behavior: 'smooth' });
      }
    })
  }

  createMovingMarker(vehicle: { gpsMoveStatus: number; latitude: any; longitude: any; }) {
    this.movingDuration = 1000 / this.fastforwardInterval;
    if (this.prevmarker) {
      this.map.removeLayer(this.prevmarker);
    }
    const color = (vehicle.gpsMoveStatus === 1) ? 'green' : (vehicle.gpsMoveStatus === 2) ? 'orange' : 'red';
    const markerIcon = L.icon.pulse({ iconSize: [10, 10], color, fillColor: color });

    try {
      const currentlatlngs = [];
      const latlngs = [];
      if (this.prevLatLng && this.prevLatLng.length > 0) {
        latlngs.push(this.prevLatLng);
        currentlatlngs.push({ lat: this.prevLatLng[0], lng: this.prevLatLng[1] });
      }
      latlngs.push([vehicle.latitude, vehicle.longitude]);
      currentlatlngs.push({ lat: vehicle.latitude, lng: vehicle.longitude });

      const marker = L.Marker.movingMarker(latlngs, [this.movingDuration], { icon: markerIcon })
        .addTo(this.map);
      marker.start();
      marker.on('end', () => { });
      this.prevLatLng = [vehicle.latitude, vehicle.longitude];
      if (currentlatlngs.length > 1) {

      }
      this.map.panTo(new L.LatLng(vehicle.latitude, vehicle.longitude));
      this.prevmarker = marker;

    } catch (e) {
      console.log(e);
    }
  }

  async getPlayBackDetails() {
    try {
      this.removeAllMarkers();
      this.removeAllLines();
      // let payload: playbackPayload = {
      //   vehicleId: this.vechicalId,
      //   start: this.daystart,
      //   end: this.dayend
      // }

      // var res: PlayBackDetails;

      // if (this.PlayOrtrip == "trip") {
      //   res = await this.playbackService.getTripPlayBackDetails(this.vechicalId, this.daystart, this.dayend); // Fetch data from the service
      // }
      // else {
      //   res = await this.playbackService.getPlayBackDetails(this.vechicalId, this.daystart, this.dayend); // Fetch data from the service
      // }
      const res: PlayBackDetails = await this.playbackService.getPlayBackDetails(this.vechicalId, this.daystart, this.dayend); // Fetch data from the service
      console.log('API Response:', res);
      this.PlayBackDetails.set(res); // Update signal with the response data
      console.log('Signal Value:', this.PlayBackDetails());

      if (res.trackingData.length > 0) {
        res.trackingData = _.orderBy(this.PlayBackDetails()?.trackingData, ["gpsDeviceTs"], "asc");
      }
      this.trackingList = [];
      this.getDistance(res.trackingData)
      this.trackingInfo = res.trackingData;
      this.allTrackingList = this.trackingInfo;
      this.parseData()

    } catch (error) {
      console.error('Error fetching vehicle statuses:', error);
    }
  }

  removeAllMarkers() {
    // this.prevmarker;
    // this.markers;
    if (this.prevmarker) { this.map.removeLayer(this.prevmarker); }
    if (this.markers) { this.markers.forEach(m => { this.map.removeLayer(m); }); }
    this.markers = [];
  }


  getDistance(trackingList: string | any[]) {
    let startCoordinate: any = {};
    let endCoordinate: any = {};
    let odometer = 0;
    for (let index = 0; index < trackingList.length; index++) {
      const element = trackingList[index];
      if (index !== 0) {
        startCoordinate = { lat: trackingList[index].latitude, lon: trackingList[index].longitude };
        odometer += this.getDistanceBetweenTwoPoints(startCoordinate, endCoordinate);
        endCoordinate = { lat: trackingList[index].latitude, lon: trackingList[index].longitude };
        trackingList[index].odometer = odometer;
      }
      else {
        endCoordinate = { lat: trackingList[index].latitude, lon: trackingList[index].longitude };
        odometer = 0;
        trackingList[index].odometer = 0;
      }
    }
    return trackingList;
  }

  parseData() {
    this.trips = []
    let trip: any = { data: [] }
    let tripStart = false;
    let startOdo;

    this.extractTimelineEvents(this.trackingInfo)
    this.sortTripEventsResponse(this.timelineEventsResponse)
    this.parseTripEvents(this.tripEventsResponse)
    this.timelineEventsFromApi = this.tripsEvents[this.selectedTrip];
    this.tripsEvents.forEach((tripEvents, index) => {
      trip.distance = 0;
      let ignitionOnTime = tripEvents[0].startTime
      let ignitinoOffTime = tripEvents[tripEvents.length - 1].startTime
      this.trackingInfo.every((data: { gpsDeviceTs: any; odometer: any; gpsMoveStatus: number; distanceKms: any; }, index: any) => {
        if (ignitionOnTime === data.gpsDeviceTs) {
          tripStart = true;
          startOdo = data.odometer;
        }
        if (tripStart) {
          trip.data.push(data)
          if (data.gpsMoveStatus === 1) {
            trip.distance += data.distanceKms
          }
        }

        if (ignitinoOffTime === data.gpsDeviceTs) {
          trip.startTs = moment(ignitionOnTime).format('hh : mm A');
          if (+trip.distance.toFixed(1) > 0) {
            this.trips.push(trip)
          }

          trip = { data: [] }
          tripStart = false;
          return false;
        }
        return true;
      })

    })
    if (this.trips.length) {
      this.parseInfoFromTrackingData(this.trips[this.selectedTrip].data)
    } else {
      this.noTripsButDataAvailable = true;
      this.parseInfoFromTrackingData(this.trackingInfo)
    }
  }

  extractTimelineEvents(trackingData: any[]) {
    this.timelineEventsResponse = [];
    let vehicleId = 0;
    let regNo = "";
    let speedViolation = false;
    let speedViolationStart: { gpsDeviceTs: any; latitude: any; longitude: any; };
    let speedViolationEnd;

    let currentEvent;

    const setCurrentEvent = (data: { gpsDeviceTs: any; latitude: any; longitude: any; }, eventType: number) => {
      currentEvent = {
        "eventType": eventType,
        "endLongitude": 0,
        "vehicleId": vehicleId,
        "regNo": regNo,
        "startTime": data.gpsDeviceTs,
        "endTime": null,
        "startLatitude": data.latitude,
        "endLatitude": 0,
        "startLongitude": data.longitude,
      }
      this.timelineEventsResponse.push(currentEvent)
    }

    trackingData.forEach((data, index) => {
      if (index === 0) {
        if (data.isIgnitionOn) {
          setCurrentEvent(data, 11)
        }
      } else {
        if (trackingData[index - 1].isIgnitionOn !== data.isIgnitionOn) {
          if (data.isIgnitionOn) {
            setCurrentEvent(data, 11)
          } else {
            setCurrentEvent(data, 12)

          }
        }
      }

      if (data.speed > 60) {
        speedViolation = true
        speedViolationStart = data
      }
      if (speedViolation && (data.speed <= 60 || !trackingData[index + 1])) {
        speedViolation = false
        speedViolationEnd = data
        let speedViolationEvent = {
          eventType: 2,
          vehicleId: vehicleId,
          regNo: regNo,
          startTime: speedViolationStart.gpsDeviceTs,
          endTime: speedViolationEnd.gpsDeviceTs,
          startLatitude: speedViolationStart.latitude,
          endLatitude: speedViolationEnd.latitude,
          startLongitude: speedViolationStart.longitude,
          endLongitude: speedViolationEnd.longitude,
        }
        this.timelineEventsResponse.push(speedViolationEvent)
      }
    });
  }

  sortTripEventsResponse(response: any[]) {
    response.forEach(event => {
      event.startTsInMilliseconds = moment(event.startTime).valueOf()
    })
    this.tripEventsResponse = _.orderBy(response, ['startTsInMilliseconds'], ['asc'])
  }

  parseTripEvents(response: any[]) {
    this.tripsEvents = []
    let tripEvents: any[] = []
    response.forEach((event, index) => {
      if (event.eventType === 12 || index === response.length - 1) {
        tripEvents.push(event)
        this.tripsEvents.push(tripEvents);
        tripEvents = [];
      } else {
        tripEvents.push(event)
      }
    })

    if (this.tripsEvents[0]?.[0].eventType !== 11) {
      this.tripsEvents.shift()
    }
  }

  parseInfoFromTrackingData(trackingData: any[]) {
    this.removeAllMarkers();
    const max: any = _.maxBy(trackingData, 'speed');
    this.trackingInfo.startTs = trackingData[0].gpsDeviceTs
    this.trackingInfo.endTs = trackingData[trackingData.length - 1].gpsDeviceTs;
    const startTs = this.trackingInfo.startTs;
    const endTs = this.trackingInfo.endTs;
    const startOdometer = trackingData[0].odometer
    const endOdometer = trackingData[trackingData.length - 1].odometer
    this.duration = moment(startTs).valueOf() - moment(endTs).valueOf()
    this.durationObj = this.millisecondsToDaysHoursMinutesSeconds(this.duration)

    this.totalRunTimeList = []
    this.totalIdleTimeList = []
    this.totalStoppedTimeList = []

    let selectedGpsMoveStatus = trackingData[0].gpsMoveStatus;
    let startIndex = 0;
    for (let index = 1; index < trackingData.length; index++) {
      const currentData = trackingData[index]
      const prevData = trackingData[index - 1]
      if (currentData.gpsMoveStatus !== prevData.gpsMoveStatus || index === trackingData.length - 1) {

        if (selectedGpsMoveStatus === 0) {
          let endTs = moment(currentData.gpsDeviceTs)
          let startTs = moment(trackingData[startIndex].gpsDeviceTs)

          let diff = moment.duration(endTs.diff(startTs)).asMilliseconds()
          this.totalStoppedTimeList.push(diff)
          startIndex = index;
          selectedGpsMoveStatus = currentData.gpsMoveStatus;


        } else if (selectedGpsMoveStatus === 1) {
          let endTs = moment(currentData.gpsDeviceTs)
          let startTs = moment(trackingData[startIndex].gpsDeviceTs)
          let diff = moment.duration(endTs.diff(startTs)).asMilliseconds()

          this.totalRunTimeList.push(diff)
          startIndex = index;
          selectedGpsMoveStatus = currentData.gpsMoveStatus;

        } else if (selectedGpsMoveStatus === 2) {
          let endTs = moment(currentData.gpsDeviceTs)
          let startTs = moment(trackingData[startIndex].gpsDeviceTs)
          let diff = moment.duration(endTs.diff(startTs)).asMilliseconds()

          this.totalIdleTimeList.push(diff)
          startIndex = index;
          selectedGpsMoveStatus = currentData.gpsMoveStatus;

        }
      }
    }


    this.totalIdleTimeList.forEach((duration, index) => {
      if (index === 0) {
        this.totalIdleTime = duration;
      } else {
        this.totalIdleTime += duration
      }
    })
    this.totalRunTimeList.forEach((duration, index) => {
      if (index === 0) {
        this.totalRunTime = duration;
      } else {
        this.totalRunTime += duration
      }
    })
    this.totalStoppedTimeList.forEach((duration, index) => {
      if (index === 0) {
        this.totalStoppedTime = duration;
      } else {
        this.totalStoppedTime += duration;
      }
    })

    if (!this.totalIdleTimeList.length) {
      // this.totalIdleTime.set(0);
      this.totalIdleTime = 0;

    }
    if (!this.totalRunTimeList.length) {
      // this.totalRunTime.set(0);
      this.totalRunTime = 0;

    }
    if (!this.totalStoppedTimeList.length) {
      // this.totalStoppedTime.set(0);
      this.totalStoppedTime = 0;

    }

    // const runtimeValue = this.totalRunTime ? this.totalRunTime : 0; 
    // const runTimeInHours = Math.abs(runtimeValue / 1000 / 60 / 60).toFixed(2);
    const runtimeValue = typeof this.totalRunTime === 'function' ? this.totalRunTime() : this.totalRunTime || 0;
    const runTimeInHours = Math.abs(Number(runtimeValue) / 1000 / 60 / 60).toFixed(2);



    this.totalIdleTime = this.millisecondsToDaysHoursMinutesSeconds(this.totalIdleTime ? this.totalIdleTime : 0);
    this.totalRunTime = this.millisecondsToDaysHoursMinutesSeconds(this.totalRunTime ? this.totalRunTime : 0);
    this.totalStoppedTime = this.millisecondsToDaysHoursMinutesSeconds(this.totalStoppedTime ? this.totalStoppedTime : 0);


    this.trackingInfo.startTs = trackingData[0].gpsDeviceTs
    this.trackingInfo.endTs = trackingData[trackingData.length - 1].gpsDeviceTs;
    this.trackingInfo.distanceTraveled = 0

    trackingData.forEach(data => {
      if (data.gpsMoveStatus === 1) {
        this.trackingInfo.distanceTraveled += data.distanceKms
      }
    })


    this.trackingInfo.avgSpeed = (+this.trackingInfo.distanceTraveled / +runTimeInHours)
    this.trackingInfo.avgSpeed = (this.trackingInfo.avgSpeed ? this.trackingInfo.avgSpeed : 0).toFixed(0)
    if (+runTimeInHours === 0) {
      this.trackingInfo.avgSpeed = 0;
    }
    this.maxSpeed = 0;
    trackingData.forEach(data => {
      if (data.speed > this.maxSpeed) {
        this.maxSpeed = data.speed
      }
    })

    if (trackingData && trackingData?.length > 1) {
      this.trackingList = trackingData;
      const end = this.trackingList?.length - 1;
      this.startodometer = this.trackingList[0]?.odometer;
      this.odometer = this.trackingList[0]?.odometer - this.startodometer;
      this.gaugeValue = this.trackingList[0]?.speed;
      this.devicetime = this.trackingList[0]?.gpsDeviceTs;
      this.recentervalue = this.trackingList[0];
      this.createMarker(this.trackingList[0]?.latitude, this.trackingList[0]?.longitude, this.startIcon).addTo(this.map);
      this.createMarker(this.trackingList[end]?.latitude, this.trackingList[end]?.longitude, this.endIcon).addTo(this.map);
      this.fitbounds(this.trackingList);
      this.createRouteDetails(this.trackingList);


      const stoppageList = _.filter(this.trackingList, item => (item.gpsMoveStatus !== 1 && item.durationTs >= environment.StoppageIntervalSecs));
      if (stoppageList && stoppageList.length > 0) {
        stoppageList.forEach(element => {
          if (element.duration) {
            element.dday = element.duration.item1 ? element.duration.item1 : 0;
            element.dhrs = element.duration.item2 ? element.duration.item2 : 0;
            element.dmin = element.duration.item3 ? element.duration.item3 : 0;

            const marker = this.createMarker(element.latitude, element.longitude, this.stopageIcon);
            marker.bindPopup(this.bindmarkerpopup(element));
            this.markers.push(marker);
            marker.addTo(this.map)
          }
        });

      }
    }
  }

  createMarker(latitude: any, longitude: any, icon: any) {
    const marker = L.marker([latitude, longitude], { icon });
    this.markers.push(marker);
    return marker;
  }

  fitbounds(data: string | any[]) {
    const latLngs = [];
    for (let i = 0; i < data.length; i += 20) {
      latLngs.push([data[i].latitude, data[i].longitude]);
    }
    if (latLngs.length > 0) {
      this.map.fitBounds(latLngs);
    }
  }

  createRouteDetails(data: string | any[]) {
    this.geoJsonResult = [];
    let coordinates = [];
    let status = data[0]?.gpsMoveStatus;
    let prevType = data[0].speed >= 30 ? 3 : data[0].speed >= 15 ? 2 : 1;
    for (let index = 0; index < data.length; index++) {
      const trackingdetail = data[index];
      status = trackingdetail.gpsMoveStatus;

      if (index === data.length - 1) {
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
        this.geoJsonResult.push(this.generateGoeJson(coordinates, prevType, trackingdetail.gpsDeviceTs));
        status = trackingdetail.gpsMoveStatus;
        prevType = trackingdetail.speed >= 30 ? 3 : trackingdetail.speed >= 15 ? 2 : 1;

      } else if (trackingdetail.speed >= 30 && prevType === 3) {
        prevType = 3;
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
      } else if (trackingdetail.speed >= 15 && prevType === 2) {
        prevType = 2;
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
      } else if (trackingdetail.speed <= 15 && prevType === 1) {
        prevType = 1;
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
      } else {
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
        if (coordinates.length >= 1) {
          this.geoJsonResult.push(this.generateGoeJson(coordinates, prevType, trackingdetail.gpsDeviceTs));
        }
        coordinates = [];
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
        status = trackingdetail.gpsMoveStatus;
        prevType = trackingdetail.speed >= 30 ? 3 : trackingdetail.speed >= 15 ? 2 : 1;
      }
    }
    function onEachFeature(feature: any, layer: any) {
      if (feature.properties && feature.properties.time) {
      }
    }

    this.geojson = L.geoJSON(this.geoJsonResult, {
      onEachFeature,
      style: function (feature: any) {
        switch (feature.properties.party) {
          case 1: return { color: '#008000' };
          case 2: return { color: '#FF541A' };
          case 3: return { color: '#B22222' };
          default: return { color: '#808080' };
        }
      }
    })

    var lines = this.geoJsonResult
      .filter(function (feature) {
        return feature.geometry.type == "LineString"
      })
      .map(function (feature) {
        var coordinates = feature.geometry.coordinates;
        coordinates.forEach(function (coordinate: any) { coordinate.reverse(); })
        return coordinates
      })
    this.arrowHead = L.polylineDecorator(lines, {
      patterns: [{
        offset: 0,
        repeat: 40,
        symbol: L.Symbol.arrowHead({
          pixelSize: 10,
          pathOptions: { fillOpacity: 1, weight: 0, color: "#1C1C53" }
        })
      }]
    })
      .addTo(this.map);

    this.geojson.addTo(this.map);
  }


  bindmarkerpopup(vehicle: { startTs: moment.MomentInput; endTs: moment.MomentInput; dday: string; dhrs: string; dmin: string; }) {
    const popupContent = '<form class="mapcardicon popup-form homeCSCs"><div><table><tr style="padding-bottom: 15px;">' +
      '<td colspan="6">' + this.regNo + '</td></tr>' +
      '<tr style="border-top: 1px solid #3F67A7;margin-bottom:10px"><td>Stoppage from ;&nbsp;</td><td><b>&nbsp:&nbsp' + moment(vehicle.startTs).format('YYYY-MM-DD HH:mm:ss') + '</b></td></tr>' +
      '<tr ><td>Stoppage to &nbsp;&nbsp;</td><td><b>&nbsp:&nbsp' + moment(vehicle.endTs).format('YYYY-MM-DD HH:mm:ss') + '</b></td></tr>' +
      '<tr><td>Stoppage Duration&nbsp;&nbsp;</td><td>&nbsp:&nbsp' + vehicle.dday + 'Days&nbsp;&nbsp' + vehicle.dhrs + 'Hrs&nbsp;&nbsp' + vehicle.dmin + 'mins' + '</td></tr>' +
      '</table></div></form>';
    return popupContent;
  }

  generateGoeJson(coordinates: any[][], status: number, gpsDeviceTs: any) {
    return {
      type: 'Feature',
      properties: {
        party: status,
        time: gpsDeviceTs
      },
      geometry: {
        type: 'LineString',
        coordinates,
      }
    };
  }
  millisecondsToDaysHoursMinutesSeconds(milliseconds: any) {
    const diffInMilliseconds = Math.abs(milliseconds)

    const millsecondsValue = diffInMilliseconds % 1000
    const diffInSeconds = Math.trunc(diffInMilliseconds / 1000)
    const secondsValue = diffInSeconds % 60
    const diffInMinutes = Math.trunc(diffInSeconds / 60)
    const minutesValue = diffInMinutes % 60
    const diffInHours = Math.trunc(diffInMinutes / 60)
    const hoursValue = diffInHours % 24
    const diffInDays = Math.trunc(diffInHours / 24)
    return {
      days: diffInDays,
      hours: hoursValue,
      minutes: minutesValue,
      seconds: secondsValue,
      milliseconds: millsecondsValue
    }
  }

  getDistanceBetweenTwoPoints(cord1: Coordinate, cord2: Coordinate) {
    if (cord1.lat == cord2.lat && cord1.lon == cord2.lon) {
      return 0;
    }

    const radlat1 = (Math.PI * cord1.lat) / 180;
    const radlat2 = (Math.PI * cord2.lat) / 180;

    const theta = cord1.lon - cord2.lon;
    const radtheta = (Math.PI * theta) / 180;

    let dist =
      Math.sin(radlat1) * Math.sin(radlat2) +
      Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);

    if (dist > 1) {
      dist = 1;
    }

    dist = Math.acos(dist);
    dist = (dist * 180) / Math.PI;
    dist = dist * 60 * 1.1515;
    dist = dist * 1.609344; //convert miles to km

    return dist;
  }
}
