import { inject, Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';


@Injectable({
  providedIn: 'root'
})

export class AlertService {
  toastr = inject(ToastrService)
  _location = inject(Location)

  constructor() {
    this.toastr.toastrConfig.enableHtml = true;
  }

  // success(message: string, title: string = '', keepAfterNavigationChange = false) {
  //   this.toastr.success(message, title, { closeButton: false,positionClass: 'toast-top-right' });
  // }

  // error(message: string, title: string = '', keepAfterNavigationChange = false) {
  //   this.toastr.error(message, title,{ closeButton: false,positionClass: 'toast-top-right' });
  // }

  // warning(message: string, title: string = '', keepAfterNavigationChange = false) {
  //   this.toastr.warning(message, title,{ closeButton: false,positionClass: 'toast-top-right' });
  // }

  // info(message: string, title: string = '', keepAfterNavigationChange = false) {
  //   this.toastr.info(message, title,{ closeButton: false,positionClass: 'toast-top-right' });
  // }

  // async alert(message: string, title: string) {
  //   this.toastr.info(message, title, { closeButton: true, positionClass: 'toast-top-right' });
  // }

  // result(result: any, isSuccessGoBack = false, message: any = null) {
  //   if (result && result.isSuccess) {
  //     if (message) {
  //       this.success(message);
  //     } else {
  //       this.success('Requested information updated successfully');
  //     }
  //     if (isSuccessGoBack) {
  //       this._location.back();
  //     }
  //   } else {
  //     if (result && result.failures) {
  //       this.error(result.failures.toString());
  //     }
  //   }
  // }


  // allocateresult(result: any, isSuccessGoBack = false, message: any = null) {
  //   if (result && result.isSuccess) {
  //     if (message) {
  //       this.success(message);
  //     } else {
  //       this.success('Requested information updated successfully');
  //     }
  //     if (isSuccessGoBack) {
  //       // this._location.back();
  //     }
  //   } else {
  //     if (result && result.failures) {
  //       this.error(result.failures.toString());
  //     }
  //   }
  // }


  success(message: string, title:string = '', keepAfterNavigationChange = false) {
    this.toastr.success(message, title);
  }

  error(message: string, title:string = '', keepAfterNavigationChange = false) {
    this.toastr.error(message, title);
  }

  warning(message: string, title:string = '', keepAfterNavigationChange = false) {
    this.toastr.warning(message, title);
  }

  info(message: string, title:string = '', keepAfterNavigationChange = false) {
    this.toastr.info(message, title);
  }

  async alert(message: string, title: string) {
    this.toastr.info(message, title, { closeButton: true, positionClass: 'toast-top-center' });
  }

  result(result: any, isSuccessGoBack = false, message: any = null) {
    if (result && result.isSuccess) {
      if (message) {
        this.success(message);
      } else {
        this.success('Requested information updated successfully');
      }
      if (isSuccessGoBack) {
        this._location.back();
      }
    } else {
      if (result && result.failures) {
        this.error(result.failures.toString());
      }
    }
  }


  allocateresult(result: any, isSuccessGoBack = false, message: any = null) {
    if (result && result.isSuccess) {
      if (message) {
        this.success(message);
      } else {
        this.success('Requested information updated successfully');
      }
      if (isSuccessGoBack) {
        // this._location.back();
      }
    } else {
      if (result && result.failures) {
        this.error(result.failures.toString());
      }
    }
  }



}

