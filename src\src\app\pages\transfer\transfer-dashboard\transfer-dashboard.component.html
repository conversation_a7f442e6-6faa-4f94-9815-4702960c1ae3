<mat-card>
  <mat-card-title>Transfer Requests</mat-card-title>
  <mat-card-content>
    <button mat-raised-button color="primary" [matMenuTriggerFor]="transferMenu">
      Submit New Request <mat-icon>arrow_drop_down</mat-icon>
    </button>
    <mat-menu #transferMenu="matMenu">
      <button mat-menu-item routerLink="/transfer/within">Transfer Within Region</button>
      <button mat-menu-item routerLink="/transfer/inter">Transfer to Another Region</button>
      <button mat-menu-item routerLink="/transfer/mutual">Mutual Transfer</button>
    </mat-menu>
  </mat-card-content>
</mat-card>

<!-- Sample Request List -->
<mat-card *ngFor="let req of ['TR1001', 'TR1002']">
  <mat-card-title>Request ID: {{ req }}</mat-card-title>
  <mat-card-subtitle>Status: Pending</mat-card-subtitle>
</mat-card>
