import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { RegisterComponent } from './register.component';
import { VendorService } from '../../../../services/x_apis/vendor/vendor.service';
import { Vendors } from '../../../../models/x_models/vendor';

@Component({
  selector: 'app-registers',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatFormFieldModule,
    TranslateModule,
    RegisterComponent],
  providers: [DatePipe],
  templateUrl: './registers.component.html',
  styleUrls: ['./registers.component.scss']
})
export class RegistersComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  vendorService = inject(VendorService)
  vendorResource = resource({ loader: () => this.vendorService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.vendorResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  vendor = signal<Vendors>({
    id: 0,
    name: '',
    regionalName: '',
    authPersonName: '',
    email: '',
    mobileNo: '',
    whatsAppNo: '',
    vendorTypeName: ''
  });

  displayedColumns: string[] = [
    'name',
    // 'regionalName',
    'authPersonName',
    // 'email',
    'mobileNo',
    'whatsAppNo',
    'vendorTypeName',
    'actions'
  ];

  protected readonly AppRoutes = AppRoutes;  // Need clarification

  constructor(private datePipe: DatePipe) {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.vendor.set({
      id: 0,
      name: '',
      regionalName: '',
      authPersonName: '',
      email: '',
      mobileNo: '',
      whatsAppNo: '',
      vendorTypeName: ''
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(data: Vendors) {
    this.vendor.set(data);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(data: Vendors) {
    this.vendor.set(data);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.vendorResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.vendorResource.reload();
    this.search.setValue("");
  }

  async onDelete(vendor: Vendors) {
    await confirmAndDelete(vendor, vendor.name, 'Vendor Register', this.vendorService, () => this.vendorResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
