<div class="card">
    <div class="component1">
      <div class="page-header1">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a (click)="navigateBack()">{{menuService.activeMenu()?.title}}</a>
            </li>
            @if(vendorId() == 0) {
            <li aria-current="page" class="breadcrumb-item active">{{'New' | translate}} {{menuService.activeMenu()?.title}}</li>
            } @else {
            <li aria-current="page" class="breadcrumb-item active"> {{vendor()?.name}}</li>
            }
          </ol>
        </nav>
  
        @if(vendorId() == 0) {
        <h1>{{'New' | translate}} {{menuService.activeMenu()?.title}}</h1>
        } @else {
        <h1>
          {{vendor()?.name}}
  
          @if(mode() === "view") {
          <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
            <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
          </button>
          }
        </h1>
        }
      </div>
  
  
  
      <form [formGroup]="form" (ngSubmit)="onSubmit()">
        <div class="form">

          <div class="field">
            <label for="name" class="required-label">{{'CompanyName' | translate}}</label>
            <mat-form-field>
              <input id="name" formControlName="name" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
                maxlength="100">
              <mat-error>
                @if(form.controls.name.errors?.['required']) {
                {{'CompanyName' | translate}} {{'IsRequired' | translate}}
                }
              </mat-error>
            </mat-form-field>
          </div>
  
          <div class="field">
            <label for="regionalName" class="required-label">{{'CompanyName' | translate}} {{'InTamil' | translate}}</label>
            <mat-form-field>
              <input id="regionalName" formControlName="regionalName" matInput
                (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
              <mat-error>
                @if(form.controls.regionalName.errors?.['required']) {
                {{'CompanyName' | translate}} {{'InTamil' | translate}} {{'IsRequired' | translate}}
                }
              </mat-error>
            </mat-form-field>
          </div>


          <div class="field">
            <label for="authPersonName" class="required-label">{{'AuthorizedPersonName' | translate }}</label>
            <mat-form-field>
              <input id="authPersonName" formControlName="authPersonName" matInput (input)="onInput($event,'/^[a-zA-Z\s]*$/')"
                maxlength="100">
              <mat-error>
                @if(form.controls.authPersonName.errors?.['required']) {
                {{'AuthorizedPersonName' | translate}} {{'IsRequired' | translate}}
                }
              </mat-error>
            </mat-form-field>
          </div>

          <div class="field">
            <label for="email" class="required-label">{{'EmailId' | translate }}</label>
            <mat-form-field>
              <input id="email" formControlName="email" matInput
                maxlength="100">
              <mat-error>
                @if(form.controls.email.errors?.['required']) {
                {{'EmailId' | translate}} {{'IsRequired' | translate}}
                }
                @else if (form.controls.email.errors?.['invalidEmail']) {
                    {{'InvalidFormat' | translate}}
                  }
              </mat-error>
            </mat-form-field>
          </div>

          <div class="field">
            <label for="mobileNo" class="required-label">{{'MobileNo' | translate }}</label>
            <mat-form-field>
              <input id="mobileNo" formControlName="mobileNo" matInput (input)="onInput($event,'/^[0-9]*$/')"
                maxlength="10">
              <mat-error>
                @if(form.controls.mobileNo.errors?.['required']) {
                {{'MobileNo' | translate}} {{'IsRequired' | translate}}
                }
                @else if (form.controls.mobileNo.errors?.['pattern']) {
                    {{'InvalidFormat' | translate}}
                  }
              </mat-error>
            </mat-form-field>
          </div>
  
          <div class="field">
            <label for="whatsAppNo" class="required-label">{{'WhatsappNo' | translate }}</label>
            <mat-form-field>
              <input id="whatsAppNo" formControlName="whatsAppNo" matInput (input)="onInput($event,'/^[0-9]*$/')"
                maxlength="10">
              <mat-error>
                @if(form.controls.whatsAppNo.errors?.['required']) {
                {{'WhatsappNo' | translate}} {{'IsRequired' | translate}}
                }
                @else if (form.controls.whatsAppNo.errors?.['pattern']) {
                    {{'InvalidFormat' | translate}}
                  }
              </mat-error>
            </mat-form-field>
          </div>
  
          <div class="field">
            <label for="vendorType" class="required-label">{{'VendorType' | translate }}</label>
            <mat-form-field appearance="outline">
              <mat-select id="vendorType" formControlName="vendorType">
                <!-- <mat-form-field class="select-search hide-subscript" appearance="outline">
                  <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                  <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
                </mat-form-field> -->
                @for (role of vendorType(); track role) {
                <mat-option [value]="role.key">{{role.value}}</mat-option>
                }
              </mat-select>
              <mat-error>
                @if(form.controls.vendorType.errors?.['required']) {
                {{'VendorType' | translate}} {{'IsRequired' | translate}}
                }
              </mat-error>
            </mat-form-field>
          </div>
  
        </div>
  
        <div class="actions">
          <button type="button" mat-flat-button (click)="navigateBack()" class="btn btn-secondary">{{'Back' |
            translate}}</button>
          @if(editable() === true) {
          @if(mode() === 'add') {
          <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
            translate}}</button>
          }
          <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{vendorId() == 0 ?
            ('Create' | translate) : ('Update' | translate)}}</button>
          }
        </div>
      </form>
    </div>
  </div>