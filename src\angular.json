{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"tncscerp": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/tncscerp", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["node_modules/intl-tel-input/build/css/intlTelInput.css", "src/styles.scss"], "scripts": ["node_modules/intl-tel-input/build/js/intlTelInput.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1000kB", "maximumError": "5MB"}, {"type": "anyComponentStyle", "maximumWarning": "10kB", "maximumError": "40kB"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "tncscerp:build:production"}, "development": {"buildTarget": "tncscerp:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["node_modules/intl-tel-input/build/css/intlTelInput.css", "src/styles.scss"], "scripts": ["node_modules/intl-tel-input/build/js/intlTelInput.js"]}}}}}, "cli": {"analytics": false}}