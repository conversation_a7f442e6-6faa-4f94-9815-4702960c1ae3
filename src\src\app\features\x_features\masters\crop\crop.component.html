<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(cropId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
      {{crop()?.cropName}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

<div class="card component">

  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="form">

      <div class="field">
        <label for="cropName" class="required-label">{{'Crop' | translate }}</label>
        <mat-form-field>
          <input id="cropName" formControlName="cropName" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
            maxlength="100">
          <mat-error>
            @if(form.controls.cropName.errors?.['required']) {
            {{'Crop' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

      <div class="field">
        <label for="seasonId" class="required-label">{{'Season' | translate }}</label>
        <mat-form-field appearance="outline">
          <mat-select id="seasonId" formControlName="seasonId">
            <mat-form-field class="select-search hide-subscript" appearance="outline">
              <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
              <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
            </mat-form-field>
            @for (role of filteredTaluks(); track role) {
            <mat-option [value]="role.key">{{role.value}}</mat-option>
            }
          </mat-select>
          <mat-error>
            @if(form.controls.seasonId.errors?.['required']) {
            {{'Season' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

      <div class="field">
        <label for="fromDate" class="required-label">{{'FromDate' | translate }}</label>
        <mat-form-field>
          <input matInput id="fromDate" formControlName="fromDate" [matDatepicker]="picker1">
          <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
          <mat-datepicker #picker1></mat-datepicker>
          <mat-error>
            @if(form.controls.fromDate.errors?.['required']) {
            {{'FromDate' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

      <div class="field">
        <label for="toDate" class="required-label">{{'ToDate' | translate }}</label>
        <mat-form-field>
          <input matInput id="toDate" formControlName="toDate" [matDatepicker]="picker2" [min]="minToDate()">
          <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
          <mat-datepicker #picker2></mat-datepicker>
          <mat-error>
            @if(form.controls.toDate.errors?.['required']) {
            {{'ToDate' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

    </div>

    <div class="actions">

      @if(editable() === true) {
      @if(mode() === 'add') {
      <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
        translate}}</button>
      }
      <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{cropId() == 0 ?
        ('Create' | translate) : ('Update' | translate)}}</button>
      }
    </div>
  </form>
</div>