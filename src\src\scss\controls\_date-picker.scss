@use '@angular/material' as mat;

.mat-datepicker-content {
    --mat-datepicker-calendar-date-selected-state-text-color: #fff;
    --mat-datepicker-calendar-date-selected-state-background-color: var(--theme-80);
    --mat-datepicker-calendar-date-selected-disabled-state-background-color: color-mix(in srgb, var(--sys-on-surface) 38%, transparent);
    --mat-datepicker-calendar-date-today-selected-state-outline-color: var(--theme-80);
    --mat-datepicker-calendar-date-focus-state-background-color: var(--theme-40);
    --mat-datepicker-calendar-date-hover-state-background-color: var(--theme-40);
    --mat-datepicker-calendar-date-in-range-state-background-color: var(--theme-30);
    --mat-datepicker-calendar-body-label-text-color: var(--grey-100);
    --mat-datepicker-calendar-period-button-text-color: var(--grey-100);
    --mat-datepicker-calendar-period-button-icon-color: var(--grey-100);
    --mat-datepicker-calendar-navigation-button-icon-color: var(--grey-100);
    --mat-datepicker-calendar-header-text-color: var(--grey-100);
    --mat-datepicker-calendar-date-today-outline-color: var(--theme-80);
    --mat-datepicker-calendar-date-today-disabled-state-outline-color: color-mix(in srgb, var(--sys-on-surface) 38%, transparent);
    --mat-datepicker-calendar-date-text-color: var(--grey-100);
    --mat-datepicker-calendar-date-disabled-state-text-color: color-mix(in srgb, var(--sys-on-surface) 38%, transparent);
    --mat-datepicker-calendar-date-preview-state-outline-color: var(--theme-80);
    --mat-datepicker-calendar-container-background-color: var(--theme-05);
    --mat-datepicker-calendar-container-text-color: var(--grey-100);
    //--mat-datepicker-calendar-text-size: 12px;
    --mat-datepicker-calendar-body-label-text-size: 14px;
    --mat-datepicker-calendar-period-button-text-size: 18px;
    --mat-datepicker-calendar-period-button-text-weight: 600;
    --mat-datepicker-calendar-header-text-size: 12px;
    --mat-datepicker-calendar-container-shape: 8px;
    --mat-datepicker-calendar-container-touch-shape: 28px;
    --mat-datepicker-calendar-container-elevation-shadow: 0px 6px 14px -6px rgba(19, 25, 39, 0.12), 0px 10px 32px -4px rgba(19, 25, 39, 0.10);
    --mat-datepicker-calendar-container-touch-elevation-shadow: 0px 6px 14px -6px rgba(19, 25, 39, 0.12), 0px 10px 32px -4px rgba(19, 25, 39, 0.10);

    --mat-icon-button-state-layer-color: var(--theme-80);

}

.mat-calendar-header .mat-calendar-period-button {
  min-width: 100px;
}

.mat-datepicker-content {
    // translate: 0px 14px;
}

.mat-mdc-form-field-type-mat-date-range-input:has(.mat-datepicker-toggle-active) {
    --mdc-outlined-text-field-outline-color: var(--theme-80);
    .mdc-notched-outline {
        box-shadow: 0 0 0 3px var(--theme-30);
        border-radius: 8px;
    }

  .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid  {
    .mdc-notched-outline {
      box-shadow: 0 0 0 3px var(--red-30);
    }
    .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
      .mat-mdc-select-arrow {
        &::after {
          color: var(--red-80);
        }
      }
    }
  }
}


// ============|| date range picker START  ||=================
.app-date-picker {
    @include mat.form-field-overrides((
        //container-height: 28px,
        //container-text-size: 10px,
    ))
}

.calendar-icon.calendar-icon {
  position: absolute;
  color: var(--theme-80);
  width: 15px;
  padding-inline: 12px !important;
  // height: 35px;
}

.mat-calendar-period-button{
    &:hover, &:focus, &:active {
        .mat-mdc-button-persistent-ripple, .mat-mdc-button-persistent-ripple::before  {
            background-color: var(--theme-80);
            opacity: 0.1;
        }

        .mat-ripple-element {
            background-color: var(--theme-80);
            opacity: 0.1;
        }
    }

}


.mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before {
  // border-radius: 50% !important;
}

.mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before {
  // border-radius: 50% !important;
  left: 3px !important;
}

mat-month-view  {
  // width: 40px;
  // height: 40px;
  // padding: 0px;
  // margin-left: 50%;
  // translate: -50% -50%;
  // margin-top: 50%;

  button .mat-calendar-body-cell-content {
    // width: 35px;
    // height: 35px;
    top: 50%;
    translate: -49% -49%;
    left: 50%;
  }

  .mat-calendar-body-in-range::before {
    // border-radius: 50% !important;
    // height: 35px !important;
    // width: 35px !important;
    // top: 0px !important;
    // left: 0px !important;
  }
}

mat-year-view .mat-calendar-body-label {
  display: none;
}

.mat-calendar-controls {
  justify-content: space-between;

  .mat-calendar-period-button {
    // order: 1;
  }

  .mat-calendar-previous-button, .mat-calendar-next-button {
    // border: 2px solid var(--theme-80);
    border-radius: 8px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    //&:before {
    //  content: '';
    //  position: absolute;
    //  width: 100%;
    //  height: 100%;
    //}

    svg {
      // display: none;
    }
  }

  .mat-calendar-previous-button {
    // order: 0;


  }

  .mat-calendar-next-button {
    // order: 2;
  }
}

button.mdc-button.mat-calendar-period-button {
  justify-content: center !important;
  padding-inline: 15px;
  padding-block: 10px;
}

.mat-calendar-spacer {
  // display: none;
}

.mat-datepicker-content .mat-calendar.mat-calendar {
    min-width: 290px;
    height: auto;
}
.mat-calendar-period-button {
    justify-content: flex-start !important;
}
.app-date-picker {
    //width: 227px;
    min-width: 290px;


    .mat-ripple-element {
        background-color: var(--theme-80);
        opacity: 0.1;
    }

    // --mat-icon-button-ripple-color: var(--theme-20);

    .mdc-floating-label--float-above {
        // display: none !important;
    }

    .mat-date-range-input-container {
        //height: 28px;
    }

    .mdc-text-field--outlined .mdc-floating-label {
        left: -0px;
    }

    .mdc-notched-outline--notched .mdc-notched-outline__notch {
        border-top: 1.5px solid var(--mdc-outlined-text-field-outline-color);
    }

    .mat-mdc-notch-piece.mdc-notched-outline__notch label {
        top: 50% !important;
        font-size: 10px;
    }

    .mat-mdc-text-field-wrapper.mdc-text-field.mdc-text-field--outlined {
        //height: 28px;
    }

    .mat-mdc-button-persistent-ripple.mdc-icon-button__ripple {
        display: none;
    }

    .mat-datepicker-toggle button {
        //height: 28px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 32px;
        margin-right: 1px;
        border-radius: 4px 8px 8px 4px;

        svg {
            display: none;
        }
    }
}

.mat-mdc-form-field-type-mat-date-range-input:has(.mat-datepicker-toggle-active) .mdc-notched-outline {
  box-shadow: 0 0 0 0 var(--theme-30);
  border-radius: 8px;
}
// ============|| date range picker END  ||=================
