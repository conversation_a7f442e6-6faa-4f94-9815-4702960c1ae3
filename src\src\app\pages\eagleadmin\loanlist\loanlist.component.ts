import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatOptgroup, MatOption, MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { PayrollService } from '../../../services/m_apis/service/payroll.service';
import { Router } from '@angular/router';
import { MatSpinner } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-loanlist',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    MatTableModule,
    MatIconModule,
    MatSnackBarModule,
    MatDialogModule,
    MatOptgroup,
    MatOption,
    MatSpinner
  ],
  templateUrl: './loanlist.component.html',
  styleUrl: './loanlist.component.scss'
})
export class LoanlistComponent {
   isLoading: boolean = false; 
  
  dataSource: any[] = [];
  mainList: any[] = [];
    displayedColumns: string[] = ['id','matricId', 'employeeRole',  'level', 'basicPay', 'actions'];


  constructor(
    private Service: PayrollService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getpendinglist();
  }








   getpendinglist() {
    this.isLoading = true;

    this.Service.getAllpendinglist().subscribe({
      next: (data) => {
        this.mainList = [];
        console.log('Employee data:', (data as any).responseData);

        this.mainList = (data as any).responseData || [];
        this.dataSource = this.mainList;

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching employee list:', error);
        this.isLoading = false;
        this.dataSource = [];
        this.mainList = [];
        

      }
    });
  }

  refreshData(){
    this.getpendinglist;

  }

}
