import { Component, computed, ElementRef, inject, input, output, resource, signal, viewChild } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { TalukService } from '../../../../services/x_apis/masters/taluk.service';
import { LookUpResponse } from '../../../../models/x_models/lookup';
import { Taluk } from '../../../../models/x_models/masters/taluk';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-taluk',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatFormFieldModule,
    TranslateModule,
    MatToolbarModule,
    MatIconModule
  ],
  templateUrl: './taluk.component.html',
  styleUrl: './taluk.component.scss'
})
export class TalukComponent {
  menuService = inject(MenuService);
  mode = input.required<string>()
  talukId = input.required<any>()
  lookupService = inject(LookupService)
  talukService = inject(TalukService)
  alertService = inject(AlertService)
  inputFormatService = inject(InputformatService)
  closed = output<boolean>()
  fb = inject(FormBuilder)

  editable = signal<boolean>(true)
  taluk = signal<Taluk | null>(null)

  translate = inject(TranslateService);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  units = resource({ loader: () => this.lookupService.getUnit() }).value;
  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')

  form = this.fb.group({
    id: [0],
    talukName: ['', Validators.required],
    talukRegionalName: ['', Validators.required],
    talukLgdCode: ['', Validators.required],
    regionId: this.fb.control<number | null>(null, Validators.required),
    unitId: this.fb.control<number | null>(null, Validators.required),
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegionUnit();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getTaluk()
    }
  }

  async fetchRegionUnit() {
    this.regions.set(await this.lookupService.getRegion());
    this.units.set(await this.lookupService.getUnit());
    this.getTaluk()
  }
  readonly regionSearch = signal('');
  readonly unitSearch = signal('');
 
  readonly filteredRegions = computed(() =>
    this.regions()?.filter(region =>
      region.value.toLowerCase().includes(this.regionSearch().toLowerCase())
    )
  );

  readonly filteredUnites = computed(() =>
    this.units()?.filter(region =>
      region.value.toLowerCase().includes(this.unitSearch().toLowerCase())
    )
  );

  filter(value: any, type:string) {
     if (type === 'region') {
      this.regionSearch.set(value.target.value);
    } else if (type === 'unit') {
      this.unitSearch.set(value.target.value);
    }
  }
    resetSearch(type: any) {

    if (type === 'region') {
      this.search1().nativeElement.value = '';
      this.regionSearch.set('');
    } else if (type === 'unit') {
      this.unitSearch.set('');
      this.search2().nativeElement.value = '';
    } 

  }


  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

  async getTaluk() {
    const res = await this.talukService.getById(this.talukId());
    this.taluk.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.talukService.create(formValue as Taluk)
      if (res.isSuccess) {
        this.closed.emit(true);
        if (formValue.id == 0) {
          this.alertService.success(`Taluk ${formValue.talukName} Created successfully.`)
        }
        else {
          this.alertService.success(`Taluk ${formValue.talukName} Updated successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  goBack() {
    this.closed.emit(true)
  }
}
