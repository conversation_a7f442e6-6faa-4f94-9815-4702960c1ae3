import { Component, computed, ElementRef, inject, input, OnInit, output, resource, signal, viewChild } from '@angular/core';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { LookUpResponse } from '../../../../models/x_models/lookup';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatNativeDateModule } from '@angular/material/core';
import { DpcService } from '../../../../services/x_apis/masters/dpc.service';
import { dpc } from '../../../../models/x_models/masters/dpc';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-DPC',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatRadioModule,
    MatNativeDateModule,
    TranslateModule,
    MatToolbarModule
  ],
  templateUrl: './dpc.component.html',
  styleUrls: ['./dpc.component.scss']
})

export class DpcComponent implements OnInit {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  dpcService = inject(DpcService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);

  translate = inject(TranslateService);
  taluks = signal<LookUpResponse[]>([]);
  blocks = signal<LookUpResponse[]>([]);
  villages = signal<LookUpResponse[]>([]);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  units = resource({ loader: () => this.lookupService.getUnit() }).value;
  dpcs = resource({ loader: () => this.lookupService.getDpcType() }).value;
  POSs = resource({ loader: () => this.lookupService.getPosType() }).value;
  agencies = resource({ loader: () => this.lookupService.getAgency() }).value

  uniqueId = input.required<number>();
  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);

  dpc = signal<dpc | null>(null);

  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')
  search3 = viewChild.required<ElementRef<HTMLInputElement>>('search3')
  search4 = viewChild.required<ElementRef<HTMLInputElement>>('search4')
  search5 = viewChild.required<ElementRef<HTMLInputElement>>('search5')
  search6 = viewChild.required<ElementRef<HTMLInputElement>>('search6')

  form = this.fb.group({
    id: [0],
    dpcCode: ['0'],
    edpcDpcCode: [''],
    dpcName: ['', Validators.required],
    dpcRegionalName: ['', Validators.required],
    permanentAddress: ['', Validators.required],
    presentAddress: ['', Validators.required],
    pincode: ['', Validators.required],
    dpcType: this.fb.control<number | null>(null, Validators.required),
    posType: this.fb.control<number | null>(null, Validators.required),
    latitude: this.fb.control<number | null>(null, Validators.required),
    longitude: this.fb.control<number | null>(null, Validators.required),
    fromDate: ['', Validators.required],
    toDate: ['', Validators.required],
    authBypassFlag: [false, Validators.required],
    regionId: this.fb.control<number | null>(null, Validators.required),
    unitId: this.fb.control<number | null>(null, Validators.required),
    talukId: this.fb.control<number | null>(null, Validators.required),
    blockId: this.fb.control<number | null>(null, Validators.required),
    villageId: this.fb.control<number | null>(null, Validators.required),
    agencyId: this.fb.control<number | null>(null, Validators.required),
    storageLocationId: this.fb.control<number>(0, Validators.required)
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getDPC()
    }
    this.form.get("unitId")?.valueChanges.subscribe(res => {

      this.getTaluk(res);
    })
    this.form.get("talukId")?.valueChanges.subscribe(res => {
      this.getblock(res);
    })
    this.form.get("blockId")?.valueChanges.subscribe(res => {
      this.getVillage(res);
    })
  }

  readonly regionSearch = signal('');
  readonly unitSearch = signal('');
  readonly blockSearch = signal('');
  readonly talukSearch = signal('');
  readonly agencySearch = signal('');
  readonly villageSearch = signal('');


  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.regionSearch().toLowerCase())
    )
  );

  readonly filteredUnites = computed(() =>
    this.units()?.filter(x =>
      x.value.toLowerCase().includes(this.unitSearch().toLowerCase())
    )
  );

  readonly filteredTaluks = computed(() =>
    this.taluks()?.filter(x =>
      x.value.toLowerCase().includes(this.talukSearch().toLowerCase())
    )
  );

   readonly filteredVillages = computed(() =>
    this.villages()?.filter(x =>
      x.value.toLowerCase().includes(this.villageSearch().toLowerCase())
    )
  );


   readonly filteredBlocks = computed(() =>
    this.blocks()?.filter(x =>
      x.value.toLowerCase().includes(this.blockSearch().toLowerCase())
    )
  );


   readonly filteredAgencies = computed(() =>
    this.agencies()?.filter(x =>
      x.value.toLowerCase().includes(this.agencySearch().toLowerCase())
    )
  );


  filter(value: any,type:string) {
     if (type === 'region') {
      this.regionSearch.set(value.target.value);
    } else if (type === 'unit') {
      this.unitSearch.set(value.target.value);
    } else if (type === 'taluk') {
      this.talukSearch.set(value.target.value);
    } else if (type === 'block') {
      this.blockSearch.set(value.target.value);
    } else if (type === 'village') {
      this.villageSearch.set(value.target.value);
    } else if (type === 'agency') {
      this.agencySearch.set(value.target.value);
    }
  }

   resetSearch(type: any) {

    if (type === 'region') {
      this.search1().nativeElement.value = '';
      this.regionSearch.set('');
    } else if (type === 'unit') {
      this.unitSearch.set('');
      this.search2().nativeElement.value = '';
    } else if (type === 'taluk') {
      this.talukSearch.set('');
      this.search3().nativeElement.value = '';

    } else if (type === 'block') {
      this.blockSearch.set('');
      this.search4().nativeElement.value = '';

    } else if (type === 'village') {
      this.villageSearch.set('');
      this.search4().nativeElement.value = '';
    }
    else {
      this.agencySearch.set('');
      this.search4().nativeElement.value = '';
    }

  }

  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
    this.units.set(await this.lookupService.getUnit());
    this.dpcs.set(await this.lookupService.getDpcType());
    // this.POSs.set(await this.lookupService.getPosType());
    this.getTaluk(this.form.value.unitId);
    this.getblock(this.form.value.talukId);
    this.getVillage(this.form.value.villageId);
  }


  async getDPC() {
    const res = await this.dpcService.getById(this.uniqueId());
    this.dpc.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

  async getTaluk(unitId: any) {
    const res = await this.lookupService.getTaluk(this.form.value.regionId!, unitId)
    this.taluks.set(res)
  }

  async getblock(talukId: any) {
    const res = await this.lookupService.getBlock(talukId)
    this.blocks.set(res)
  }

  async getVillage(blockId: any) {
    const res = await this.lookupService.getVillage(blockId)
    this.villages.set(res)
  }
  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }


  async onSubmit() {
    debugger
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.dpcService.create(formValue as dpc);
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`DPC "${formValue.dpcName}" Saved Successfully.`)
        }
        else {
          this.alertService.success(`DPC "${formValue.dpcName}" Updated Successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }


  goBack() {
    this.closed.emit(true)
  }

}
