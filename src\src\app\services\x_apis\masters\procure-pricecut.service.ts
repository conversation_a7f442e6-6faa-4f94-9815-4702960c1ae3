import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { ProcurePricecut, ProcurePricecutResponse } from '../../../models/x_models/masters/procure-pricecut';

@Injectable({
  providedIn: 'root'
})
export class ProcurePricecutService {

  dataService = inject(DataService)

  create(data: ProcurePricecut) {
    return this.dataService.post<Response>("/pricecut", data)
  }

  get() {
    return this.dataService.get<ProcurePricecutResponse>("/pricecut")
  }

  getById(id: number) {
    return this.dataService.get<ProcurePricecut>(`/pricecut/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/pricecut/${id}`)
  }

}
