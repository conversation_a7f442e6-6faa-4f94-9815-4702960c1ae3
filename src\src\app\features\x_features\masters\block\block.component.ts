import { Component, computed, inject, input, output, resource, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { LookUpResponse } from '../../../../models/x_models/lookup';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { MatNativeDateModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { BlockService } from '../../../../services/x_apis/masters/block.service';
import { Block } from '../../../../models/x_models/masters/block';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-block',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule,
    MatToolbarModule,
    MatFormFieldModule],
  templateUrl: './block.component.html',
  styleUrl: './block.component.scss'
})
export class BlockComponent {
  menuService = inject(MenuService);
  blockService = inject(BlockService);
  lookupService = inject(LookupService);
  alertService = inject(AlertService)
  inputFormatService = inject(InputformatService);


  mode = input.required<string>();
  closed = output<boolean>();
  editable = signal<boolean>(true);
  router = inject(Router);

  fb = inject(FormBuilder);

  taluks = signal<LookUpResponse[]>([]);

  blockId = input.required<any>()
  block = signal<Block | null>(null);

  translate = inject(TranslateService);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  units = resource({ loader: () => this.lookupService.getUnit() }).value;

  form = this.fb.group({
    id: this.fb.control<number | null>(0),
    blockName: this.fb.control<string>('', [Validators.required]),
    blockRegionalName: this.fb.control<string>('', [Validators.required]),
    blockLgdCode: this.fb.control<string>('', [Validators.required]),
    regionId: this.fb.control<number | null>(null, Validators.required),
    unitId: this.fb.control<number | null>(null, Validators.required),
    talukId: this.fb.control<number | null>(null, Validators.required)
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegionUnit();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

  readonly search = signal('');

  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  readonly filteredUnites = computed(() =>
    this.units()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  readonly filteredTaluks = computed(() =>
    this.taluks()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  filter(value: any) {
    this.search.set(value.target.value);
  }

  async fetchRegionUnit() {
    this.regions.set(await this.lookupService.getRegion());
    this.units.set(await this.lookupService.getUnit());
    this.getTaluk();
  }

  async getTaluk() {
    if (this.form.value.regionId && this.form.value.unitId) {
      const taluk = await this.lookupService.getTaluk(Number(this.form.value.regionId), Number(this.form.value.unitId));
      this.taluks.set(taluk);
    }
  }

  async getFormData() {
    const res = await this.blockService.getById(this.blockId());
    this.block.set(res)
    this.form.patchValue({ ...res });
    this.getTaluk();
    this.form.controls['talukId'].setValue(res.talukId);

    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.blockService.create(formValue as Block)
      if (res.isSuccess) {
        this.closed.emit(true);
        if (formValue.id == 0) {
          this.alertService.success(`Block ${formValue.blockName} Created successfully.`)
        }
        else {
          this.alertService.success(`Block ${formValue.blockName} Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }


  goBack() {
    this.closed.emit(true)
  }
}
