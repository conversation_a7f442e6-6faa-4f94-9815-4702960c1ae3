import { Component, computed, inject, input, OnInit, output, resource, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AlertService } from '../../../../services/alert.service';
import { DatePipe } from '@angular/common';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { ProcuretokenService } from '../../../../services/x_apis/procurement/procuretoken.service';
import { Procuretoken } from '../../../../models/x_models/procurement/procuetoken';

@Component({
    selector: 'app-tokendetails',
    imports: [FormsModule,
        MatFormField,
        MatInput,
        ReactiveFormsModule,
        MatError,
        MatOption,
        MatSelect,
        MatButton,
        MatIconModule,
        MatDatepickerModule,
        DatePipe,
        MatFormFieldModule, TranslateModule],
    providers: [DatePipe],
    templateUrl: './tokendetails.component.html',
    styleUrl: './tokendetails.component.scss'
})
export class TokendetailsComponent {
    
    menuService = inject(MenuService);
    procuretokenService = inject(ProcuretokenService)
    alert = inject(AlertService);
    inputFormatService = inject(InputformatService);
    translate = inject(TranslateService);
    lookupService = inject(LookupService);

    mode = input.required<string>();
    tokenId = input.required<any>()
    closed = output<boolean>();
    router = inject(Router);

    fb = inject(FormBuilder);
    editable = signal<boolean>(true);
    tokenData = signal<Procuretoken | null>(null);
    minToDate = signal<Date>(new Date());
    showGrid =signal<number>(1);

    constructor() { }

    ngOnInit() {
        debugger
            this.getById();
    }

    async getById() {
        debugger
        const res = await this.procuretokenService.getById(this.tokenId());
        this.tokenData.set(res);
        if(res){
            this.showGrid.set(2)
        }
        console.log('this.tokenData',this.tokenData())
    }

    navigateBack() {
        this.closed.emit(true)
    }

}