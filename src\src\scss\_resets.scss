*,
*::before,
*::after {
    box-sizing: border-box;
    // letter-spacing: 0;
}

* {
    margin: 0;
}

html,
body {
    height: 100%;
    color: var(--grey-100);
    font-size: 16px;
}

body {
    line-height: 1.5;
    font-family: "Roboto", "Helvetica Neue", sans-serif !important;
    -webkit-font-smoothing: antialiased;
}

img,
picture,
video,
canvas,
svg {
    display: block;
    max-width: 100%;
}

input,
button,
textarea,
select {
    font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
    overflow-wrap: break-word;
}

#root,
#__next {
    isolation: isolate;
}

router-outlet + * {
  display: block;
  animation: fade-in-bottom .2s ease-in-out;
  height: 100%;
}

.feather {
    font-family: feather !important;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-feature-settings: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

// input:-internal-autofill-selected {
//   background-image: none !important;
//   background-color: #fff !important;
//   color: red;
// }

// input:-webkit-autofill {
//   appearance: none !important;
//   background-color: white !important; /* Change to your preferred color */
//   // color: black !important; /* Change text color */
//   box-shadow: 0 0 0px 1000px white inset !important; /* Force background override */
//   font-size: 14px !important;
// }
