<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        
        {{device()?.imeiNo}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

  <div class="card component">
  
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="imeiNo" class="required-label">{{'IMEINumber' | translate}}</label>
          <mat-form-field>
            <input id="imeiNo" formControlName="imeiNo" matInput (input)="onInput($event,'/^[0-9]*$/')"  maxlength="15">
            <mat-error>
              @if(form.controls.imeiNo.errors?.['required']) {
                {{'IMEINumber' | translate}} {{'IsRequired' | translate}}
                } 
                <!-- @else if (form.controls.Name.errors?.['pattern']) {
                Please use only letters and numbers.
                } -->
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="SeasonId" class="required-label">{{'DeviceManufacturer' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="SeasonId" formControlName="deviceManufacturerType" (openedChange)="resetSearch('device')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'device')">
              </mat-form-field>
              @for (devicemanufacturer of filteredManufacturers(); track devicemanufacturer) {
              <mat-option [value]="devicemanufacturer.key">{{devicemanufacturer.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.deviceManufacturerType.errors?.['required']) {
                {{'DeviceManufacturer' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>
        <div class="field">
          <label for="firmware" class="required-label">{{'FirmwareVersion' | translate}}</label>
          <mat-form-field>
            <input id="firmware" formControlName="firmwareVersion" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"  maxlength="100">
            <mat-error>
              @if(form.controls.firmwareVersion.errors?.['required']) {
                {{'FirmwareVersion' | translate}} {{'IsRequired' | translate}}
                } 
                <!-- @else if (form.controls.Name.errors?.['pattern']) {
                Please use only letters and numbers.
                } -->
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="providerId" class="required-label">{{'Provider' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="providerId" formControlName="providerId" (openedChange)="resetSearch('provider')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'provider')">
              </mat-form-field>
              @for (provider of filteredProviders(); track provider) {
              <mat-option [value]="provider.key">{{provider.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.providerId.errors?.['required']) {
                {{'Provider' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="protocolId" class="required-label">{{'Protocol' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="protocolId" formControlName="protocolId" (openedChange)="resetSearch('protocol')">
             <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search3 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'protocol')">
              </mat-form-field>
              @for (provider of filteredProtocols(); track provider) {
              <mat-option [value]="provider.key">{{provider.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.protocolId.errors?.['required']) {
                {{'Protocol' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

      </div>

      <div class="actions">
       
        @if(editable() === true) {
          @if(mode() === 'add') {
            <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' | translate}}</button>
            }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0 ?
          ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>
    </form>
  </div>
