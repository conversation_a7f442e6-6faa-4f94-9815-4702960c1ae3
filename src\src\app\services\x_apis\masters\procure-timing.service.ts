import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { ProcureTiming, ProcureTimingResponse } from '../../../models/x_models/masters/procure-timing';


@Injectable({
  providedIn: 'root'
})
export class ProcureTimingService {

  dataService = inject(DataService)

  create(data: ProcureTiming) {
    return this.dataService.post<Response>("/procurementtiming", data)
  }

  get() {
    return this.dataService.get<ProcureTimingResponse>("/procurementtiming")
  }

  getById(id: number) {
    return this.dataService.get<ProcureTiming>(`/procurementtiming/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/procurementtiming/${id}`)
  }

}
