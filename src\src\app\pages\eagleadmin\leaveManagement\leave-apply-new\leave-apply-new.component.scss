// TNCSC Leave Form Styles
.leave-form-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    background-color: #fcfcfc;
    min-height: 100vh;
    font-family: 'Roboto', sans-serif;
  
    // Header Card
    .header-card {
        background: linear-gradient(135deg, #203664 0%, darken(#203664, 10%) 100%);
        color: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 24px;
        transition: transform 0.3s ease;
      
        &:hover {
          transform: translateY(-4px);
        }
      
        .form-header {
          padding: 16px;
          display: flex;
          align-items: center;
      
          .header-content {
            display: flex;
            align-items: center;
            width: 100%;
          }
      
          .logo-section {
            margin-right: 16px;
      
            .tncsc-logo {
              font-size: 40px;
              width: 40px;
              height: 40px;
              color: white;
              background-color: rgba(255, 255, 255, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
      
          .title-section {
            .form-subtitle {
              font-size: 24px;
              font-weight: 500;
              margin: 0;
            }
      
            .subtitle {
              font-size: 14px;
              opacity: 0.8;
              margin: 4px 0 0;
            }
          }
        }
      }
  
    // Main Form Card
    .form-card {
      border-radius: 16px !important;
      box-shadow: 0 12px 48px rgba(0, 0, 0, 0.08);
      padding: 32px;
      background: white;
      position: relative;
      overflow: visible;
  
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        // background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px 16px 0 0;
      }
    }
  
    // Form Sections
    .form-section {
      margin-bottom: 40px;
      
      .section-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 24px;
        padding-bottom: 12px;
        border-bottom: 2px solid #e0e7ff;
        
        mat-icon {
          color: #667eea;
          font-size: 24px;
          width: 24px;
          height: 24px;
        }
      }
    }
  
    // Form Layout
    .form-row {
      display: flex;
      padding-top: 15px;
      gap: 20px;
      margin-bottom: 20px;
      
      &.two-columns {
        .date-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          
          .session-field {
            margin-top: 0;
          }
        }
      }
    }
  
    .full-width {
      width: 100%;
    }
  
    // Material Form Field Customization
    ::ng-deep {
      .mat-mdc-form-field {
        &.mat-form-field-appearance-outline {
          .mat-mdc-form-field-outline {
            color: #d1d5db;
            
            &-thick {
              color: #667eea;
            }
          }
          
          &.mat-focused .mat-mdc-form-field-outline-thick {
            color: #667eea;
          }
          
          .mat-mdc-form-field-label {
            color: #6b7280;
          }
          
          &.mat-focused .mat-mdc-form-field-label {
            color: #667eea;
          }
        }
        
        .mat-mdc-input-element {
          color: #374151;
        }
        
        .mat-mdc-select-arrow {
          color: #667eea;
        }
      }
  
      .mat-mdc-select-panel {
        border-radius: 12px;
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
      }
  
      .mat-mdc-option {
        &:hover {
          background-color: #f3f4f6;
        }
        
        &.mat-mdc-option-active {
          background-color: #eef2ff;
          color: #667eea;
        }
      }
    }
  
    // Leave Duration Display
    .leave-duration {
      display: flex;
      justify-content: center;
      margin: 20px 0;
      
      mat-chip-listbox {
        mat-chip-option {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          font-weight: 500;
          padding: 12px 20px;
          border-radius: 25px;
          
          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }
  
    // File Upload Section
    .file-upload-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
      
      .upload-button {
        padding: 12px 24px;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }
        
        mat-icon {
          margin-right: 8px;
        }
      }
      
      .file-info {
        font-size: 12px;
        color: #6b7280;
        font-style: italic;
      }
    }
  
    .selected-files-container {
      margin-top: 16px;
      
      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #374151;
      }
      
      mat-chip-listbox {
        .file-chip {
          background-color: #f3f4f6;
          color: #374151;
          margin: 4px;
          border-radius: 20px;
          
          mat-icon {
            margin-right: 8px;
            font-size: 16px;
          }
          
          button[matChipRemove] {
            margin-left: 8px;
            
            mat-icon {
              font-size: 16px;
              color: #ef4444;
            }
          }
        }
      }
    }
  
    // Action Buttons
    .form-actions {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      margin-top: 40px;
      padding-top: 24px;
      border-top: 1px solid #e5e7eb;
      
      .action-button {
        flex: 1;
        max-width: 200px;
        padding: 14px 28px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        
        mat-icon {
          margin-right: 8px;
        }
        
        &:hover {
          transform: translateY(-2px);
        }
        
        &.submit-button {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          
          &:hover {
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
          }
          
          &:disabled {
            background: #d1d5db;
            color: #9ca3af;
            transform: none;
            cursor: not-allowed;
          }
        }
      }
    }
  
    // Footer
    .form-footer {
      margin-top: 24px;
      text-align: center;
      
      p {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #6b7280;
        font-size: 14px;
        margin: 0;
        padding: 16px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        
        mat-icon {
          color: #667eea;
        }
      }
    }
  
    // Responsive Design
    @media (max-width: 768px) {
      padding: 16px;
      
      .header-card .form-header .header-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
        
        .title-section {
          .form-title {
            font-size: 24px;
          }
          
          .form-subtitle {
            font-size: 16px;

          }
        }
      }
      
      .form-card {
        padding: 24px 20px;
      }
      
      .form-row {
        flex-direction: column;
        gap: 12px;
        
        &.two-columns .date-container {
          gap: 8px;
        }
      }
      
      .form-actions {
        flex-direction: column;
        
        .action-button {
          max-width: none;
        }
      }
    }
  
    @media (max-width: 480px) {
      padding: 12px;
      
      .form-card {
        padding: 20px 16px;
      }
      
      .section-title {
        font-size: 18px;
        
        mat-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }
    }
  }
  
  // Custom Snackbar Styles
  ::ng-deep {
    .success-snackbar {
      background-color: #10b981 !important;
      color: white !important;
    }
    
    .error-snackbar {
      background-color: #ef4444 !important;
      color: white !important;
    }
  }