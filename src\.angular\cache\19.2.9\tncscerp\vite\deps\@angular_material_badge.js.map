{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/badge.mjs"], "sourcesContent": ["import { AriaDescriber, _IdGenerator, InteractivityChecker, A11yModule } from '@angular/cdk/a11y';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, NgZone, ElementRef, Renderer2, ANIMATION_MODULE_TYPE, booleanAttribute, ChangeDetectionStrategy, ViewEncapsulation, Component, Input, Directive, NgModule } from '@angular/core';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport { MatCommonModule } from './common-module-DoCSSHRt.mjs';\nimport '@angular/cdk/bidi';\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n/**\n * Component used to load the structural styles of the badge.\n * @docs-private\n */\nclass _MatBadgeStyleLoader {\n  static ɵfac = function _MatBadgeStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _MatBadgeStyleLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _MatBadgeStyleLoader,\n    selectors: [[\"ng-component\"]],\n    decls: 0,\n    vars: 0,\n    template: function _MatBadgeStyleLoader_Template(rf, ctx) {},\n    styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color, var(--mat-sys-error));color:var(--mat-badge-text-color, var(--mat-sys-on-error));font-family:var(--mat-badge-text-font, var(--mat-sys-label-small-font));font-weight:var(--mat-badge-text-weight, var(--mat-sys-label-small-weight));border-radius:var(--mat-badge-container-shape, var(--mat-sys-corner-full))}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}@media(forced-colors: active){.mat-badge-content{outline:solid 1px;border-radius:0}}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color, color-mix(in srgb, var(--mat-sys-error) 38%, transparent));color:var(--mat-badge-disabled-state-text-color, var(--mat-sys-on-error))}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, 6px);min-height:var(--mat-badge-small-size-container-size, 6px);line-height:var(--mat-badge-small-size-line-height, 6px);padding:var(--mat-badge-small-size-container-padding, 0);font-size:var(--mat-badge-small-size-text-size, 0);margin:var(--mat-badge-small-size-container-offset, -6px 0)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset, -6px)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, 16px);min-height:var(--mat-badge-container-size, 16px);line-height:var(--mat-badge-line-height, 16px);padding:var(--mat-badge-container-padding, 0 4px);font-size:var(--mat-badge-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-container-offset, -12px 0)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset, -12px)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, 16px);min-height:var(--mat-badge-large-size-container-size, 16px);line-height:var(--mat-badge-large-size-line-height, 16px);padding:var(--mat-badge-large-size-container-padding, 0 4px);font-size:var(--mat-badge-large-size-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-large-size-container-offset, -12px 0)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset, -12px)}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatBadgeStyleLoader, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color, var(--mat-sys-error));color:var(--mat-badge-text-color, var(--mat-sys-on-error));font-family:var(--mat-badge-text-font, var(--mat-sys-label-small-font));font-weight:var(--mat-badge-text-weight, var(--mat-sys-label-small-weight));border-radius:var(--mat-badge-container-shape, var(--mat-sys-corner-full))}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}@media(forced-colors: active){.mat-badge-content{outline:solid 1px;border-radius:0}}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color, color-mix(in srgb, var(--mat-sys-error) 38%, transparent));color:var(--mat-badge-disabled-state-text-color, var(--mat-sys-on-error))}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, 6px);min-height:var(--mat-badge-small-size-container-size, 6px);line-height:var(--mat-badge-small-size-line-height, 6px);padding:var(--mat-badge-small-size-container-padding, 0);font-size:var(--mat-badge-small-size-text-size, 0);margin:var(--mat-badge-small-size-container-offset, -6px 0)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset, -6px)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, 16px);min-height:var(--mat-badge-container-size, 16px);line-height:var(--mat-badge-line-height, 16px);padding:var(--mat-badge-container-padding, 0 4px);font-size:var(--mat-badge-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-container-offset, -12px 0)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset, -12px)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, 16px);min-height:var(--mat-badge-large-size-container-size, 16px);line-height:var(--mat-badge-large-size-line-height, 16px);padding:var(--mat-badge-large-size-container-padding, 0 4px);font-size:var(--mat-badge-large-size-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-large-size-container-offset, -12px 0)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset, -12px)}\\n\"]\n    }]\n  }], null, null);\n})();\n/** Directive to display a text badge. */\nclass MatBadge {\n  _ngZone = inject(NgZone);\n  _elementRef = inject(ElementRef);\n  _ariaDescriber = inject(AriaDescriber);\n  _renderer = inject(Renderer2);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _idGenerator = inject(_IdGenerator);\n  /**\n   * Theme color of the badge. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.io/components/badge/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.io/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color;\n  }\n  set color(value) {\n    this._setColor(value);\n    this._color = value;\n  }\n  _color = 'primary';\n  /** Whether the badge should overlap its contents or not */\n  overlap = true;\n  /** Whether the badge is disabled. */\n  disabled;\n  /**\n   * Position the badge should reside.\n   * Accepts any combination of 'above'|'below' and 'before'|'after'\n   */\n  position = 'above after';\n  /** The content for the badge */\n  get content() {\n    return this._content;\n  }\n  set content(newContent) {\n    this._updateRenderedContent(newContent);\n  }\n  _content;\n  /** Message used to describe the decorated element via aria-describedby */\n  get description() {\n    return this._description;\n  }\n  set description(newDescription) {\n    this._updateDescription(newDescription);\n  }\n  _description;\n  /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n  size = 'medium';\n  /** Whether the badge is hidden. */\n  hidden;\n  /** Visible badge element. */\n  _badgeElement;\n  /** Inline badge description. Used when the badge is applied to non-interactive host elements. */\n  _inlineBadgeDescription;\n  /** Whether the OnInit lifecycle hook has run yet */\n  _isInitialized = false;\n  /** InteractivityChecker to determine if the badge host is focusable. */\n  _interactivityChecker = inject(InteractivityChecker);\n  _document = inject(DOCUMENT);\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_MatBadgeStyleLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const nativeElement = this._elementRef.nativeElement;\n      if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n        throw Error('matBadge must be attached to an element node.');\n      }\n      // Heads-up for developers to avoid putting matBadge on <mat-icon>\n      // as it is aria-hidden by default docs mention this at:\n      // https://material.angular.io/components/badge/overview#accessibility\n      if (nativeElement.tagName.toLowerCase() === 'mat-icon' && nativeElement.getAttribute('aria-hidden') === 'true') {\n        console.warn(`Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` + `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` + `\\n${nativeElement.outerHTML}`);\n      }\n    }\n  }\n  /** Whether the badge is above the host or not */\n  isAbove() {\n    return this.position.indexOf('below') === -1;\n  }\n  /** Whether the badge is after the host or not */\n  isAfter() {\n    return this.position.indexOf('before') === -1;\n  }\n  /**\n   * Gets the element into which the badge's content is being rendered. Undefined if the element\n   * hasn't been created (e.g. if the badge doesn't have content).\n   */\n  getBadgeElement() {\n    return this._badgeElement;\n  }\n  ngOnInit() {\n    // We may have server-side rendered badge that we need to clear.\n    // We need to do this in ngOnInit because the full content of the component\n    // on which the badge is attached won't necessarily be in the DOM until this point.\n    this._clearExistingBadges();\n    if (this.content && !this._badgeElement) {\n      this._badgeElement = this._createBadgeElement();\n      this._updateRenderedContent(this.content);\n    }\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n    // We have to destroy it ourselves, otherwise it'll be retained in memory.\n    if (this._renderer.destroyNode) {\n      this._renderer.destroyNode(this._badgeElement);\n      this._inlineBadgeDescription?.remove();\n    }\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n  }\n  /** Gets whether the badge's host element is interactive. */\n  _isHostInteractive() {\n    // Ignore visibility since it requires an expensive style caluclation.\n    return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n      ignoreVisibility: true\n    });\n  }\n  /** Creates the badge element */\n  _createBadgeElement() {\n    const badgeElement = this._renderer.createElement('span');\n    const activeClass = 'mat-badge-active';\n    badgeElement.setAttribute('id', this._idGenerator.getId('mat-badge-content-'));\n    // The badge is aria-hidden because we don't want it to appear in the page's navigation\n    // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n    badgeElement.setAttribute('aria-hidden', 'true');\n    badgeElement.classList.add(BADGE_CONTENT_CLASS);\n    if (this._animationMode === 'NoopAnimations') {\n      badgeElement.classList.add('_mat-animation-noopable');\n    }\n    this._elementRef.nativeElement.appendChild(badgeElement);\n    // animate in after insertion\n    if (typeof requestAnimationFrame === 'function' && this._animationMode !== 'NoopAnimations') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          badgeElement.classList.add(activeClass);\n        });\n      });\n    } else {\n      badgeElement.classList.add(activeClass);\n    }\n    return badgeElement;\n  }\n  /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n  _updateRenderedContent(newContent) {\n    const newContentNormalized = `${newContent ?? ''}`.trim();\n    // Don't create the badge element if the directive isn't initialized because we want to\n    // append the badge element to the *end* of the host element's content for backwards\n    // compatibility.\n    if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n      this._badgeElement = this._createBadgeElement();\n    }\n    if (this._badgeElement) {\n      this._badgeElement.textContent = newContentNormalized;\n    }\n    this._content = newContentNormalized;\n  }\n  /** Updates the host element's aria description via AriaDescriber. */\n  _updateDescription(newDescription) {\n    // Always start by removing the aria-describedby; we will add a new one if necessary.\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n    // NOTE: We only check whether the host is interactive here, which happens during\n    // when then badge content changes. It is possible that the host changes\n    // interactivity status separate from one of these. However, watching the interactivity\n    // status of the host would require a `MutationObserver`, which is likely more code + overhead\n    // than it's worth; from usages inside Google, we see that the vats majority of badges either\n    // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n    if (!newDescription || this._isHostInteractive()) {\n      this._removeInlineDescription();\n    }\n    this._description = newDescription;\n    // We don't add `aria-describedby` for non-interactive hosts elements because we\n    // instead insert the description inline.\n    if (this._isHostInteractive()) {\n      this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n    } else {\n      this._updateInlineDescription();\n    }\n  }\n  _updateInlineDescription() {\n    // Create the inline description element if it doesn't exist\n    if (!this._inlineBadgeDescription) {\n      this._inlineBadgeDescription = this._document.createElement('span');\n      this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n    }\n    this._inlineBadgeDescription.textContent = this.description;\n    this._badgeElement?.appendChild(this._inlineBadgeDescription);\n  }\n  _removeInlineDescription() {\n    this._inlineBadgeDescription?.remove();\n    this._inlineBadgeDescription = undefined;\n  }\n  /** Adds css theme class given the color to the component host */\n  _setColor(colorPalette) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove(`mat-badge-${this._color}`);\n    if (colorPalette) {\n      classList.add(`mat-badge-${colorPalette}`);\n    }\n  }\n  /** Clears any existing badges that might be left over from server-side rendering. */\n  _clearExistingBadges() {\n    // Only check direct children of this host element in order to avoid deleting\n    // any badges that might exist in descendant elements.\n    const badges = this._elementRef.nativeElement.querySelectorAll(`:scope > .${BADGE_CONTENT_CLASS}`);\n    for (const badgeElement of Array.from(badges)) {\n      if (badgeElement !== this._badgeElement) {\n        badgeElement.remove();\n      }\n    }\n  }\n  static ɵfac = function MatBadge_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatBadge)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatBadge,\n    selectors: [[\"\", \"matBadge\", \"\"]],\n    hostAttrs: [1, \"mat-badge\"],\n    hostVars: 20,\n    hostBindings: function MatBadge_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-badge-overlap\", ctx.overlap)(\"mat-badge-above\", ctx.isAbove())(\"mat-badge-below\", !ctx.isAbove())(\"mat-badge-before\", !ctx.isAfter())(\"mat-badge-after\", ctx.isAfter())(\"mat-badge-small\", ctx.size === \"small\")(\"mat-badge-medium\", ctx.size === \"medium\")(\"mat-badge-large\", ctx.size === \"large\")(\"mat-badge-hidden\", ctx.hidden || !ctx.content)(\"mat-badge-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      color: [0, \"matBadgeColor\", \"color\"],\n      overlap: [2, \"matBadgeOverlap\", \"overlap\", booleanAttribute],\n      disabled: [2, \"matBadgeDisabled\", \"disabled\", booleanAttribute],\n      position: [0, \"matBadgePosition\", \"position\"],\n      content: [0, \"matBadge\", \"content\"],\n      description: [0, \"matBadgeDescription\", \"description\"],\n      size: [0, \"matBadgeSize\", \"size\"],\n      hidden: [2, \"matBadgeHidden\", \"hidden\", booleanAttribute]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatBadge, [{\n    type: Directive,\n    args: [{\n      selector: '[matBadge]',\n      host: {\n        'class': 'mat-badge',\n        '[class.mat-badge-overlap]': 'overlap',\n        '[class.mat-badge-above]': 'isAbove()',\n        '[class.mat-badge-below]': '!isAbove()',\n        '[class.mat-badge-before]': '!isAfter()',\n        '[class.mat-badge-after]': 'isAfter()',\n        '[class.mat-badge-small]': 'size === \"small\"',\n        '[class.mat-badge-medium]': 'size === \"medium\"',\n        '[class.mat-badge-large]': 'size === \"large\"',\n        '[class.mat-badge-hidden]': 'hidden || !content',\n        '[class.mat-badge-disabled]': 'disabled'\n      }\n    }]\n  }], () => [], {\n    color: [{\n      type: Input,\n      args: ['matBadgeColor']\n    }],\n    overlap: [{\n      type: Input,\n      args: [{\n        alias: 'matBadgeOverlap',\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'matBadgeDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    position: [{\n      type: Input,\n      args: ['matBadgePosition']\n    }],\n    content: [{\n      type: Input,\n      args: ['matBadge']\n    }],\n    description: [{\n      type: Input,\n      args: ['matBadgeDescription']\n    }],\n    size: [{\n      type: Input,\n      args: ['matBadgeSize']\n    }],\n    hidden: [{\n      type: Input,\n      args: [{\n        alias: 'matBadgeHidden',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatBadgeModule {\n  static ɵfac = function MatBadgeModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatBadgeModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatBadgeModule,\n    imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader],\n    exports: [MatBadge, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [A11yModule, MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatBadgeModule, [{\n    type: NgModule,\n    args: [{\n      // Note: we _shouldn't_ have to import `_MatBadgeStyleLoader`,\n      // but it seems to be necessary for tests.\n      imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader],\n      exports: [MatBadge, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatBadge, MatBadgeModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,sBAAsB;AAK5B,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,8BAA8B,IAAI,KAAK;AAAA,IAAC;AAAA,IAC3D,QAAQ,CAAC,kyGAAkyG;AAAA,IAC3yG,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,kyGAAkyG;AAAA,IAC7yG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA,EAC/B,iBAAiB,OAAO,aAAa;AAAA,EACrC,YAAY,OAAO,SAAS;AAAA,EAC5B,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlC,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,UAAU,KAAK;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS;AAAA;AAAA,EAET,UAAU;AAAA;AAAA,EAEV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA,EAEX,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,YAAY;AACtB,SAAK,uBAAuB,UAAU;AAAA,EACxC;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,gBAAgB;AAC9B,SAAK,mBAAmB,cAAc;AAAA,EACxC;AAAA,EACA;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,OAAO,oBAAoB;AAAA,EACnD,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,UAAM,cAAc,OAAO,sBAAsB;AACjD,gBAAY,KAAK,oBAAoB;AACrC,gBAAY,KAAK,qBAAqB;AACtC,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAM,gBAAgB,KAAK,YAAY;AACvC,UAAI,cAAc,aAAa,cAAc,cAAc;AACzD,cAAM,MAAM,+CAA+C;AAAA,MAC7D;AAIA,UAAI,cAAc,QAAQ,YAAY,MAAM,cAAc,cAAc,aAAa,aAAa,MAAM,QAAQ;AAC9G,gBAAQ,KAAK;AAAA,EAAmK,cAAc,SAAS,EAAE;AAAA,MAC3M;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,WAAO,KAAK,SAAS,QAAQ,OAAO,MAAM;AAAA,EAC5C;AAAA;AAAA,EAEA,UAAU;AACR,WAAO,KAAK,SAAS,QAAQ,QAAQ,MAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AAIT,SAAK,qBAAqB;AAC1B,QAAI,KAAK,WAAW,CAAC,KAAK,eAAe;AACvC,WAAK,gBAAgB,KAAK,oBAAoB;AAC9C,WAAK,uBAAuB,KAAK,OAAO;AAAA,IAC1C;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AAGZ,QAAI,KAAK,UAAU,aAAa;AAC9B,WAAK,UAAU,YAAY,KAAK,aAAa;AAC7C,WAAK,yBAAyB,OAAO;AAAA,IACvC;AACA,SAAK,eAAe,kBAAkB,KAAK,YAAY,eAAe,KAAK,WAAW;AAAA,EACxF;AAAA;AAAA,EAEA,qBAAqB;AAEnB,WAAO,KAAK,sBAAsB,YAAY,KAAK,YAAY,eAAe;AAAA,MAC5E,kBAAkB;AAAA,IACpB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,eAAe,KAAK,UAAU,cAAc,MAAM;AACxD,UAAM,cAAc;AACpB,iBAAa,aAAa,MAAM,KAAK,aAAa,MAAM,oBAAoB,CAAC;AAG7E,iBAAa,aAAa,eAAe,MAAM;AAC/C,iBAAa,UAAU,IAAI,mBAAmB;AAC9C,QAAI,KAAK,mBAAmB,kBAAkB;AAC5C,mBAAa,UAAU,IAAI,yBAAyB;AAAA,IACtD;AACA,SAAK,YAAY,cAAc,YAAY,YAAY;AAEvD,QAAI,OAAO,0BAA0B,cAAc,KAAK,mBAAmB,kBAAkB;AAC3F,WAAK,QAAQ,kBAAkB,MAAM;AACnC,8BAAsB,MAAM;AAC1B,uBAAa,UAAU,IAAI,WAAW;AAAA,QACxC,CAAC;AAAA,MACH,CAAC;AAAA,IACH,OAAO;AACL,mBAAa,UAAU,IAAI,WAAW;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,uBAAuB,YAAY;AACjC,UAAM,uBAAuB,GAAG,cAAc,EAAE,GAAG,KAAK;AAIxD,QAAI,KAAK,kBAAkB,wBAAwB,CAAC,KAAK,eAAe;AACtE,WAAK,gBAAgB,KAAK,oBAAoB;AAAA,IAChD;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,cAAc;AAAA,IACnC;AACA,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,mBAAmB,gBAAgB;AAEjC,SAAK,eAAe,kBAAkB,KAAK,YAAY,eAAe,KAAK,WAAW;AAOtF,QAAI,CAAC,kBAAkB,KAAK,mBAAmB,GAAG;AAChD,WAAK,yBAAyB;AAAA,IAChC;AACA,SAAK,eAAe;AAGpB,QAAI,KAAK,mBAAmB,GAAG;AAC7B,WAAK,eAAe,SAAS,KAAK,YAAY,eAAe,cAAc;AAAA,IAC7E,OAAO;AACL,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,2BAA2B;AAEzB,QAAI,CAAC,KAAK,yBAAyB;AACjC,WAAK,0BAA0B,KAAK,UAAU,cAAc,MAAM;AAClE,WAAK,wBAAwB,UAAU,IAAI,qBAAqB;AAAA,IAClE;AACA,SAAK,wBAAwB,cAAc,KAAK;AAChD,SAAK,eAAe,YAAY,KAAK,uBAAuB;AAAA,EAC9D;AAAA,EACA,2BAA2B;AACzB,SAAK,yBAAyB,OAAO;AACrC,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA,EAEA,UAAU,cAAc;AACtB,UAAM,YAAY,KAAK,YAAY,cAAc;AACjD,cAAU,OAAO,aAAa,KAAK,MAAM,EAAE;AAC3C,QAAI,cAAc;AAChB,gBAAU,IAAI,aAAa,YAAY,EAAE;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA,EAEA,uBAAuB;AAGrB,UAAM,SAAS,KAAK,YAAY,cAAc,iBAAiB,aAAa,mBAAmB,EAAE;AACjG,eAAW,gBAAgB,MAAM,KAAK,MAAM,GAAG;AAC7C,UAAI,iBAAiB,KAAK,eAAe;AACvC,qBAAa,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,IAChC,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,UAAU;AAAA,IACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,qBAAqB,IAAI,OAAO,EAAE,mBAAmB,IAAI,QAAQ,CAAC,EAAE,mBAAmB,CAAC,IAAI,QAAQ,CAAC,EAAE,oBAAoB,CAAC,IAAI,QAAQ,CAAC,EAAE,mBAAmB,IAAI,QAAQ,CAAC,EAAE,mBAAmB,IAAI,SAAS,OAAO,EAAE,oBAAoB,IAAI,SAAS,QAAQ,EAAE,mBAAmB,IAAI,SAAS,OAAO,EAAE,oBAAoB,IAAI,UAAU,CAAC,IAAI,OAAO,EAAE,sBAAsB,IAAI,QAAQ;AAAA,MAC7Y;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,iBAAiB,OAAO;AAAA,MACnC,SAAS,CAAC,GAAG,mBAAmB,WAAW,gBAAgB;AAAA,MAC3D,UAAU,CAAC,GAAG,oBAAoB,YAAY,gBAAgB;AAAA,MAC9D,UAAU,CAAC,GAAG,oBAAoB,UAAU;AAAA,MAC5C,SAAS,CAAC,GAAG,YAAY,SAAS;AAAA,MAClC,aAAa,CAAC,GAAG,uBAAuB,aAAa;AAAA,MACrD,MAAM,CAAC,GAAG,gBAAgB,MAAM;AAAA,MAChC,QAAQ,CAAC,GAAG,kBAAkB,UAAU,gBAAgB;AAAA,IAC1D;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,6BAA6B;AAAA,QAC7B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,QAC5B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,QAC5B,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,QAC5B,8BAA8B;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,iBAAiB,UAAU,oBAAoB;AAAA,IACrE,SAAS,CAAC,UAAU,eAAe;AAAA,EACrC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY,iBAAiB,eAAe;AAAA,EACxD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA;AAAA,MAGL,SAAS,CAAC,YAAY,iBAAiB,UAAU,oBAAoB;AAAA,MACrE,SAAS,CAAC,UAAU,eAAe;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}