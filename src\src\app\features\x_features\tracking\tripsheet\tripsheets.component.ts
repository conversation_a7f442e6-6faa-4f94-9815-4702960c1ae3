import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { ProcureTimings } from '../../../../models/x_models/masters/procure-timing';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { ProcureTimingService } from '../../../../services/x_apis/masters/procure-timing.service';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { TripsheetComponent } from "./tripsheet.component";
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
  selector: 'app-tripsheets',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatFormFieldModule,
    MatSortModule,
    TranslateModule,
    TripsheetComponent
],
  templateUrl: './tripsheets.component.html',
  styleUrls: ['./tripsheets.component.scss']
})

export class TripsheetsComponent {
    menuService = inject(MenuService);
    access = computed(() => this.menuService.activeMenu()?.controlAccess);
    translate = inject(TranslateService);

    paginator = viewChild<MatPaginator>(MatPaginator);
    sort = viewChild<MatSort>(MatSort);

    procureTimingService = inject(ProcureTimingService);
    procureTimingResource = resource({ loader: () => this.procureTimingService.get() });
    dataSource = linkedSignal(() => new MatTableDataSource(this.procureTimingResource.value()));

    loading = signal(false);
    mode = signal<'view' | 'edit' | 'add'>('add');
    search = new FormControl("");
    showGrid = signal(false);
    status = StatusType;

    list = signal<ProcureTimings>({
        id: 0,
        seasonName: "",
        noOfBags: 0
    });

    displayedColumns: string[] = [
        'actions',
        'status',
        'memoNo',
        'memoDate',
        'source',
        'destination',
        'regNo',
        'driverDet',
        'startDate',
        'endDate',
        'km'
    ];

    constructor() {
        effect(() => {
            if (this.paginator()) {
                this.dataSource().sort = this.sort()!;
                this.dataSource().paginator = this.paginator()!
            }
        });
    }

   

    onAdd() {
        this.list.set({
            id: 0,
            seasonName: "",
            noOfBags: 0
        });
        this.mode.set("add")
        this.showGrid.set(true)
    }

    onView(rowData: ProcureTimings) {
        this.list.set(rowData);
        this.mode.set('view');
        this.showGrid.set(true)
    }

    onEdit(rowData: ProcureTimings) {
        this.list.set(rowData);
        this.mode.set('edit');
        this.showGrid.set(true)
    }

    onClose() {
        this.showGrid.set(false)
        this.procureTimingResource.reload()
        this.search.setValue("");
    }

    onRefresh() {
        this.procureTimingResource.reload()
        this.search.setValue("");
    }

    async onDelete(rowData: ProcureTimings) {
        await confirmAndDelete(rowData, rowData.seasonName, 'Procurement Timing', this.procureTimingService, () => this.procureTimingResource.reload());
    }

    onSearch() {
        const filterValue = this.search.value?.trim().toLowerCase() || '';
        this.dataSource().filter = filterValue;
    }

}
