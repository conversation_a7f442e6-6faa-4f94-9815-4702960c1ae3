<div class="leave-calendar-container">
  <mat-card class="header-card">
    <mat-card-header class="form-header">
      <div class="header-content">
        <div class="logo-section">
          <mat-icon class="tncsc-logo">event_available</mat-icon>
        </div>
        <div class="title-section">
          <h2 class="form-subtitle">Holiday Calendar</h2>
          <p class="subtitle">{{selectedYear}} holiday calendar</p>
        </div>
      </div>
    </mat-card-header>
  </mat-card>

    <div class="years">
        <mat-form-field>
          <mat-label>Select Year</mat-label>
          <mat-select (selectionChange)="onYearChange($event)" [(value)]="selectedYear">
            <mat-option value="2025">2025</mat-option>
            <mat-option value="2024">2024</mat-option>
          </mat-select>
        </mat-form-field>
        
        
    </div>
  
    <div class="month-grid" >
       
        <mat-card *ngFor="let month of months; let i = index" class="month-card" (click)="openMonthDialog(i)">
        <mat-card-content>
          <div class="month-content">
            <div class="month-header">
              <span class="month-name">{{ month.name }} 2025</span>
            </div>
            <div class="holiday-list">
              <div *ngFor="let holiday of month.holidays" class="holiday-item">
                <span class="holiday-date">{{ holiday.date }}</span>
                <span class="holiday-day">{{ holiday.day }}</span>
                <span class="holiday-name">{{ holiday.name }}</span>
              </div>
              <div *ngIf="!month.holidays.length" class="no-holidays " style="text-align: center;">
                No public holidays
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>