import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { GunnyForm, GuunyResponse } from '../../../models/x_models/masters/gunny';

@Injectable({
    providedIn: 'root'
})
export class GunnyService {
    dataService = inject(DataService)

    create(data: GunnyForm) {
        return this.dataService.post<Response>("/gunny", data)
    }

    gunnyDetails(data: any) {
        return this.dataService.post<Response>("/gunny/gunnydetails", data)
    }


    get() {
        return this.dataService.get<GuunyResponse>("/gunny")
    }

    getById(id: number) {
        return this.dataService.get<GunnyForm>(`/gunny/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/gunny/${id}`)
    }
}
