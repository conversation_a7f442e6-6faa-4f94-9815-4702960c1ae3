import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  Employee,
  Region,
  Department,
  MutualTransferRequest,
  TransferType,
  TransferStatus,
  Priority
} from '../../../enums/m_enums/transfer.model';
import { TransferDataService } from '../../../services/m_apis/transfer-data.service';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';

@Component({
  selector: 'app-mutual-transfer',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatCheckboxModule,
    MatSnackBarModule
  ],
  templateUrl: './mutual-transfer.component.html',
  styleUrls: ['./mutual-transfer.component.scss']
})
export class MutualTransferComponent implements OnInit {

  transferForm!: FormGroup;
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  regions: Region[] = [];
  departments: Department[] = [];
  currentEmployee: Employee | null = null;
  selectedEmployeeB: Employee | null = null;
  isLoading = false;
  minDate = new Date();

  constructor(
    private fb: FormBuilder,
    private transferService: TransferDataService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadData();
  }

  private initializeForm(): void {
    this.transferForm = this.fb.group({
      // Employee A (Current User) Information
      employeeAName: ['', Validators.required],
      employeeACode: ['', Validators.required],
      employeeARegion: ['', Validators.required],
      employeeADepartment: ['', Validators.required],
      employeeAPosition: ['', Validators.required],
      employeeAGrade: ['', Validators.required],

      // Search and Filter
      employeeSearch: [''],
      filterRegion: [''],
      filterDepartment: [''],

      // Employee B Selection
      employeeB: ['', Validators.required],

      // Transfer Details
      priority: ['MEDIUM', Validators.required],
      preferredJoiningDate: [''],

      // Reason and Agreement
      reason: ['', [Validators.required, Validators.minLength(10)]],
      justification: [''],
      agreementConfirmed: [false, Validators.requiredTrue],
      termsAccepted: [false, Validators.requiredTrue],
      documentationComplete: [false, Validators.requiredTrue]
    });
  }

  private loadData(): void {
    this.isLoading = true;

    // Load current employee data
    this.transferService.getCurrentEmployee().subscribe(employee => {
      this.currentEmployee = employee;
      this.populateCurrentEmployeeData(employee);
    });

    // Load all employees (excluding current user)
    this.transferService.getEmployees().subscribe(employees => {
      this.employees = employees.filter(emp => emp.id !== this.currentEmployee?.id);
      this.filteredEmployees = [...this.employees];
    });

    // Load regions
    this.transferService.getRegions().subscribe(regions => {
      this.regions = regions;
    });

    // Load departments
    this.transferService.getDepartments().subscribe(departments => {
      this.departments = departments;
      this.isLoading = false;
    });
  }

  private populateCurrentEmployeeData(employee: Employee): void {
    const currentRegion = this.regions.find(r => r.id === employee.region);
    const currentDept = this.departments.find(d => d.id === employee.department);

    this.transferForm.patchValue({
      employeeAName: employee.name,
      employeeACode: employee.employeeCode,
      employeeARegion: currentRegion?.name || employee.region,
      employeeADepartment: currentDept?.name || employee.department,
      employeeAPosition: employee.position,
      employeeAGrade: employee.currentGrade
    });
  }

  onEmployeeSearch(event: any): void {
    const searchTerm = event.target.value.toLowerCase();
    this.filterEmployees(searchTerm);
  }

  onRegionFilter(event: any): void {
    this.filterEmployees();
  }

  onDepartmentFilter(event: any): void {
    this.filterEmployees();
  }

  private filterEmployees(searchTerm: string = ''): void {
    const regionFilter = this.transferForm.get('filterRegion')?.value;
    const departmentFilter = this.transferForm.get('filterDepartment')?.value;
    const currentSearchTerm = searchTerm || this.transferForm.get('employeeSearch')?.value?.toLowerCase() || '';

    this.filteredEmployees = this.employees.filter(emp => {
      const matchesSearch = !currentSearchTerm ||
        emp.name.toLowerCase().includes(currentSearchTerm) ||
        emp.employeeCode?.toLowerCase().includes(currentSearchTerm) ||
        emp.region?.toLowerCase().includes(currentSearchTerm);

      const matchesRegion = !regionFilter || emp.region === regionFilter;
      const matchesDepartment = !departmentFilter || emp.department === departmentFilter;

      return matchesSearch && matchesRegion && matchesDepartment;
    });
  }

  onEmployeeBSelect(event: any): void {
    const employeeId = event.value;
    this.selectedEmployeeB = this.employees.find(emp => emp.id === employeeId) || null;
  }

  getRegionName(regionId?: string): string {
    if (!regionId) return '';
    const region = this.regions.find(r => r.id === regionId);
    return region?.name || regionId;
  }

  getDepartmentName(departmentId?: string): string {
    if (!departmentId) return '';
    const department = this.departments.find(d => d.id === departmentId);
    return department?.name || departmentId;
  }

  allAgreementsConfirmed(): boolean {
    const form = this.transferForm;
    return form.get('agreementConfirmed')?.value &&
           form.get('termsAccepted')?.value &&
           form.get('documentationComplete')?.value;
  }

  onSubmit(): void {
    if (this.transferForm.valid && this.currentEmployee && this.selectedEmployeeB && this.allAgreementsConfirmed()) {
      this.isLoading = true;

      const formData = this.transferForm.value;

      const transferRequest: MutualTransferRequest = {
        id: '',
        requestNumber: '',
        employeeId: this.currentEmployee.id,
        employeeName: this.currentEmployee.name,
        transferType: TransferType.MUTUAL,
        status: TransferStatus.DRAFT,
        priority: formData.priority as Priority,

        // Current Details (Employee A)
        currentRegionId: this.currentEmployee.region || '',
        currentRegionName: this.transferForm.get('employeeARegion')?.value,
        currentDepartmentId: this.currentEmployee.department || '',
        currentDepartmentName: this.transferForm.get('employeeADepartment')?.value,
        currentPosition: this.currentEmployee.position,
        currentGrade: this.currentEmployee.currentGrade || '',

        // Proposed Details (Employee B's current location)
        proposedRegionId: this.selectedEmployeeB.region || '',
        proposedRegionName: this.getRegionName(this.selectedEmployeeB.region),
        proposedDepartmentId: this.selectedEmployeeB.department || '',
        proposedDepartmentName: this.getDepartmentName(this.selectedEmployeeB.department),
        proposedPosition: this.selectedEmployeeB.position,
        proposedGrade: this.selectedEmployeeB.currentGrade || '',

        // Mutual Transfer Specific
        mutualEmployeeId: this.selectedEmployeeB.id,
        mutualEmployeeName: this.selectedEmployeeB.name,
        mutualAgreementConfirmed: formData.agreementConfirmed,

        // Transfer Details
        reason: formData.reason,
        justification: formData.justification,
        preferredJoiningDate: formData.preferredJoiningDate,

        // Audit Fields
        submittedBy: this.currentEmployee.name,
        createdDate: new Date(),
        createdBy: this.currentEmployee.name
      };

      this.transferService.submitMutualTransfer(transferRequest).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.success) {
            this.snackBar.open(
              response.message || 'Mutual transfer request submitted successfully!',
              'Close',
              { duration: 5000, panelClass: ['success-snackbar'] }
            );
            this.router.navigate(['/transfer-dashboard']);
          } else {
            this.snackBar.open(
              response.message || 'Failed to submit transfer request',
              'Close',
              { duration: 5000, panelClass: ['error-snackbar'] }
            );
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error submitting transfer request:', error);
          this.snackBar.open(
            'An error occurred while submitting the request',
            'Close',
            { duration: 5000, panelClass: ['error-snackbar'] }
          );
        }
      });
    } else {
      this.markFormGroupTouched();
      let message = 'Please fill in all required fields correctly';

      if (!this.allAgreementsConfirmed()) {
        message = 'Please confirm all agreements before submitting';
      } else if (!this.selectedEmployeeB) {
        message = 'Please select an employee for mutual transfer';
      }

      this.snackBar.open(message, 'Close', { duration: 3000, panelClass: ['warning-snackbar'] });
    }
  }

  saveDraft(): void {
    // Implementation for saving as draft
    this.snackBar.open('Draft saved successfully!', 'Close', { duration: 3000 });
  }

  resetForm(): void {
    this.transferForm.reset();
    this.selectedEmployeeB = null;
    this.filteredEmployees = [...this.employees];
    if (this.currentEmployee) {
      this.populateCurrentEmployeeData(this.currentEmployee);
    }
    this.transferForm.get('priority')?.setValue('MEDIUM');
    this.transferForm.get('agreementConfirmed')?.setValue(false);
    this.transferForm.get('termsAccepted')?.setValue(false);
    this.transferForm.get('documentationComplete')?.setValue(false);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.transferForm.get(fieldName);
    if (field?.hasError('required')) {
      return `${this.getFieldDisplayName(fieldName)} is required`;
    }
    if (field?.hasError('minlength')) {
      const minLength = field.errors?.['minlength']?.requiredLength;
      return `${this.getFieldDisplayName(fieldName)} must be at least ${minLength} characters`;
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      'reason': 'Reason for transfer',
      'employeeB': 'Partner employee',
      'priority': 'Priority level'
    };
    return fieldNames[fieldName] || fieldName;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.transferForm.controls).forEach(key => {
      const control = this.transferForm.get(key);
      control?.markAsTouched();
    });
  }
}