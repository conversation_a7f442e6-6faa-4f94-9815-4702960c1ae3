import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Employee } from '../../../enums/m_enums/transfer.model';
import { TransferDataService } from '../../../services/m_apis/transfer-data.service';
import { CommonModule } from '@angular/common';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatOption } from '@angular/material/select';


@Component({
  selector: 'app-mutual-transfer',
  imports:[CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatFormField,
    MatLabel,
    MatOption
  ],
  templateUrl: './mutual-transfer.component.html',
  styleUrls: ['./mutual-transfer.component.scss']
})
export class MutualTransferComponent implements OnInit {

  transferForm!: FormGroup;
  employees: Employee[] = [];

  constructor(
    private fb: FormBuilder,
    private transferService: TransferDataService
  ) {}

  ngOnInit(): void {
    this.transferForm = this.fb.group({
      employeeA: ['', Validators.required],
      employeeB: ['', Validators.required],
      agreementConfirmed: [false, Validators.requiredTrue]
    });

    this.transferService.getEmployees().subscribe(emps => this.employees = emps);
  }

  onSubmit(): void {
    if (this.transferForm.valid) {
      this.transferService.submitMutualTransfer(this.transferForm.value).subscribe(res => {
        alert('Mutual transfer submitted successfully!');
      });
    }
  }
}