import { Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { SchemeComponent } from './scheme.component';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { DatePipe, NgTemplateOutlet } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatOption, MatSelect } from '@angular/material/select';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { SchemeService } from '../../../../services/x_apis/masters/scheme.service';
import { Scheme, Schemes } from '../../../../models/x_models/masters/scheme';
import { AppRoutes } from '../../../../enums/app-routes';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-schemes',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatToolbarModule,
    MatOption,
    MatSelect,
    CloseFilterOnBlurDirective,
     TranslateModule,
    MatFormFieldModule,
    SchemeComponent
  ],
  templateUrl: './schemes.component.html',
  styleUrl: './schemes.component.scss'
})
export class SchemesComponent {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  schemeService = inject(SchemeService)
  schemeResource = resource({ loader: () => this.schemeService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.schemeResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  scheme = signal<Scheme>({
    id: 0,
    schemeCode: '',
    schemeName: '',
    description: '',
  });

  displayedColumns: string[] = [
    'schemeName',
    'schemeCode',
    'description',
    'actions'
  ];
  // schemes = signal<Schemes[]>([]);
  protected readonly AppRoutes = AppRoutes;  // Need clarification

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.scheme.set({
      id: 0,
      schemeCode: '',
      schemeName: '',
      description: '',
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(schemes: Scheme) {
    this.scheme.set(schemes);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(schemes: Scheme) {
    this.scheme.set(schemes);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.schemeResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.schemeResource.reload();
    this.search.setValue("");
  }

  async onDelete(scheme: Schemes) {
    await confirmAndDelete(scheme, scheme.schemeName, 'Scheme', this.schemeService, () => this.schemeResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
