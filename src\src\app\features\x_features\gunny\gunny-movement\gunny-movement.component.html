<!-- <div class="card"> -->

<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" class="nav-aln">
    <a routerLink="/" class="crumb">Gunny Management</a>
    <mat-icon>chevron_right</mat-icon>
    <a routerLink="/gunnys" class="crumb">Gunny Movement</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">Sacn Gunny QR Details</span>
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>


<div class="card component" style="margin-top:10px">



  <form [formGroup]="form">
    <div class="grid-container">
      <div class="row-3">
        <label for="fromStorageLocationId" class="required-label">{{'From' | translate }}</label>
        <mat-form-field appearance="outline">
          <mat-select id="fromStorageLocationId" formControlName="fromStorageLocationId" [disabled]="formDisable()">
            <mat-form-field class="select-search hide-subscript" appearance="outline">
              <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
              <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
            </mat-form-field>
            @for (role of storageType(); track role) {
            <mat-option [value]="role.key">{{role.value}}</mat-option>
            }
          </mat-select>
          <mat-error>
            @if(form.controls.fromStorageLocationId.errors?.['required']) {
            {{'From' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row-3">
        <label for="toStorageLocationId" class="required-label">{{'To' | translate }}</label>
        <mat-form-field appearance="outline">
          <mat-select id="toStorageLocationId" formControlName="toStorageLocationId" [disabled]="formDisable()">
            <mat-form-field class="select-search hide-subscript" appearance="outline">
              <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
              <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
            </mat-form-field>
            @for (role of storageType(); track role) {
            <mat-option [value]="role.key">{{role.value}}</mat-option>
            }
          </mat-select>
          <mat-error>
            @if(form.controls.toStorageLocationId.errors?.['required']) {
            {{'To' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row-6">
        <label for="qrCode" class="required-label">{{'ScanQR' | translate }}</label>
        <mat-form-field style="max-width: 100%;
    min-height: 70px;">
          <input id="qrCode" formControlName="qrCode" matInput maxlength="10" (keydown.enter)="onQrEnter()" style="color: #06064a;
    font-size: 30px;" #input1 (keydown.enter)="moveFocus(input1)">
          <mat-error>
            @if(form.controls.qrCode.errors?.['required']) {
            {{'ScanQR' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>
    </div>




    <div class="actions">

      <button mat-flat-button class="btn btn-theme" type="submit" (click)="onQrEnter()"><mat-icon
          color="primary">save</mat-icon>{{'Save' | translate}}</button>
    </div>



  </form>


  @if(showGrid()){

  <div class="page-header" style="color: darkslategray;">
    <h3>Scanned Gunny Details</h3>
  </div>

  @if(dataSource().filteredData.length > 0) {
  <div class="grid-container" style="margin-right: 10px;
        margin-left: 10px;">
    <div class="row-3" style="margin-bottom: 20px;">
      <div class="summary-item">
        <span class="subject-name">Total</span>
        <div class="badge">{{dataSource().filteredData.length}}</div>
      </div>
    </div>
    @for (test of qrAbstract(); track test) {
    <div class="row-3" style="margin-bottom: 20px;">
      <div class="summary-item">
        <span class="subject-name">{{test.gunny}}</span>
        <div class="badge">{{test.count}}</div>
      </div>
    </div>
    }
  </div>
  }

  <div class="content">
    <div class="table-wrapper">
      <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">

        <ng-container matColumnDef="gunnyName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'QRCode' | translate}} </th>
          <td mat-cell *matCellDef="let row"> {{row.gunnyName}} </td>
        </ng-container>

        <ng-container matColumnDef="gunnyQrCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Date' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.gunnyQrCode }} </td>
        </ng-container>

        <!-- <ng-container matColumnDef="fromDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'FromDate' | translate}} </th>
            <td mat-cell *matCellDef="let row"> {{row.fromDate | date :'dd-MM-yyyy' }} </td>
          </ng-container>
  
          <ng-container matColumnDef="toDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'ToDate' | translate}}</th>
            <td mat-cell *matCellDef="let row"> {{row.toDate | date :'dd-MM-yyyy' }} </td>
          </ng-container> -->

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef class="action"> {{ 'Actions' | translate }} </th>
          <td mat-cell *matCellDef="let row">

            <div class="table-controls">


              <button title="{{ 'Delete' | translate }}" (click)="onDelete(row)"
                class="btn-icon btn-delete btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
              </button>

            </div>

          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" [attr.colspan]="displayedColumns.length" style="text-align: center;padding:10px">
            No data found
          </td>
        </tr>
        <!-- <tr *matNoDataRow>
            <ng-container *ngTemplateOutlet="shimmer"></ng-container>
          </tr> -->
      </table>

    </div>

    <mat-paginator class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
      aria-label="Select page of users"></mat-paginator>

    <div class="mobile-wrapper">
      <div class="un-cards">
        @for (item of dataSource().data; track item) {
        <div class="un-card">
          <div class="desc">
            <div class="quote-no">{{item.gunnyName}}</div>
            <div style="line-height: 1em;"><span class="tracking-no">{{item.gunnyQrCode}}</span> </div>
            <!-- <div class="quote-no">{{item.fromDate | date}}</div>
              <div class="quote-no">{{item.toDate | date }}</div> -->
          </div>
          <div class="actions">
            <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu" aria-label="Card actions">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">

              <button (click)="onDelete(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
                <span>{{ 'Delete' | translate }}</span>
              </button>

            </mat-menu>
          </div>
        </div>
        }
      </div>
    </div>
  </div>
  }



</div>
<!-- </div> -->