<div class="component card">

    <div class="page-header">
        <h1>{{'VehicleTracking' | translate}}</h1>
    </div>

    <div class="header">

        <form [formGroup]="form">
            <div class="grid-container">
                <div class="row-4">
                    <label for="regionId" class="required-label">{{ 'Region' | translate }}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="regionId" formControlName="regionId">
                            @for (role of regions(); track role) {
                            <mat-option [value]="role.key">{{ role.value }}</mat-option>
                            }
                        </mat-select>
                    </mat-form-field>
                </div>
                <div class="row-8">
                    @if(!noData()) {
                        <div class="actions">
                            <button mat-flat-button color="accent" (click)="onMapFullView(showGrid() ? 1 : 2)"
                                style="text-align:center;padding:10px;border-radius:5px;">
                                <mat-icon>map</mat-icon>
                                {{ (showGrid() ? 'MapView' : 'GridView') | translate }}
                            </button>
                        </div>
                    }
                </div>
            </div>
        </form>

        @if (showGrid() || mapId() === 1) {
        <div class="card-container" style="margin-bottom:20px">
            <mat-card *ngFor="let card of vehicleCards">
                <mat-card-content class="card-content">
                    <div class="row-8">
                        <div class="text-content">
                            <mat-card-title style="font-size:15px;">{{ card.title }}</mat-card-title>
                            <p style="font-size:25px;font-weight:bold">{{ card.total }}</p>
                        </div>
                    </div>
                    <div class="row-4">
                        <img class="card-image" [src]="card.imageUrl" [alt]="card.title">
                    </div>
                </mat-card-content>
            </mat-card>
        </div>
        }

        @if (showGrid()) {
        <div class="filters-wrapper" style="margin-bottom:20px">
            <mat-form-field class="search hide-subscript" appearance="outline">
                <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
                <input id="search" [formControl]="search" (input)="onSearch()" autocomplete="off" matInput
                    placeholder="{{ 'Search' | translate }}">
            </mat-form-field>
            <div class="filters-more">
                <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
                    <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
                </button>
            </div>
        </div>
        }

    </div>

    @if (showGrid()) {

    <div class="content">
        <div class="table-wrapper">

            <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">

                <ng-container matColumnDef="regionName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'Region' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.regionName}} </td>
                </ng-container>

                <ng-container matColumnDef="vehicleStatusTypeName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'VehicleStatus' | translate }}</th>
                    <td mat-cell *matCellDef="let row">
                        <span class="status-container">
                            {{row.vehicleStatusTypeName}}
                            <img [src]="getVehicleStatusImage(row.vehicleStatusType)"
                                [alt]="getVehicleStatusAltText(row.vehicleStatusType)" class="status-icon" />
                        </span>
                    </td>
                </ng-container>

                <ng-container matColumnDef="regNo">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'RegisterNo' | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{row.regNo}} </td>
                </ng-container>

                <ng-container matColumnDef="gpsDeviceTs">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'LastUpdated' | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{row.gpsDeviceTs | date:'dd-MM-yyyy hh:mm:ss a' }} </td>
                </ng-container>

                <ng-container matColumnDef="mainPowerStatusType">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'PowerStatus' | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{row.mainPowerStatusType}} </td>
                </ng-container>

                <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef class="action"> {{ 'Actions' | translate }} </th>
                    <td mat-cell *matCellDef="let row">
                        <div class="table-controls">
                            @if (access()?.canView) {
                            <button title="{{ 'View' | translate }}" (click)="onMapView(row)"
                                class="btn-icon btn-icon-unstyled ">
                                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                            </button>
                            }

                        </div>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" [attr.colspan]="displayedColumns.length" style="text-align: center;padding:10px">
                      No data found
                    </td>
                  </tr>
            </table>

        </div>
        <!-- @if (dataSource().filteredData.length==0) {
        <div style="text-align: center;">No Data</div>

        } -->
        <mat-paginator class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
            aria-label="Select page of users"></mat-paginator>

        <div class="mobile-wrapper">
            <div class="un-cards">
                @for (item of dataSource().data; track item) {
                <div class="un-card">
                    <div class="desc">
                        <div class="quote-no">{{item.regNo}}&nbsp;-&nbsp;{{item.regionName }}</div>
                        <div class="quote-no">{{item.vehicleStatusTypeName}}&nbsp;-&nbsp;{{item.gpsDeviceTs }}</div>
                    </div>
                    <div class="actions">
                        <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu"
                            aria-label="Card actions">
                            <mat-icon>more_vert</mat-icon>
                        </button>
                        <mat-menu #menu="matMenu">

                            @if (access()?.canView) {
                            <button (click)="onMapView(item)" mat-menu-item>
                                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                                <span>{{ 'View' | translate }}</span>
                            </button>
                            }

                        </mat-menu>
                    </div>
                </div>
                }
            </div>
        </div>


    </div>
    } @else if(mapView()){
    <app-livetrack-map [vehicleData]="vehicleDetails()" [mapId]="mapId()" (closed)="onClose()"></app-livetrack-map>
    <!-- <app-livetrack-map [mapId]="mapId()" (closed)="onClose()"></app-livetrack-map> -->
    }
</div>