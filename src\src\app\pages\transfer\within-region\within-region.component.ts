import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Department, Region } from '../../../enums/m_enums/transfer.model';
import { TransferDataService } from '../../../services/m_apis/transfer-data.service';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import {MatFormFieldModule} from '@angular/material/form-field';
    import { MatSelectModule } from '@angular/material/select';

@Component({
  selector: 'app-within-region',
  imports:[CommonModule,ReactiveFormsModule,FormsModule,MatMenuModule,MatFormFieldModule,MatSelectModule],
  templateUrl: './within-region.component.html',
  styleUrls: ['./within-region.component.scss']
})
export class WithinRegionComponent implements OnInit {

  transferForm!: FormGroup;
  departments: Department[] = [];
  regions: Region[] = [];

  constructor(
    private fb: FormBuilder,
    private transferService: TransferDataService
  ) {}

  ngOnInit(): void {
    this.transferForm = this.fb.group({
      currentPosition: ['', Validators.required],
      proposedDepartment: ['', Validators.required],
      reason: ['', [Validators.required, Validators.minLength(10)]]
    });

    this.transferService.getDepartments().subscribe(depts => this.departments = depts);
    this.transferService.getRegions().subscribe(regions => this.regions = regions);
  }

  onSubmit(): void {
    if (this.transferForm.valid) {
      this.transferService.submitWithinRegionTransfer(this.transferForm.value).subscribe(res => {
        console.log(res);
        
        alert('Transfer request submitted successfully!');
      });
    }
  }
}