import { Component, OnInit, signal } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  Department,
  Region,
  Employee,
  Position,
  WithinRegionTransferRequest,
  TransferType,
  TransferStatus,
  Priority
} from '../../../enums/m_enums/transfer.model';
import { TransferDataService } from '../../../services/m_apis/transfer-data.service';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';

@Component({
  selector: 'app-within-region',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatMenuModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatSnackBarModule
  ],
  templateUrl: './within-region.component.html',
  styleUrls: ['./within-region.component.scss']
})
export class WithinRegionComponent implements OnInit {

  transferForm!: FormGroup;
  departments: Department[] = [];
  regions: Region[] = [];
  availablePositions: Position[] = [];
  currentEmployee: Employee | null = null;
  isLoading = false;
  minDate = new Date();

  constructor(
    private fb: FormBuilder,
    private transferService: TransferDataService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadData();
  }

  private initializeForm(): void {
    this.transferForm = this.fb.group({
      // Current Information (readonly)
      employeeName: ['', Validators.required],
      employeeCode: ['', Validators.required],
      currentRegion: ['', Validators.required],
      currentDepartment: ['', Validators.required],
      currentPosition: ['', Validators.required],
      currentGrade: ['', Validators.required],

      // Transfer Request Details
      proposedDepartment: ['', Validators.required],
      proposedPosition: [''],
      priority: ['MEDIUM', Validators.required],
      preferredJoiningDate: [''],

      // Reason and Justification
      reason: ['', [Validators.required, Validators.minLength(10)]],
      justification: ['']
    });
  }

  private loadData(): void {
    this.isLoading = true;

    // Load current employee data
    this.transferService.getCurrentEmployee().subscribe(employee => {
      this.currentEmployee = employee;
      this.populateCurrentEmployeeData(employee);
    });

    // Load departments
    this.transferService.getDepartments().subscribe(depts => {
      this.departments = depts.filter(dept => dept.id !== this.currentEmployee?.department);
    });

    // Load regions
    this.transferService.getRegions().subscribe(regions => {
      this.regions = regions;
      this.isLoading = false;
    });
  }

  private populateCurrentEmployeeData(employee: Employee): void {
    const currentRegion = this.regions.find(r => r.id === employee.region);
    const currentDept = this.departments.find(d => d.id === employee.department);

    this.transferForm.patchValue({
      employeeName: employee.name,
      employeeCode: employee.employeeCode,
      currentRegion: currentRegion?.name || employee.region,
      currentDepartment: currentDept?.name || employee.department,
      currentPosition: employee.position,
      currentGrade: employee.currentGrade
    });
  }

  onDepartmentChange(event: any): void {
    const departmentId = event.value;
    if (departmentId) {
      this.transferService.getPositionsByDepartment(departmentId).subscribe(positions => {
        this.availablePositions = positions;
        this.transferForm.get('proposedPosition')?.setValue('');
      });
    } else {
      this.availablePositions = [];
    }
  }

  onSubmit(): void {
    if (this.transferForm.valid && this.currentEmployee) {
      this.isLoading = true;

      const formData = this.transferForm.value;
      const proposedDept = this.departments.find(d => d.id === formData.proposedDepartment);
      const proposedPos = this.availablePositions.find(p => p.id === formData.proposedPosition);

      const transferRequest: WithinRegionTransferRequest = {
        id: '',
        requestNumber: '',
        employeeId: this.currentEmployee.id,
        employeeName: this.currentEmployee.name,
        transferType: TransferType.WITHIN_REGION,
        status: TransferStatus.DRAFT,
        priority: formData.priority as Priority,

        // Current Details
        currentRegionId: this.currentEmployee.region || '',
        currentRegionName: this.transferForm.get('currentRegion')?.value,
        currentDepartmentId: this.currentEmployee.department || '',
        currentDepartmentName: this.transferForm.get('currentDepartment')?.value,
        currentPosition: this.currentEmployee.position,
        currentGrade: this.currentEmployee.currentGrade || '',

        // Proposed Details
        proposedRegionId: this.currentEmployee.region || '', // Same region
        proposedRegionName: this.transferForm.get('currentRegion')?.value,
        proposedDepartmentId: formData.proposedDepartment,
        proposedDepartmentName: proposedDept?.name || '',
        proposedPosition: proposedPos?.title || '',
        proposedGrade: proposedPos?.grade || '',

        // Transfer Details
        reason: formData.reason,
        justification: formData.justification,
        preferredJoiningDate: formData.preferredJoiningDate,

        // Audit Fields
        submittedBy: this.currentEmployee.name,
        createdDate: new Date(),
        createdBy: this.currentEmployee.name
      };

      this.transferService.submitWithinRegionTransfer(transferRequest).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.success) {
            this.snackBar.open(
              response.message || 'Transfer request submitted successfully!',
              'Close',
              { duration: 5000, panelClass: ['success-snackbar'] }
            );
            this.router.navigate(['/transfer-dashboard']);
          } else {
            this.snackBar.open(
              response.message || 'Failed to submit transfer request',
              'Close',
              { duration: 5000, panelClass: ['error-snackbar'] }
            );
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error submitting transfer request:', error);
          this.snackBar.open(
            'An error occurred while submitting the request',
            'Close',
            { duration: 5000, panelClass: ['error-snackbar'] }
          );
        }
      });
    } else {
      this.markFormGroupTouched();
      this.snackBar.open(
        'Please fill in all required fields correctly',
        'Close',
        { duration: 3000, panelClass: ['warning-snackbar'] }
      );
    }
  }

  saveDraft(): void {
    // Implementation for saving as draft
    this.snackBar.open('Draft saved successfully!', 'Close', { duration: 3000 });
  }

  resetForm(): void {
    this.transferForm.reset();
    this.availablePositions = [];
    if (this.currentEmployee) {
      this.populateCurrentEmployeeData(this.currentEmployee);
    }
    this.transferForm.get('priority')?.setValue('MEDIUM');
  }

  getErrorMessage(fieldName: string): string {
    const field = this.transferForm.get(fieldName);
    if (field?.hasError('required')) {
      return `${this.getFieldDisplayName(fieldName)} is required`;
    }
    if (field?.hasError('minlength')) {
      const minLength = field.errors?.['minlength']?.requiredLength;
      return `${this.getFieldDisplayName(fieldName)} must be at least ${minLength} characters`;
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      'reason': 'Reason for transfer',
      'proposedDepartment': 'Proposed department',
      'priority': 'Priority level'
    };
    return fieldNames[fieldName] || fieldName;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.transferForm.controls).forEach(key => {
      const control = this.transferForm.get(key);
      control?.markAsTouched();
    });
  }
}