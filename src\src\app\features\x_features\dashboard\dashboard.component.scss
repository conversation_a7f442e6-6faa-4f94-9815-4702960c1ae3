* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Arial', sans-serif;
    background: #f5f5f7;
  }
  
  .container {
    display: flex;
    min-height: 100vh;
  }
  
  /* Sidebar */
  .sidebar {
    width: 240px;
    background-color: #5b21b6;
    color: #fff;
    padding: 30px 20px;
    border-top-right-radius: 25px;
    border-bottom-right-radius: 25px;
  }
  
  .logo {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 40px;
  }
  
  .menu {
    display: flex;
    flex-direction: column;
  }
  
  .menu-item {
    color: #fff;
    text-decoration: none;
    margin-bottom: 20px;
    padding: 10px;
    border-radius: 12px;
    transition: background 0.3s;
  }
  
  .menu-item:hover,
  .menu-item.active {
    background-color: #7c3aed;
  }
  
  /* Main */
  .main {
    flex: 1;
    padding: 30px;
  }
  
  /* Header */
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
  }
  
  .username {
    font-weight: 500;
  }
  
  .notif {
    position: relative;
    font-size: 20px;
  }
  
  .badge {
    background: red;
    color: white;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 50%;
    position: absolute;
    top: -5px;
    right: -10px;
  }
  
  /* Search */
  .search-bar {
    display: flex;
    margin-bottom: 20px;
  }
  
  .search-bar input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 10px 0 0 10px;
    outline: none;
  }
  
  .search-bar button {
    background: #5b21b6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 0 10px 10px 0;
    cursor: pointer;
  }
  
  /* Filters */
  .filters {
    margin-bottom: 30px;
  }
  
  .chip {
    display: inline-block;
    background: #e0d4f3;
    color: #5b21b6;
    padding: 6px 12px;
    border-radius: 20px;
    margin-right: 10px;
    cursor: pointer;
    font-size: 14px;
  }
  
  .chip.active {
    background: #5b21b6;
    color: white;
  }
  
  /* Cards */
  .cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 20px;
  }
  
  .card {
    background: white;
    padding: 20px;
    border-radius: 16px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .card-header h2 {
    font-size: 18px;
    font-weight: bold;
  }
  
  .card p {
    margin: 5px 0;
    font-size: 14px;
    color: #555;
  }
  
  .card .location {
    color: #999;
    font-size: 13px;
  }
  