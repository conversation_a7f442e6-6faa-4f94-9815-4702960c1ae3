import { ValidatorFn, AbstractControl, ValidationErrors } from "@angular/forms";

export function validPhoneNumber(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value.replace(/[^0-9]/g, '');

    const notRequiredLength = value.length !== 9
    const sameDigits = /^(\d)\1{8}$/.test(value)
    const inSequence = /^(123456789|987654321|876543210|234567890)$/.test(value)

    if (value.length) {
      if (notRequiredLength) {
        return {notRequiredLength: true}
      } else if (sameDigits) {
        return {sameDigits: true}
      } else if (inSequence) {
        return {inSequence: true}
      }
      return null
    }
    return null
  }
}

export function validEmail(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    const validEmail = /^\S+@\S+\.\S+$/.test(value)

    if (value.length) {
      if (!validEmail) {
        return {invalid: true}
      }
    }
    return null
  }
}

export function matchPasswords(password: string, confirmPassword: string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const passwordValue = control.get(password)?.value;
    const confirmPasswordValue = control.get(confirmPassword)?.value;
    return passwordValue === confirmPasswordValue ? null : { passwordMismatch: true };
  };
}

export function currencyValidator(control: AbstractControl): ValidationErrors | null {
  const value = control.value;

  if (!Number(value.replace(".", "").replaceAll(",", ""))) {
    return { invalidCurrency: true }
  }

  return null; // Validation passes
}
