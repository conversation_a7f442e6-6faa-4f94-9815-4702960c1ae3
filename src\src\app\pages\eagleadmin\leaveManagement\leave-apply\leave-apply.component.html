<div class="leave-management-container">
    <!-- Navigation Bar -->
    <div class="nav-bar">
      <button
        mat-raised-button
        class="nav-button"
        [class.active]="activeView === 'apply'"
        (click)="setView('apply')"
      >
        <mat-icon>assignment</mat-icon>
        Leave Apply
      </button>
      <button
        mat-raised-button
        class="nav-button"
        [class.active]="activeView === 'pending'"
        (click)="setView('pending')"
      >
        <mat-icon>hourglass_empty</mat-icon>
        Pending Applied Leave
      </button>
      <button
        mat-raised-button
        class="nav-button"
        [class.active]="activeView === 'history'"
        (click)="setView('history')"
      >
        <mat-icon>history</mat-icon>
        History of Leaves
      </button>
    </div>
  
    <!-- Leave Apply View -->
    <div class="leave-apply-container" *ngIf="activeView === 'apply'">
      <mat-card class="apply-card">
        <div class="header">
          <h1 class="page-title">
            <mat-icon>assignment</mat-icon>
            Leave Application
          </h1>
          <p class="subtitle">Apply for your leave with ease</p>
        </div>
    
        <!-- Leave Balances Section -->
        <div class="leave-balances">
          <div class="balance-card" *ngFor="let balance of leaveBalances">
            <mat-icon>
              {{ getIconForLeaveType(balance.leaveTypeCode) }}
            </mat-icon>
            <div class="balance-info">
              <span class="balance-label">{{ balance.leaveTypeName }}</span>
              <span class="balance-value">{{ balance.totalBalance }} days</span>
            </div>
          </div>
        </div>
        
        
    
        <mat-card-content>
          <div class="form-container">
            <!-- Calculated Days Display -->
            <div class="days-calculation" *ngIf="calculatedDays !== null">
              <mat-card class="days-card">
                <mat-icon>schedule</mat-icon>
                <div class="days-info">
                  <span class="days-label">Total Leave Duration</span>
                  <span class="days-value">{{ calculatedDays }} {{ calculatedDays === 0.5 ? 'day' : 'days' }}</span>
                </div>
              </mat-card>
            </div>
    
            <form [formGroup]="leaveForm" (ngSubmit)="onSubmit()">
              <!-- Leave Type Selection -->
              <div class="form-section">
                <mat-form-field appearance="outline" class="form-field highlight-leave-type">
                  <mat-label>Leave Type</mat-label>
                  <mat-select formControlName="leaveType" required (selectionChange)="updateDaysCalculation()">
                    <mat-option *ngFor="let leaveType of leaveTypes" [value]="leaveType.leaveTypeId">{{ leaveType.leaveTypeName }}</mat-option>
                  </mat-select>
                  <mat-error *ngIf="leaveForm.get('leaveType')?.hasError('required')">Leave type is required</mat-error>
                </mat-form-field>
              </div>
    
              <!-- Date and Session Selection -->
              <div class="form-section date-session-grid">
                <mat-form-field appearance="outline" class="form-field highlight-date tight-spacing">
                  <mat-label>From Date</mat-label>
                  <input matInput [matDatepicker]="fromPicker" formControlName="fromDate" required (dateChange)="updateDaysCalculation()">
                  <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
                  <mat-datepicker #fromPicker></mat-datepicker>
                  <mat-error *ngIf="leaveForm.get('fromDate')?.hasError('required')">From date is required</mat-error>
                </mat-form-field>
    
                <mat-form-field appearance="outline" class="form-field highlight-session tight-spacing">
                  <mat-label>From Session</mat-label>
                  <mat-select formControlName="fromSession" (selectionChange)="updateDaysCalculation()">
                    <mat-option value="FIRST_HALF">Session 1 (First Half)</mat-option>
                    <mat-option value="SECOND_HALF">Session 2 (Second Half)</mat-option>
                  </mat-select>
                  <!-- <mat-error *ngIf="leaveForm.get('fromSession')?.hasError('required')">Session is required</mat-error> -->
                </mat-form-field>
    
                <mat-form-field appearance="outline" class="form-field highlight-date tight-spacing">
                  <mat-label>To Date</mat-label>
                  <input matInput [matDatepicker]="toPicker" formControlName="toDate" required (dateChange)="updateDaysCalculation()">
                  <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
                  <mat-datepicker #toPicker></mat-datepicker>
                  <mat-error *ngIf="leaveForm.get('toDate')?.hasError('required')">To date is required</mat-error>
                </mat-form-field>
    
                <mat-form-field appearance="outline" class="form-field highlight-session tight-spacing">
                  <mat-label>To Session</mat-label>
                  <mat-select formControlName="toSession" (selectionChange)="updateDaysCalculation()">
                    <mat-option value="FIRST_HALF">Session 1 (First Half)</mat-option>
                    <mat-option value="SECOND_HALF">Session 2 (Second Half)</mat-option>
                  </mat-select>
                  <!-- <mat-error *ngIf="leaveForm.get('toSession')?.hasError('required')">Session is required</mat-error> -->
                </mat-form-field>
              </div>
    
              <!-- Assigned Approver -->
              <div class="form-section">
                <mat-form-field appearance="outline" class="form-field highlight-approver">
                  <mat-label>Assigned Person to Approve</mat-label>
                  <mat-select formControlName="approver" required>
                    <mat-option *ngFor="let person of approvers" [value]="person.id">{{person.name}}</mat-option>
                  </mat-select>
                  <mat-error *ngIf="leaveForm.get('approver')?.hasError('required')">Approver is required</mat-error>
                </mat-form-field>
              </div>
    
              <!-- Reason for Leave -->
               <div class=" row">
                <div class="col-md-6">
              <!-- <div class="form-section textarea-field full-width"> -->
                <div>
                <mat-form-field appearance="outline">
                  <mat-label>Reason for Leave</mat-label>
                  <textarea matInput formControlName="reason" rows="3" required></textarea>
                  <mat-error *ngIf="leaveForm.get('reason')?.hasError('required')">Reason is required</mat-error>
                </mat-form-field>
              </div>
            </div>
              </div>    
              <!-- File Attachment -->
              <div class="form-section file-upload-section">
                <div class="file-upload highlight-file">
                  <input
                    type="file"
                    #fileInput
                  
                    (change)="onFileSelected($event)"
                    accept=".pdf,.doc,.docx,.jpg,.png"
                    style="display: none;"
                  >
                  <div class="file-upload-wrapper">
                    <div *ngIf="!selectedFileName" class="upload-icon" (click)="fileInput.click()">
                      <mat-icon color="primary">attach_file</mat-icon>
                      <span>Attach File - (Upload PDF, DOC, DOCX, JPG, PNG)</span>
                    </div>
                    <div *ngIf="selectedFileName" class="file-info">
                      <mat-icon color="accent">attach_file</mat-icon>
                      <span class="file-name">{{ selectedFileName }}</span>
                    </div>
                  </div>
                </div>
              </div>
              
    
              <!-- Submit Button -->
              <div class="form-section submit-section">
                <button mat-raised-button color="primary" type="submit" [disabled]="leaveForm.invalid">
                  Submit Application
                </button>
                <button mat-stroked-button type="button" class="reset-btn" (click)="leaveForm.reset(); calculatedDays = null">
                  Reset
                </button>
              </div>
              <div *ngIf="errorMessage" class="mt-4 text-red-600 text-center">
                {{ errorMessage }}
              </div>
              <div *ngIf="successMessage" class="mt-4 text-green-600 text-center">
                {{ successMessage }}
              </div>
            </form>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
    
      <!-- Pending Applied Leave View -->
      <div class="leave-table-container" *ngIf="activeView === 'pending'">
        <mat-card class="table-card">
          <div class="header">
            <h1 class="page-title">
              <mat-icon>hourglass_empty</mat-icon>
              Pending Applied Leave
            </h1>
            <p class="subtitle">View your pending leave applications</p>
          </div>
          <mat-card-content>
            <table mat-table [dataSource]="pendingLeaves" class="leave-table">
              <ng-container matColumnDef="leaveType">
                <th mat-header-cell *matHeaderCellDef>Leave Type</th>
                <td mat-cell *matCellDef="let leave">{{ leave.leaveTypeName }}</td>
              </ng-container>
              <ng-container matColumnDef="fromDate">
                <th mat-header-cell *matHeaderCellDef>From Date</th>
                <td mat-cell *matCellDef="let leave">{{ leave.fromDate | date:'dd MMM yyyy' }}</td>
              </ng-container>
              <ng-container matColumnDef="toDate">
                <th mat-header-cell *matHeaderCellDef>To Date</th>
                <td mat-cell *matCellDef="let leave">{{ leave.toDate | date:'dd MMM yyyy' }}</td>
              </ng-container>
              <ng-container matColumnDef="reason">
                <th mat-header-cell *matHeaderCellDef>Reason</th>
                <td mat-cell *matCellDef="let leave">{{ leave.reason }}</td>
              </ng-container>
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let leave" [ngStyle]="{ color: onHistoryColor(leave.status) }">{{ leave.status }}</td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
            <div style="text-align: center;background-color: #a00808;" *ngIf="pendingLeaves.length === 0" class="no-data">
              No pending leave applications
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    
      <!-- History of Leaves View -->
      <div class="leave-table-container" *ngIf="activeView === 'history'">
        <mat-card class="table-card">
          <div class="header">
            <h1 class="page-title">
              <mat-icon>history</mat-icon>
              History of Leaves
            </h1>
            <p class="subtitle">View your leave history</p>
          </div>
          <mat-card-content>
            <table mat-table [dataSource]="leaveHistory" class="leave-table">
              <ng-container matColumnDef="leaveType">
                <th mat-header-cell *matHeaderCellDef>Leave Type</th>
                <td mat-cell *matCellDef="let leave">{{ leave.leaveTypeName }}</td>
              </ng-container>
              <ng-container matColumnDef="fromDate">
                <th mat-header-cell *matHeaderCellDef>From Date</th>
                <td mat-cell *matCellDef="let leave">{{ leave.fromDate | date:'dd MMM yyyy' }}</td>
              </ng-container>
              <ng-container matColumnDef="toDate">
                <th mat-header-cell *matHeaderCellDef>To Date</th>
                <td mat-cell *matCellDef="let leave">{{ leave.toDate | date:'dd MMM yyyy' }}</td>
              </ng-container>
              <ng-container matColumnDef="reason">
                <th mat-header-cell *matHeaderCellDef>Reason</th>
                <td mat-cell *matCellDef="let leave">{{ leave.reason }}</td>
              </ng-container>
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let leave" [ngStyle]="{ color: onHistoryColor(leave.status) }">{{ leave.status }}</td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
            <div *ngIf="leaveHistory.length === 0" class="no-data">
              No leave history available
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
