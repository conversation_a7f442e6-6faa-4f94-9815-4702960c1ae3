export interface ProcurePricecut {
    id: number,
    procurementPriceCutType: number,
    productId: number,
    seasonId: number,
    regionId: number,
    minRange: number,
    maxRange: number,
    priceCutAmount: number
}

export interface ProcurePricecuts {
    id: number,
    procurementPriceCutTypeName: string,
    productName: string,
    seasonName: string,
    regionName: string,
    minRange: number,
    maxRange: number,
    priceCutAmount: number
}

export type ProcurePricecutResponse = ProcurePricecuts[];