<svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_824_689)">
<g filter="url(#filter0_f_824_689)">
<circle cx="4" cy="4" r="3.7" stroke="#CC0000" stroke-width="0.6"/>
</g>
<circle cx="4" cy="4" r="2.84375" fill="#CC0000"/>
<g clip-path="url(#clip1_824_689)">
<path d="M4.28359 4.00078L5.14219 3.14219C5.22031 3.06406 5.22031 2.9375 5.14219 2.85938C5.06406 2.78125 4.9375 2.78125 4.85938 2.85938L4.00078 3.71797L3.14219 2.85938C3.06406 2.78125 2.9375 2.78125 2.85938 2.85938C2.78125 2.9375 2.78125 3.06406 2.85938 3.14219L3.71797 4.00078L2.85938 4.85938C2.78125 4.9375 2.78125 5.06406 2.85938 5.14219C2.89844 5.18125 2.94961 5.20078 3.00078 5.20078C3.05195 5.20078 3.10313 5.18125 3.14219 5.14219L4.00078 4.28359L4.85938 5.14219C4.89844 5.18125 4.94961 5.20078 5.00078 5.20078C5.05195 5.20078 5.10313 5.18125 5.14219 5.14219C5.22031 5.06406 5.22031 4.9375 5.14219 4.85938L4.28359 4.00078Z" fill="white" stroke="white" stroke-width="0.1"/>
</g>
</g>
<defs>
<filter id="filter0_f_824_689" x="-0.5" y="-0.5" width="9" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.25" result="effect1_foregroundBlur_824_689"/>
</filter>
<clipPath id="clip0_824_689">
<rect width="8" height="8" fill="white"/>
</clipPath>
<clipPath id="clip1_824_689">
<rect width="4" height="4" fill="white" transform="translate(2 2)"/>
</clipPath>
</defs>
</svg>
