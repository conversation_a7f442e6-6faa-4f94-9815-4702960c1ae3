import {
  HttpHandlerFn,
  HttpInterceptorFn,
  HttpRequest,
} from '@angular/common/http';
import { inject } from '@angular/core';
import { finalize } from 'rxjs';
import { LoaderService } from '../services/loader.service';
import { SkipLoader } from '../services/data.service';

export const loadingInterceptor: HttpInterceptorFn = (
  req: HttpRequest<unknown>,
  next: HttpHandlerFn
) => {
  let load = true;
  let requestComplete = false
  if (req.context.get(SkipLoader)) {
    return next(req);
  }

  const loadingService = inject(LoaderService);
  loadingService.start('interceptor');

  setTimeout(() => {
    load = false 
    if (requestComplete) {
      loadingService.stop('interceptor')
    }
  }, 200);

  return next(req).pipe(
    finalize(() => {
      requestComplete = true
      if (!load) {
        loadingService.stop('interceptor')
      }
    })
  );
};
