import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { VillageComponent } from './village.component';
import { VillageService } from '../../../../services/x_apis/masters/village.service';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { Villages } from '../../../../models/x_models/masters/village';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-villages',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    MatSortModule,
    MatToolbarModule,
    TranslateModule,
    MatFormFieldModule, VillageComponent
  ],
  templateUrl: './villages.component.html',
  styleUrls: ['./villages.component.scss']
})
export class VillagesComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  villageService = inject(VillageService);
  villagesResource = resource({ loader: () => this.villageService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.villagesResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  village = signal<Villages>({
    id: 0,
    villageName: "",
    villageRegionalName: "",
    regionName: "",
    unitName: "",
    talukName: "",
    blockName: "",
    villageLgdCode: ""
  });

  displayedColumns: string[] = [
    'villageName',
    'villageRegionalName',
    'villageLgdCode',
    'regionName',
    'unitName',
    'talukName',
    'blockName',
    'actions',
  ];

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    })
  }

  ngOnInit() {
  }


  onAdd() {
    this.village.set({
      id: 0,
      villageName: "",
      villageRegionalName: "",
      regionName: "",
      unitName: "",
      talukName: "",
      blockName: "",
      villageLgdCode: ""
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(rowData: Villages) {
    this.village.set(rowData);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(rowData: Villages) {
    this.village.set(rowData);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.villagesResource.reload()
    this.search.setValue("");
  }

  onRefresh() {
    this.villagesResource.reload();
    this.search.setValue("");
  }

  async onDelete(village: Villages) {
    await confirmAndDelete(village, village.villageName, 'Village', this.villageService, () => this.villagesResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
