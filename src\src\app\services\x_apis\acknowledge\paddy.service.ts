import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { PaddyForm, PaddyResponse } from '../../../models/x_models/acknowledge/paddy';

@Injectable({
    providedIn: 'root'
})
export class PaddyService {
    dataService = inject(DataService)

    create(data: PaddyForm) {
        return this.dataService.post<Response>("/truck", data)
    }

    get() {
        return this.dataService.get<PaddyResponse>("/crop")
    }

    getById(id: number) {
        return this.dataService.get<PaddyForm>(`/truck/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/truck/${id}`)
    }
}
