import {Routes} from '@angular/router';

export const M_ROUTES: Routes = [
  {
    path: 'personalDetails',
    loadComponent: () =>
      import('../../app/pages/msp/personal-details/personal-details.component')
        .then((m) => m.PersonalDetailsComponent),
  },
  {
    path:'payroll',
     loadComponent: () =>
      import('../../app/pages/eagleadmin/payroll/payroll.component')
        .then((m) => m.PayrollComponent),
  },
  {
    path:'view/:empId',
     loadComponent: () =>
      import('../../app/pages/eagleadmin/view/view.component')
        .then((m) => m.ViewComponent),
  },
   {
    path:'approvedlist',
     loadComponent: () =>
      import('../../app/pages/eagleadmin/payslipapprovedlist/payslipapprovedlist.component')
        .then((m) => m.PayslipapprovedlistComponent),
  },
   {
    path:'attendence',
     loadComponent: () =>
      import('../../app/pages/eagleadmin/attendance/attendance.component')
        .then((m) => m.AttendanceComponent),
  },
  {
    path:'approvedpayslip/:empId',
     loadComponent: () =>
      import('../../app/pages/eagleadmin/payslipview/payslipview.component')
        .then((m) => m.PayslipviewComponent),
  },
   {
    path:'paymatrix',
     loadComponent: () =>
      import('../../app/pages/eagleadmin/matrixpay/matrixpay.component')
        .then((m) => m.MatrixpayComponent),
  },
   {
    path:'loanlist',
     loadComponent: () =>
      import('../../app/pages/eagleadmin/loanlist/loanlist.component')
        .then((m) => m.LoanlistComponent),
  },
  {
    path:'leave-apply',
    loadComponent: () =>
      import('../../app/pages/eagleadmin/leaveManagement/leave-apply/leave-apply.component')
        .then((m) => m.LeaveApplyComponent),
  },
  {
    path:'leave-apply-new',
    loadComponent: () =>
      import('../../app/pages/eagleadmin/leaveManagement/leave-apply-new/leave-apply-new.component')
        .then((m) => m.LeaveApplyNewComponent),
  },
  {
    path:'pending-leaves',
    loadComponent: () =>
      import('../../app/pages/eagleadmin/leaveManagement/pending-leaves/pending-leaves.component')
        .then((m) => m.PendingLeavesComponent),
  },
  {
    path:'history-leaves',
    loadComponent: () =>
      import('../../app/pages/eagleadmin/leaveManagement/history-of-leaves/history-of-leaves.component')
        .then((m) => m.HistoryOfLeavesComponent),
  },
  {
    path:'leave-balance',
    loadComponent: () =>
      import('../../app/pages/eagleadmin/leaveManagement/leave-balance/leave-balance.component')
        .then((m) => m.LeaveBalanceComponent),
  },
  {
    path:'holiday-leave',
    loadComponent: () =>
      import('../../app/pages/eagleadmin/leaveManagement/holidays-leaves/holidays-leaves.component')
        .then((m) => m.HolidaysLeavesComponent),
  },
  {
    path:'add-holiday',
    loadComponent: () =>
      import('../../app/pages/eagleadmin/leaveManagement/add-holiday/add-holiday.component')
        .then((m) => m.AddHolidayComponent),
  },
  {
    path:'leave-balance-add',
    loadComponent: () =>
      import('../../app/pages/eagleadmin/leaveManagement/leave-balance-add/leave-balance-add.component')
        .then((m) => m.LeaveBalanceAddComponent),
  },
   {
    path:'transfer-dashboard',
    loadComponent: () =>
      import('../../app/pages/transfer/transfer-dashboard/transfer-dashboard.component')
        .then((m) => m.TransferDashboardComponent),
  },
     {
    path:'within-region',
    loadComponent: () =>
      import('../../app/pages/transfer/within-region/within-region.component')
        .then((m) => m.WithinRegionComponent),
  },
     {
    path:'inter-region',
    loadComponent: () =>
      import('../../app/pages/transfer/inter-region/inter-region.component')
        .then((m) => m.InterRegionComponent),
  },
     {
    path:'mutual-transfer',
    loadComponent: () =>
      import('../../app/pages/transfer/mutual-transfer/mutual-transfer.component')
        .then((m) => m.MutualTransferComponent),
  },


]
