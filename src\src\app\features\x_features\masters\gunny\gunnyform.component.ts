import { Component, computed, effect, ElementRef, inject, input, linkedSignal, OnInit, output, resource, signal, viewChild } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { <PERSON><PERSON><PERSON>on, MatButtonModule } from '@angular/material/button';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError, MatPrefix } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { Router, RouterModule } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AlertService } from '../../../../services/alert.service';
import { DatePipe, NgTemplateOutlet } from '@angular/common';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { GunnyService } from '../../../../services/x_apis/masters/gunny.service';
import { GunnyForm, GunnyList } from '../../../../models/x_models/masters/gunny';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { QrCode } from '../../../../models/x_models/gunny/gunny-allocation';

import Swal from 'sweetalert2';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatCardModule } from '@angular/material/card';
@Component({
  selector: 'app-gunnyform',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatMenuModule,
    MatPaginatorModule,
    MatDatepickerModule,
    MatFormFieldModule, TranslateModule,
    MatIcon,
    MatPrefix,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatSortModule,
    MatToolbarModule,
    RouterModule,
    MatCardModule
  ],
  templateUrl: './gunnyform.component.html',
  styleUrls: ['./gunnyform.component.scss'],
  providers: [provideNativeDateAdapter(), DatePipe],
})
export class GunnyformComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);

  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);
  alertService = inject(AlertService);

  gunnyService = inject(GunnyService);
  lookupService = inject(LookupService);
  gunnyType = resource({ loader: () => this.lookupService.getGunnytype() }).value;
  storageType = resource({ loader: () => this.lookupService.getStoragelocation(1) }).value;

  dataSource = signal(new MatTableDataSource<any>([]));
  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);


  mode = input.required<string>();
  showGrid = signal(false);
  gridmode = signal<'view' | 'edit' | 'add'>('add');
  uniqueId = input.required<any>()
  closed = output<boolean>();
  router = inject(Router);
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  formData = signal<GunnyForm | null>(null);
  minToDate = signal<Date>(new Date());
  qrData = signal<QrCode[]>([]);
  qrAbstract = signal<any[]>([]);



  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')

  form = this.fb.group({
    id: [0],
    gunnyTypeDetailId: this.fb.control<number | null>(1, Validators.required),
    storageLocationId: this.fb.control<number | null>(null, Validators.required),
    gunnyQrCode: this.fb.control<string>('', Validators.required),
  });


  listData = signal<GunnyList>({
    id: 0,
    gunnyQrCode: '',
    gunnyName: '',
    gunnyShortName: '',
    storageLocationName: ''
  });

  displayedColumns: string[] = [
    'gunny',
    'gunnyQrCode',
    'actions',
  ];

  constructor() { }

  ngOnInit() {
  }

  readonly typeSearch = signal('');
  readonly locationSearch = signal('');
  readonly filteredType = computed(() =>
    this.gunnyType()?.filter(x =>
      x.value.toLowerCase().includes(this.typeSearch().toLowerCase())
    )
  );
  readonly filteredlocation = computed(() =>
    this.storageType()?.filter(x =>
      x.value.toLowerCase().includes(this.locationSearch().toLowerCase())
    )
  );

  filter(value: any, type: string) {
    if (type === 'type') {
      this.typeSearch.set(value.target.value);
    } else {
      this.locationSearch.set(value.target.value);
    }
  }

  resetSearch(type: any) {
    if (type === 'type') {
      this.search1().nativeElement.value = '';
      this.typeSearch.set('');
    } else {
      this.locationSearch.set('');
      this.search2().nativeElement.value = '';
    }

  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    debugger
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.gunnyService.create(formValue as GunnyForm)
      if(res){
        
      }
      if (res.isSuccess) {
        this.showGrid.set(true);
        this.addQR(1, this.form.value.gunnyQrCode, this.form.value.gunnyTypeDetailId);
        console.log(`Gunny ${formValue.storageLocationId} Created Successfully.`)
        // if (formValue.id == 0) {
        //   console.log(`Gunny ${formValue.storageLocationId} Created Successfully.`)
        // }
        // else {
        //   console.log(`Gunny ${formValue.storageLocationId} Created Successfully.`)
        // }
      } else{
        this.form.controls['gunnyQrCode'].setValue(null);
        this.onRefresh();
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true);
    // if (this.qrData().length > 0) {
    //   this.saveConfirmation();
    // } else {
    //   this.closed.emit(true);
    // }
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }



  //for grid ----------------------------->>>

  onAdd() {
    this.listData.set({
      id: 0,
      gunnyQrCode: '',
      gunnyName: '',
      gunnyShortName: '',
      storageLocationName: ''
    });
    this.gridmode.set("add")
    this.showGrid.set(true)
  }

  onView(data: GunnyList) {
    this.listData.set(data);
    this.gridmode.set('view');
    this.showGrid.set(true)
  }

  onEditGrid(data: GunnyList) {
    this.listData.set(data);
    this.gridmode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    // this.listResource.reload();
    // this.search.setValue("");
  }


  async onDelete(data: GunnyList) {
    debugger
    await confirmAndDelete(data, data.gunnyName, 'Gunny', this.gunnyService, () => this.onRefresh());
    const index = this.qrData().findIndex((item: any) => item.gunnyName === data.gunnyName);
    if (index !== -1) {
      this.qrData().splice(index, 1);
      this.onRefresh();
    }
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();
    this.dataSource().filter = filterValue;
  }

  onQrEnter() {
    this.onSubmit();
  }

  addQR(id: number, qrcode: any, gunny: any) {
    if (qrcode) {
      if (id == 1) {
        this.qrData.update(currentMenu => [
          { gunnyName: gunny, gunnyQrCode: qrcode, id: 0 } as QrCode,
          ...currentMenu
        ]);
        this.onRefresh();
      }
    } else {
      this.alertService.error('Please Enter QR Code');
    }
  }

  onRefresh() {
    const updatedData = this.qrData().map(item => {
      const matchingGunny = this.gunnyType()?.find(g => g.key === item.gunnyName);
      return matchingGunny ? { ...item, gunny: matchingGunny.value } : item;
    });
    this.dataSource.set(new MatTableDataSource(updatedData));
    this.onAbstract(updatedData);
    setTimeout(() => {
      this.dataSource().sort = this.sort()!;
      this.dataSource().paginator = this.paginator()!;
    }, 100);
    this.form.controls['gunnyQrCode'].setValue(null);
  }

  onAbstract(updatedData: any[]) {
    const result = updatedData.reduce((acc, curr) => {
      const existing = acc.find((item: QrCode) => item.gunnyName === curr.gunnyName);
      if (existing) {
        existing.count++;
        existing.gunnyQrCode = curr.gunnyQrCode;
      } else {
        acc.push({ ...curr, count: 1 });
      }
      return acc;
    }, []);
    this.qrAbstract.set(result);
  }

  async saveConfirmation() {
    const txt = 'Do you want to save your changes?';
    const title = 'Unsaved Changes';
    const Yes = 'Save';
    const No = `Don't Save`;

    const swalRes = await Swal.fire({
      title,
      text: txt,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: Yes,
      cancelButtonText: No,
      customClass: {
        confirmButton: 'btn-primary',
        cancelButton: 'btn-secondary',
      },
    }).then(async (textResult) => {
      if (textResult.isConfirmed) {
        this.closed.emit(true);
        this.alertService.success('All Scanned Details Save Successfully.')
      } else {
        this.closed.emit(true);
      }
    })
  }

  moveFocus(nextInput: any) {
    nextInput.setFocus();
  }

}
