import { CommonModule } from '@angular/common';
import { Component, ElementRef, inject, OnInit, signal, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, _ } from '@ngx-translate/core';
import { environment } from '../../../../../environments/environment';
import { SessionService } from '../../../../services/session.service';
import { Tripsheet } from '../../../../models/x_models/tracking/tripsheet';
import { LivetrackMapComponent } from '../livetrack/livetrack-map.component';


declare var L: any;

@Component({
  selector: 'app-tripsheet',
  imports:[
     CommonModule,
    // RouterModule.forChild(routes),
    FormsModule,
    FormsModule,
    TranslateModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatInputModule,
    MatFormFieldModule,
    MatDialogModule,
    MatButtonModule,
    MatButtonToggleModule,
    MatSelectModule,
    LivetrackMapComponent
  ],
  templateUrl: './tripsheet.component.html',
  styleUrls: ['./tripsheet.component.scss']
})


export class TripsheetComponent implements OnInit {
  sessionService = inject(SessionService);
  formBuilder = inject(FormBuilder);
  route = inject(ActivatedRoute);
  router = inject(Router);
  // livetrackService = inject(LivetrackService);
  userSessionService = inject(SessionService);
  // getVehicalStatuses = inject(vehiclestatusService);
  // tripSheetService = inject(TripSheetService);
  // mapview: any = L.tileLayer('http://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', { maxZoom: 16, subdomains: ['mt0', 'mt1', 'mt2', 'mt3'] });
  // // tslint:disable-next-line: max-line-length
  // terrainview: any = L.tileLayer('http://{s}.google.com/vt/lyrs=p&x={x}&y={y}&z={z}', { maxZoom: 16, subdomains: ['mt0', 'mt1', 'mt2', 'mt3'] });
  // // tslint:disable-next-line: max-line-length
  // satelliteview: any = L.tileLayer('http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', { maxZoom: 16, subdomains: ['mt0', 'mt1', 'mt2', 'mt3'] });

  environment = signal(environment);
  tsData: any;
  TripSheetDetails = signal<Tripsheet | null>(null);
  // dataSource!: MatTableDataSource<TripSheetResponse>;
  displayedColumns!: string[];
  time: string = '';
  StartDatetime: string = '';
  EndDatetime: string = '';
  memoTime: string = '';
  @ViewChild('mapId', { static: true }) mapcontainer!: ElementRef;
  map: any;
  markerClusterGroup: any;
  // trackingdetails = signal<PlayBackDetails | null>(null);

  prevmarker: any;
  polylines: any[] = [];
  markers: any[] = [];
  seqGroup: any;
  geoJsonResult: any[] = [];
  geojson: any;
  arrowHead: any;
  

  // startIcon = L.icon({
  //   iconUrl: '../../assets/images/status/start.png', iconSize: [40, 40],
  // });
  // endIcon = L.icon({
  //   iconUrl: '../../assets/images/status/end.png', iconSize: [40, 40],
  // });

  constructor() {
  }

  ngOnInit() {
    // this.loadMap();
    this.route.queryParams.subscribe(params => {
      this.tsData = {
        tripsheetid: params['tId'],
      };
    });
    // this.getTripDetails();
    this.TripSheetDetails.set({
    tripstatus:2,
    km:1000,
    sourcefrommemo:"12",
    sourceFromVillage:"Thiruvarur",
    sourceFromTaluk:"Thiruvaur",
    sourceFromRegion:"Thiruvarur",
    vehicle_number:3456,
    verifyStatus:"ON",
    drivername:"Ganesh",
    drivermobileno:9878899999,
    txn_id:76554,
    setOfTripSheetViewModels:[],
    dVerifyStatus:"ON",
    sourcetomemo:"",
    sourceToVillage:"Chennai",
    sourceToTaluk:"Chennai",
    sourceToRegion:"Chennai"
    })
  }

  removeAllMarkers() {
    if (this.prevmarker) { this.map.removeLayer(this.prevmarker); }
    if (this.markers) { this.markers.forEach(m => { this.map.removeLayer(m); }); }
    this.markers = [];
  }

  removeAllLines() {
    if (this.polylines) { this.polylines.forEach(m => { this.map.removeLayer(m); }); this.polylines = []; }
    if (this.seqGroup) { this.map.removeLayer(this.seqGroup); }
  }

  // async getPlayBackDetails() {
  //   try {
  //     const vehicleId = this.TripSheetDetails()?.vehicleid ?? 0;
  //     //startdate 
  //     var startTime = this.TripSheetDetails()?.startts ?? ""; 
  //     var startDate = new Date();
  //     if (startTime) {
  //       startDate = new Date(startTime);
        
  //     } 
  //     //end date 
  //     var endTime = this.TripSheetDetails()?.endts ?? "";
  //     var endDate = new Date();
  //     if (endTime) {
  //       endDate = new Date(endTime);
  //     } 

  //     this.removeAllMarkers();
  //     this.removeAllLines();
  //     var res: PlayBackDetails;
  //     res = await this.getVehicalStatuses.getTripPlayBackDetails(vehicleId, startDate, endDate);
  //     console.log('API Response:', res);
  //     this.trackingdetails.set(res);
  //     console.log('Signal Value:', this.trackingdetails());

  //     if (res.trackingData.length > 0) {
  //       res.trackingData = _.orderBy(this.trackingdetails()?.trackingData, ["gpsDeviceTs"], "asc");
  //     }
  //     const end = res.trackingData?.length - 1;
  //     this.createMarker(res.trackingData[0]?.latitude, res.trackingData[0]?.longitude, this.startIcon).addTo(this.map);
  //     this.createMarker(res.trackingData[end]?.latitude, res.trackingData[end]?.longitude, this.endIcon).addTo(this.map);
  //     this.fitbounds(res.trackingData);

  //     this.createRouteDetails(res.trackingData);

  //   } catch (error) {
  //     console.error('Error fetching vehicle statuses:', error);
  //   }
  // }

  // createMarker(latitude: any, longitude: any, icon: any) {
  //   const marker = L.marker([latitude, longitude], { icon });
  //   this.markers.push(marker);
  //   return marker;
  // }

  fitbounds(data: string | any[]) {
    const latLngs = [];
    for (let i = 0; i < data.length; i += 20) {
      latLngs.push([data[i].latitude, data[i].longitude]);
    }
    if (latLngs.length > 0) {
      this.map.fitBounds(latLngs);
    }
  }

  createRouteDetails(data: any[]) {

    this.geoJsonResult = [];
    let coordinates = [];
    let status = data[0]?.gpsMoveStatus;
    let prevType = data[0].speed >= 30 ? 3 : data[0].speed >= 15 ? 2 : 1;
    for (let index = 0; index < data.length; index++) {
      const trackingdetail = data[index];
      status = trackingdetail.gpsMoveStatus;

      if (index === data.length - 1) {
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
        this.geoJsonResult.push(this.generateGoeJson(coordinates, prevType, trackingdetail.gpsDeviceTs));
        status = trackingdetail.gpsMoveStatus;
        prevType = trackingdetail.speed >= 30 ? 3 : trackingdetail.speed >= 15 ? 2 : 1;

      } else if (trackingdetail.speed >= 30 && prevType === 3) {
        prevType = 3;
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
      } else if (trackingdetail.speed >= 15 && prevType === 2) {
        prevType = 2;
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
      } else if (trackingdetail.speed <= 15 && prevType === 1) {
        prevType = 1;
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
      } else {
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
        if (coordinates.length >= 1) {
          this.geoJsonResult.push(this.generateGoeJson(coordinates, prevType, trackingdetail.gpsDeviceTs));
        }
        coordinates = [];
        coordinates.push([trackingdetail.longitude, trackingdetail.latitude]);
        status = trackingdetail.gpsMoveStatus;
        prevType = trackingdetail.speed >= 30 ? 3 : trackingdetail.speed >= 15 ? 2 : 1;
      }
    }
    function onEachFeature(feature: any, layer: any) {
      if (feature.properties && feature.properties.time) {
      }
    }

    this.geojson = L.geoJSON(this.geoJsonResult, {
      onEachFeature,
      style: function (feature: any) {
        switch (feature.properties.party) {
          // case 1: return { color: '#008000' };
          // case 2: return { color: '#FF541A' };
          // case 3: return { color: '#B22222' };
          default: return { color: '#0077ff' };
        }
      }
    })

    var lines = this.geoJsonResult
      .filter(function (feature) {
        return feature.geometry.type == "LineString"
      })
      .map(function (feature) {
        var coordinates = feature.geometry.coordinates;
        coordinates.forEach(function (coordinate: any) { coordinate.reverse(); })
        return coordinates
      })
    this.arrowHead = L.polylineDecorator(lines, {
      patterns: [{
        offset: 0,
        repeat: 40,
        symbol: L.Symbol.arrowHead({
          pixelSize: 10,
          pathOptions: { fillOpacity: 1, weight: 0, color: "#1C1C53" }
        })
      }]
    }).addTo(this.map);

    this.geojson.addTo(this.map);
  }

  generateGoeJson(coordinates: any[][], status: number, gpsDeviceTs: any) {
    return {
      type: 'Feature',
      properties: {
        party: status,
        time: gpsDeviceTs
      },
      geometry: {
        type: 'LineString',
        coordinates,
      }
    };
  }

  // async getTripDetails() {
  //   try {
  //     const tripsheetid = parseInt(this.tsData.tripsheetid || '0', 10);
  //     var res: TripSheetResponse = await this.tripSheetService.getTripSheetbyId(tripsheetid);
  //     this.TripSheetDetails.set(res);
      
  //     // this.dataSource = new MatTableDataSource<TripSheetResponse>(ress);
  //     if (this.TripSheetDetails()?.memocreatedts != null) {
  //       var memocreatedts = this.TripSheetDetails()?.memocreatedts ?? "";
  //       this.memoTime = this.formatTimestamp(memocreatedts);
  //     }
  //     if (this.TripSheetDetails()?.startts != null) {
  //       var startTs = this.TripSheetDetails()?.startts ?? "";
  //       this.StartDatetime = this.formatTimestamp(startTs);
  //     }
  //     if (this.TripSheetDetails()?.endts != null) {
  //       var endTs = this.TripSheetDetails()?.endts ?? "";
  //       this.EndDatetime = this.formatTimestamp(endTs);
  //       this.calculateTimeDifference();
  //       this.getPlayBackDetails();

  //     }
  //   } catch (error) {
  //     console.error('Error fetching vehicle statuses:', error);
  //   }
  // }

  getTripStatus(status: any) {
    return (status === 0) ? 'Memo Created '
      : (status === 1) ? 'Started' : (status === 2) ? 'Completed' : '';
  }

  // calculateTimeDifference() {
  //   const startTs = this.TripSheetDetails()?.startts ?? "";
  //   const endTs = this.TripSheetDetails()?.endts ?? "";

  //   if (startTs && endTs) {
  //     const start = new Date(startTs);
  //     const end = new Date(endTs);

  //     const diffMs = end.getTime() - start.getTime();
  //     const diffMinutes = Math.floor(diffMs / 60000);

  //     const hours = Math.floor(diffMinutes / 60);
  //     const minutes = diffMinutes % 60;

  //     this.time = hours > 0 ? `${hours} hr ${minutes} mins` : `${minutes} mins`;
  //   }
  // }

  formatTimestamp(startts: string): string {
    if (!startts) return "Invalid date";
  
    const dateObj = new Date(startts);
  
    // Format date as "15-01-2025" (DD-MM-YYYY)
    const date = dateObj.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).replace(/\//g, "-"); // Ensure "-" as separator
  
    // Format time as "3:45 PM" (12-hour format)
    const time = dateObj.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  
    // Check if the time is "00:00" (Midnight) and return only the date
    return time.startsWith("12:00 AM") ? date : `${date} ${time}`;
  }
  

  // loadMap() {
  //   const center = [13.0827, 80.2707];
  //   const baseMaps = {
  //     'Map': this.mapview,
  //     'Terrain': this.terrainview,
  //     'Satellite': this.satelliteview,
  //   };
  //   this.map = L.map(this.mapcontainer.nativeElement, {
  //     fullscreenControl: {
  //       pseudoFullscreen: false,
  //       position: 'topleft',
  //     },
  //   }, { minZoom: 1, tilt: true }).setView(center, 15, { heading: 100.0, tilt: 10.0 });
  //   L.control.layers(baseMaps).addTo(this.map);
  //   this.mapview.addTo(this.map);

  //   this.markerClusterGroup = L.markerClusterGroup({
  //     chunkedLoading: true,
  //     showCoverageOnHover: false,
  //     maxClusterRadius: 50, // Adjust cluster radius as needed
  //     disableClusteringAtZoom: 17
  //   });

  //   this.map.addLayer(this.markerClusterGroup);

  //   // fixes a map loading issue
  //   setTimeout(() => {
  //     window.dispatchEvent(new Event('resize'));
  //   }, 10);

  // }

  navigateToPlayback(TripSheetDetails: any): void {

    var vehicleId = TripSheetDetails.vehicleid;
    var startTime = TripSheetDetails.startts;
    var endTime = TripSheetDetails.endts;
    var regNo = TripSheetDetails.vehicle_number;
    var tripid = TripSheetDetails.tripsheetid;

    this.router.navigate(['/playback/trip'], {
      queryParams: { vId: vehicleId, sts: startTime, ets: endTime, regNo: regNo, tId: tripid, td: true }
    });

  }

  navigateToLivetrack(TripSheetDetails: any) {
    
    var vehicleId = TripSheetDetails.vehicleid;
    var tripid = TripSheetDetails.tripsheetid;
    this.router.navigate(['/livetrack/trip'], {
      queryParams: { vId: vehicleId, tId: tripid, td: true }
    });
  }
}
