{"version": 3, "sources": ["../../../../../../node_modules/ngx-toastr/fesm2022/ngx-toastr.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, InjectionToken, inject, Injectable, ComponentFactoryResolver, ApplicationRef, SecurityContext, Injector, Inject, signal, Component, ChangeDetectionStrategy, HostBinding, HostListener, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { DOCUMENT, NgIf } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport * as i2 from '@angular/platform-browser';\nconst _c0 = [\"toast-component\", \"\"];\nfunction Toast_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function Toast_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.remove());\n    });\n    i0.ɵɵelementStart(1, \"span\", 6);\n    i0.ɵɵtext(2, \"\\xD7\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Toast_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"[\", ctx_r1.duplicatesCount + 1, \"]\");\n  }\n}\nfunction Toast_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, Toast_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.titleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\nfunction Toast_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.messageClass);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.message, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Toast_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.messageClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.message);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nfunction Toast_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.width() + \"%\");\n  }\n}\nfunction ToastNoAnimation_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ToastNoAnimation_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.remove());\n    });\n    i0.ɵɵelementStart(1, \"span\", 6);\n    i0.ɵɵtext(2, \"\\xD7\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ToastNoAnimation_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"[\", ctx_r1.duplicatesCount + 1, \"]\");\n  }\n}\nfunction ToastNoAnimation_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ToastNoAnimation_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.titleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\nfunction ToastNoAnimation_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.messageClass);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.message, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ToastNoAnimation_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.messageClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.message);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nfunction ToastNoAnimation_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.width() + \"%\");\n  }\n}\nclass ToastContainerDirective {\n  el;\n  constructor(el) {\n    this.el = el;\n  }\n  getContainerElement() {\n    return this.el.nativeElement;\n  }\n  static ɵfac = function ToastContainerDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastContainerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ToastContainerDirective,\n    selectors: [[\"\", \"toastContainer\", \"\"]],\n    exportAs: [\"toastContainer\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastContainerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[toastContainer]',\n      exportAs: 'toastContainer',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\n\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal {\n  _attachedHost;\n  /** The type of the component that will be instantiated for attachment. */\n  component;\n  /**\n   * [Optional] Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalHost.\n   * The origin necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef;\n  /** Injector used for the instantiation of the component. */\n  injector;\n  constructor(component, injector) {\n    this.component = component;\n    this.injector = injector;\n  }\n  /** Attach this portal to a host. */\n  attach(host, newestOnTop) {\n    this._attachedHost = host;\n    return host.attach(this, newestOnTop);\n  }\n  /** Detach this portal from its host */\n  detach() {\n    const host = this._attachedHost;\n    if (host) {\n      this._attachedHost = undefined;\n      return host.detach();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalHost reference without performing `attach()`. This is used directly by\n   * the PortalHost when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n}\n/**\n * Partial implementation of PortalHost that only deals with attaching a\n * ComponentPortal\n */\nclass BasePortalHost {\n  /** The portal currently attached to the host. */\n  _attachedPortal;\n  /** A function that will permanently dispose this host. */\n  _disposeFn;\n  attach(portal, newestOnTop) {\n    this._attachedPortal = portal;\n    return this.attachComponentPortal(portal, newestOnTop);\n  }\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost();\n    }\n    this._attachedPortal = undefined;\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = undefined;\n    }\n  }\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n}\n\n/**\n * Reference to a toast opened via the Toastr service.\n */\nclass ToastRef {\n  _overlayRef;\n  /** The instance of component opened into the toast. */\n  componentInstance;\n  /** Count of duplicates of this toast */\n  duplicatesCount = 0;\n  /** Subject for notifying the user that the toast has finished closing. */\n  _afterClosed = new Subject();\n  /** triggered when toast is activated */\n  _activate = new Subject();\n  /** notifies the toast that it should close before the timeout */\n  _manualClose = new Subject();\n  /** notifies the toast that it should reset the timeouts */\n  _resetTimeout = new Subject();\n  /** notifies the toast that it should count a duplicate toast */\n  _countDuplicate = new Subject();\n  constructor(_overlayRef) {\n    this._overlayRef = _overlayRef;\n  }\n  manualClose() {\n    this._manualClose.next();\n    this._manualClose.complete();\n  }\n  manualClosed() {\n    return this._manualClose.asObservable();\n  }\n  timeoutReset() {\n    return this._resetTimeout.asObservable();\n  }\n  countDuplicate() {\n    return this._countDuplicate.asObservable();\n  }\n  /**\n   * Close the toast.\n   */\n  close() {\n    this._overlayRef.detach();\n    this._afterClosed.next();\n    this._manualClose.next();\n    this._afterClosed.complete();\n    this._manualClose.complete();\n    this._activate.complete();\n    this._resetTimeout.complete();\n    this._countDuplicate.complete();\n  }\n  /** Gets an observable that is notified when the toast is finished closing. */\n  afterClosed() {\n    return this._afterClosed.asObservable();\n  }\n  isInactive() {\n    return this._activate.isStopped;\n  }\n  activate() {\n    this._activate.next();\n    this._activate.complete();\n  }\n  /** Gets an observable that is notified when the toast has started opening. */\n  afterActivate() {\n    return this._activate.asObservable();\n  }\n  /** Reset the toast timouts and count duplicates */\n  onDuplicate(resetTimeout, countDuplicate) {\n    if (resetTimeout) {\n      this._resetTimeout.next();\n    }\n    if (countDuplicate) {\n      this._countDuplicate.next(++this.duplicatesCount);\n    }\n  }\n}\n\n/**\n * Everything a toast needs to launch\n */\nclass ToastPackage {\n  toastId;\n  config;\n  message;\n  title;\n  toastType;\n  toastRef;\n  _onTap = new Subject();\n  _onAction = new Subject();\n  constructor(toastId, config, message, title, toastType, toastRef) {\n    this.toastId = toastId;\n    this.config = config;\n    this.message = message;\n    this.title = title;\n    this.toastType = toastType;\n    this.toastRef = toastRef;\n    this.toastRef.afterClosed().subscribe(() => {\n      this._onAction.complete();\n      this._onTap.complete();\n    });\n  }\n  /** Fired on click */\n  triggerTap() {\n    this._onTap.next();\n    if (this.config.tapToDismiss) {\n      this._onTap.complete();\n    }\n  }\n  onTap() {\n    return this._onTap.asObservable();\n  }\n  /** available for use in custom toast */\n  triggerAction(action) {\n    this._onAction.next(action);\n  }\n  onAction() {\n    return this._onAction.asObservable();\n  }\n}\nconst DefaultNoComponentGlobalConfig = {\n  maxOpened: 0,\n  autoDismiss: false,\n  newestOnTop: true,\n  preventDuplicates: false,\n  countDuplicates: false,\n  resetTimeoutOnDuplicate: false,\n  includeTitleDuplicates: false,\n  iconClasses: {\n    error: 'toast-error',\n    info: 'toast-info',\n    success: 'toast-success',\n    warning: 'toast-warning'\n  },\n  // Individual\n  closeButton: false,\n  disableTimeOut: false,\n  timeOut: 5000,\n  extendedTimeOut: 1000,\n  enableHtml: false,\n  progressBar: false,\n  toastClass: 'ngx-toastr',\n  positionClass: 'toast-top-right',\n  titleClass: 'toast-title',\n  messageClass: 'toast-message',\n  easing: 'ease-in',\n  easeTime: 300,\n  tapToDismiss: true,\n  onActivateTick: false,\n  progressAnimation: 'decreasing'\n};\nconst TOAST_CONFIG = new InjectionToken('ToastConfig');\n\n/**\n * A PortalHost for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n *\n * This is the only part of the portal core that directly touches the DOM.\n */\nclass DomPortalHost extends BasePortalHost {\n  _hostDomElement;\n  _componentFactoryResolver;\n  _appRef;\n  constructor(_hostDomElement, _componentFactoryResolver, _appRef) {\n    super();\n    this._hostDomElement = _hostDomElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   */\n  attachComponentPortal(portal, newestOnTop) {\n    const componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n    let componentRef;\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the ChangeDetector for that component to the application (which\n    // happens automatically when using a ViewContainer).\n    componentRef = componentFactory.create(portal.injector);\n    // When creating a component outside of a ViewContainer, we need to manually register\n    // its ChangeDetector with the application. This API is unfortunately not yet published\n    // in Angular core. The change detector must also be deregistered when the component\n    // is destroyed to prevent memory leaks.\n    this._appRef.attachView(componentRef.hostView);\n    this.setDisposeFn(() => {\n      this._appRef.detachView(componentRef.hostView);\n      componentRef.destroy();\n    });\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    if (newestOnTop) {\n      this._hostDomElement.insertBefore(this._getComponentRootNode(componentRef), this._hostDomElement.firstChild);\n    } else {\n      this._hostDomElement.appendChild(this._getComponentRootNode(componentRef));\n    }\n    return componentRef;\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n}\n\n/** Container inside which all toasts will render. */\nclass OverlayContainer {\n  _document = inject(DOCUMENT);\n  _containerElement;\n  ngOnDestroy() {\n    if (this._containerElement && this._containerElement.parentNode) {\n      this._containerElement.parentNode.removeChild(this._containerElement);\n    }\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time  it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body\n   * and 'aria-live=\"polite\"'\n   */\n  _createContainer() {\n    const container = this._document.createElement('div');\n    container.classList.add('overlay-container');\n    container.setAttribute('aria-live', 'polite');\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n  static ɵfac = function OverlayContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayContainer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayContainer,\n    factory: OverlayContainer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  _portalHost;\n  constructor(_portalHost) {\n    this._portalHost = _portalHost;\n  }\n  attach(portal, newestOnTop = true) {\n    return this._portalHost.attach(portal, newestOnTop);\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns Resolves when the overlay has been detached.\n   */\n  detach() {\n    return this._portalHost.detach();\n  }\n}\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalHost, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  _overlayContainer = inject(OverlayContainer);\n  _componentFactoryResolver = inject(ComponentFactoryResolver);\n  _appRef = inject(ApplicationRef);\n  _document = inject(DOCUMENT);\n  // Namespace panes by overlay container\n  _paneElements = new Map();\n  /**\n   * Creates an overlay.\n   * @returns A reference to the created overlay.\n   */\n  create(positionClass, overlayContainer) {\n    // get existing pane if possible\n    return this._createOverlayRef(this.getPaneElement(positionClass, overlayContainer));\n  }\n  getPaneElement(positionClass = '', overlayContainer) {\n    if (!this._paneElements.get(overlayContainer)) {\n      this._paneElements.set(overlayContainer, {});\n    }\n    if (!this._paneElements.get(overlayContainer)[positionClass]) {\n      this._paneElements.get(overlayContainer)[positionClass] = this._createPaneElement(positionClass, overlayContainer);\n    }\n    return this._paneElements.get(overlayContainer)[positionClass];\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  _createPaneElement(positionClass, overlayContainer) {\n    const pane = this._document.createElement('div');\n    pane.id = 'toast-container';\n    pane.classList.add(positionClass);\n    pane.classList.add('toast-container');\n    if (!overlayContainer) {\n      this._overlayContainer.getContainerElement().appendChild(pane);\n    } else {\n      overlayContainer.getContainerElement().appendChild(pane);\n    }\n    return pane;\n  }\n  /**\n   * Create a DomPortalHost into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal host.\n   * @returns A portal host for the given DOM element.\n   */\n  _createPortalHost(pane) {\n    return new DomPortalHost(pane, this._componentFactoryResolver, this._appRef);\n  }\n  /**\n   * Creates an OverlayRef for an overlay in the given DOM element.\n   * @param pane DOM element for the overlay\n   */\n  _createOverlayRef(pane) {\n    return new OverlayRef(this._createPortalHost(pane));\n  }\n  static ɵfac = function Overlay_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Overlay)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Overlay,\n    factory: Overlay.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ToastrService {\n  overlay;\n  _injector;\n  sanitizer;\n  ngZone;\n  toastrConfig;\n  currentlyActive = 0;\n  toasts = [];\n  overlayContainer;\n  previousToastMessage;\n  index = 0;\n  constructor(token, overlay, _injector, sanitizer, ngZone) {\n    this.overlay = overlay;\n    this._injector = _injector;\n    this.sanitizer = sanitizer;\n    this.ngZone = ngZone;\n    this.toastrConfig = {\n      ...token.default,\n      ...token.config\n    };\n    if (token.config.iconClasses) {\n      this.toastrConfig.iconClasses = {\n        ...token.default.iconClasses,\n        ...token.config.iconClasses\n      };\n    }\n  }\n  /** show toast */\n  show(message, title, override = {}, type = '') {\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show successful toast */\n  success(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.success || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show error toast */\n  error(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.error || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show info toast */\n  info(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.info || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show warning toast */\n  warning(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.warning || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /**\n   * Remove all or a single toast by id\n   */\n  clear(toastId) {\n    // Call every toastRef manualClose function\n    for (const toast of this.toasts) {\n      if (toastId !== undefined) {\n        if (toast.toastId === toastId) {\n          toast.toastRef.manualClose();\n          return;\n        }\n      } else {\n        toast.toastRef.manualClose();\n      }\n    }\n  }\n  /**\n   * Remove and destroy a single toast by id\n   */\n  remove(toastId) {\n    const found = this._findToast(toastId);\n    if (!found) {\n      return false;\n    }\n    found.activeToast.toastRef.close();\n    this.toasts.splice(found.index, 1);\n    this.currentlyActive = this.currentlyActive - 1;\n    if (!this.toastrConfig.maxOpened || !this.toasts.length) {\n      return false;\n    }\n    if (this.currentlyActive < this.toastrConfig.maxOpened && this.toasts[this.currentlyActive]) {\n      const p = this.toasts[this.currentlyActive].toastRef;\n      if (!p.isInactive()) {\n        this.currentlyActive = this.currentlyActive + 1;\n        p.activate();\n      }\n    }\n    return true;\n  }\n  /**\n   * Determines if toast message is already shown\n   */\n  findDuplicate(title = '', message = '', resetOnDuplicate, countDuplicates) {\n    const {\n      includeTitleDuplicates\n    } = this.toastrConfig;\n    for (const toast of this.toasts) {\n      const hasDuplicateTitle = includeTitleDuplicates && toast.title === title;\n      if ((!includeTitleDuplicates || hasDuplicateTitle) && toast.message === message) {\n        toast.toastRef.onDuplicate(resetOnDuplicate, countDuplicates);\n        return toast;\n      }\n    }\n    return null;\n  }\n  /** create a clone of global config and apply individual settings */\n  applyConfig(override = {}) {\n    return {\n      ...this.toastrConfig,\n      ...override\n    };\n  }\n  /**\n   * Find toast object by id\n   */\n  _findToast(toastId) {\n    for (let i = 0; i < this.toasts.length; i++) {\n      if (this.toasts[i].toastId === toastId) {\n        return {\n          index: i,\n          activeToast: this.toasts[i]\n        };\n      }\n    }\n    return null;\n  }\n  /**\n   * Determines the need to run inside angular's zone then builds the toast\n   */\n  _preBuildNotification(toastType, message, title, config) {\n    if (config.onActivateTick) {\n      return this.ngZone.run(() => this._buildNotification(toastType, message, title, config));\n    }\n    return this._buildNotification(toastType, message, title, config);\n  }\n  /**\n   * Creates and attaches toast data to component\n   * returns the active toast, or in case preventDuplicates is enabled the original/non-duplicate active toast.\n   */\n  _buildNotification(toastType, message, title, config) {\n    if (!config.toastComponent) {\n      throw new Error('toastComponent required');\n    }\n    // max opened and auto dismiss = true\n    // if timeout = 0 resetting it would result in setting this.hideTime = Date.now(). Hence, we only want to reset timeout if there is\n    // a timeout at all\n    const duplicate = this.findDuplicate(title, message, this.toastrConfig.resetTimeoutOnDuplicate && config.timeOut > 0, this.toastrConfig.countDuplicates);\n    if ((this.toastrConfig.includeTitleDuplicates && title || message) && this.toastrConfig.preventDuplicates && duplicate !== null) {\n      return duplicate;\n    }\n    this.previousToastMessage = message;\n    let keepInactive = false;\n    if (this.toastrConfig.maxOpened && this.currentlyActive >= this.toastrConfig.maxOpened) {\n      keepInactive = true;\n      if (this.toastrConfig.autoDismiss) {\n        this.clear(this.toasts[0].toastId);\n      }\n    }\n    const overlayRef = this.overlay.create(config.positionClass, this.overlayContainer);\n    this.index = this.index + 1;\n    let sanitizedMessage = message;\n    if (message && config.enableHtml) {\n      sanitizedMessage = this.sanitizer.sanitize(SecurityContext.HTML, message);\n    }\n    const toastRef = new ToastRef(overlayRef);\n    const toastPackage = new ToastPackage(this.index, config, sanitizedMessage, title, toastType, toastRef);\n    /** New injector that contains an instance of `ToastPackage`. */\n    const providers = [{\n      provide: ToastPackage,\n      useValue: toastPackage\n    }];\n    const toastInjector = Injector.create({\n      providers,\n      parent: this._injector\n    });\n    const component = new ComponentPortal(config.toastComponent, toastInjector);\n    const portal = overlayRef.attach(component, config.newestOnTop);\n    toastRef.componentInstance = portal.instance;\n    const ins = {\n      toastId: this.index,\n      title: title || '',\n      message: message || '',\n      toastRef,\n      onShown: toastRef.afterActivate(),\n      onHidden: toastRef.afterClosed(),\n      onTap: toastPackage.onTap(),\n      onAction: toastPackage.onAction(),\n      portal\n    };\n    if (!keepInactive) {\n      this.currentlyActive = this.currentlyActive + 1;\n      setTimeout(() => {\n        ins.toastRef.activate();\n      });\n    }\n    this.toasts.push(ins);\n    return ins;\n  }\n  static ɵfac = function ToastrService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastrService)(i0.ɵɵinject(TOAST_CONFIG), i0.ɵɵinject(Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(i0.NgZone));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ToastrService,\n    factory: ToastrService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastrService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TOAST_CONFIG]\n    }]\n  }, {\n    type: Overlay\n  }, {\n    type: i0.Injector\n  }, {\n    type: i2.DomSanitizer\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\nclass Toast {\n  toastrService;\n  toastPackage;\n  ngZone;\n  message;\n  title;\n  options;\n  duplicatesCount;\n  originalTimeout;\n  /** width of progress bar */\n  width = signal(-1);\n  /** a combination of toast type and options.toastClass */\n  toastClasses = '';\n  state;\n  /** controls animation */\n  get _state() {\n    return this.state();\n  }\n  /** hides component when waiting to be displayed */\n  get displayStyle() {\n    if (this.state().value === 'inactive') {\n      return 'none';\n    }\n    return;\n  }\n  timeout;\n  intervalId;\n  hideTime;\n  sub;\n  sub1;\n  sub2;\n  sub3;\n  constructor(toastrService, toastPackage, ngZone) {\n    this.toastrService = toastrService;\n    this.toastPackage = toastPackage;\n    this.ngZone = ngZone;\n    this.message = toastPackage.message;\n    this.title = toastPackage.title;\n    this.options = toastPackage.config;\n    this.originalTimeout = toastPackage.config.timeOut;\n    this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n    this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n      this.activateToast();\n    });\n    this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n      this.remove();\n    });\n    this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n      this.resetTimeout();\n    });\n    this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n      this.duplicatesCount = count;\n    });\n    this.state = signal({\n      value: 'inactive',\n      params: {\n        easeTime: this.toastPackage.config.easeTime,\n        easing: 'ease-in'\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    this.sub1.unsubscribe();\n    this.sub2.unsubscribe();\n    this.sub3.unsubscribe();\n    clearInterval(this.intervalId);\n    clearTimeout(this.timeout);\n  }\n  /**\n   * activates toast and sets timeout\n   */\n  activateToast() {\n    this.state.update(state => ({\n      ...state,\n      value: 'active'\n    }));\n    if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n      this.outsideTimeout(() => this.remove(), this.options.timeOut);\n      this.hideTime = new Date().getTime() + this.options.timeOut;\n      if (this.options.progressBar) {\n        this.outsideInterval(() => this.updateProgress(), 10);\n      }\n    }\n  }\n  /**\n   * updates progress bar width\n   */\n  updateProgress() {\n    if (this.width() === 0 || this.width() === 100 || !this.options.timeOut) {\n      return;\n    }\n    const now = new Date().getTime();\n    const remaining = this.hideTime - now;\n    this.width.set(remaining / this.options.timeOut * 100);\n    if (this.options.progressAnimation === 'increasing') {\n      this.width.update(width => 100 - width);\n    }\n    if (this.width() <= 0) {\n      this.width.set(0);\n    }\n    if (this.width() >= 100) {\n      this.width.set(100);\n    }\n  }\n  resetTimeout() {\n    clearTimeout(this.timeout);\n    clearInterval(this.intervalId);\n    this.state.update(state => ({\n      ...state,\n      value: 'active'\n    }));\n    this.outsideTimeout(() => this.remove(), this.originalTimeout);\n    this.options.timeOut = this.originalTimeout;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width.set(-1);\n    if (this.options.progressBar) {\n      this.outsideInterval(() => this.updateProgress(), 10);\n    }\n  }\n  /**\n   * tells toastrService to remove this toast after animation time\n   */\n  remove() {\n    if (this.state().value === 'removed') {\n      return;\n    }\n    clearTimeout(this.timeout);\n    this.state.update(state => ({\n      ...state,\n      value: 'removed'\n    }));\n    this.outsideTimeout(() => this.toastrService.remove(this.toastPackage.toastId), +this.toastPackage.config.easeTime);\n  }\n  tapToast() {\n    if (this.state().value === 'removed') {\n      return;\n    }\n    this.toastPackage.triggerTap();\n    if (this.options.tapToDismiss) {\n      this.remove();\n    }\n  }\n  stickAround() {\n    if (this.state().value === 'removed') {\n      return;\n    }\n    if (this.options.disableTimeOut !== 'extendedTimeOut') {\n      clearTimeout(this.timeout);\n      this.options.timeOut = 0;\n      this.hideTime = 0;\n      // disable progressBar\n      clearInterval(this.intervalId);\n      this.width.set(0);\n    }\n  }\n  delayedHideToast() {\n    if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state().value === 'removed') {\n      return;\n    }\n    this.outsideTimeout(() => this.remove(), this.options.extendedTimeOut);\n    this.options.timeOut = this.options.extendedTimeOut;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width.set(-1);\n    if (this.options.progressBar) {\n      this.outsideInterval(() => this.updateProgress(), 10);\n    }\n  }\n  outsideTimeout(func, timeout) {\n    if (this.ngZone) {\n      this.ngZone.runOutsideAngular(() => this.timeout = setTimeout(() => this.runInsideAngular(func), timeout));\n    } else {\n      this.timeout = setTimeout(() => func(), timeout);\n    }\n  }\n  outsideInterval(func, timeout) {\n    if (this.ngZone) {\n      this.ngZone.runOutsideAngular(() => this.intervalId = setInterval(() => this.runInsideAngular(func), timeout));\n    } else {\n      this.intervalId = setInterval(() => func(), timeout);\n    }\n  }\n  runInsideAngular(func) {\n    if (this.ngZone) {\n      this.ngZone.run(() => func());\n    } else {\n      func();\n    }\n  }\n  static ɵfac = function Toast_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Toast)(i0.ɵɵdirectiveInject(ToastrService), i0.ɵɵdirectiveInject(ToastPackage), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Toast,\n    selectors: [[\"\", \"toast-component\", \"\"]],\n    hostVars: 5,\n    hostBindings: function Toast_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function Toast_click_HostBindingHandler() {\n          return ctx.tapToast();\n        })(\"mouseenter\", function Toast_mouseenter_HostBindingHandler() {\n          return ctx.stickAround();\n        })(\"mouseleave\", function Toast_mouseleave_HostBindingHandler() {\n          return ctx.delayedHideToast();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵsyntheticHostProperty(\"@flyInOut\", ctx._state);\n        i0.ɵɵclassMap(ctx.toastClasses);\n        i0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n      }\n    },\n    attrs: _c0,\n    decls: 5,\n    vars: 5,\n    consts: [[\"type\", \"button\", \"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alert\", 3, \"innerHTML\"], [\"role\", \"alert\"], [1, \"toast-progress\"]],\n    template: function Toast_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, Toast_button_0_Template, 3, 0, \"button\", 0)(1, Toast_div_1_Template, 3, 5, \"div\", 1)(2, Toast_div_2_Template, 1, 3, \"div\", 2)(3, Toast_div_3_Template, 2, 4, \"div\", 3)(4, Toast_div_4_Template, 2, 2, \"div\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.title);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n      }\n    },\n    dependencies: [NgIf],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('flyInOut', [state('inactive', style({\n        opacity: 0\n      })), state('active', style({\n        opacity: 1\n      })), state('removed', style({\n        opacity: 0\n      })), transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')), transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Toast, [{\n    type: Component,\n    args: [{\n      selector: '[toast-component]',\n      template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width() + '%'\"></div>\n  </div>\n  `,\n      animations: [trigger('flyInOut', [state('inactive', style({\n        opacity: 0\n      })), state('active', style({\n        opacity: 1\n      })), state('removed', style({\n        opacity: 0\n      })), transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')), transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))])],\n      preserveWhitespaces: false,\n      standalone: true,\n      imports: [NgIf],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: ToastrService\n  }, {\n    type: ToastPackage\n  }, {\n    type: i0.NgZone\n  }], {\n    toastClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    _state: [{\n      type: HostBinding,\n      args: ['@flyInOut']\n    }],\n    displayStyle: [{\n      type: HostBinding,\n      args: ['style.display']\n    }],\n    tapToast: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    stickAround: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    delayedHideToast: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\nconst DefaultGlobalConfig = {\n  ...DefaultNoComponentGlobalConfig,\n  toastComponent: Toast\n};\n/**\n * @description\n * Provides the `TOAST_CONFIG` token with the given config.\n *\n * @param config The config to configure toastr.\n * @returns The environment providers.\n *\n * @example\n * ```ts\n * import { provideToastr } from 'ngx-toastr';\n *\n * bootstrap(AppComponent, {\n *   providers: [\n *     provideToastr({\n *       timeOut: 2000,\n *       positionClass: 'toast-top-right',\n *     }),\n *   ],\n * })\n */\nconst provideToastr = (config = {}) => {\n  const providers = [{\n    provide: TOAST_CONFIG,\n    useValue: {\n      default: DefaultGlobalConfig,\n      config\n    }\n  }];\n  return makeEnvironmentProviders(providers);\n};\nclass ToastrModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastrModule,\n      providers: [provideToastr(config)]\n    };\n  }\n  static ɵfac = function ToastrModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastrModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastrModule,\n    imports: [Toast],\n    exports: [Toast]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastrModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Toast],\n      exports: [Toast]\n    }]\n  }], null, null);\n})();\nclass ToastrComponentlessModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastrModule,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultNoComponentGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n  static ɵfac = function ToastrComponentlessModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastrComponentlessModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastrComponentlessModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastrComponentlessModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\nclass ToastNoAnimation {\n  toastrService;\n  toastPackage;\n  appRef;\n  message;\n  title;\n  options;\n  duplicatesCount;\n  originalTimeout;\n  /** width of progress bar */\n  width = signal(-1);\n  /** a combination of toast type and options.toastClass */\n  toastClasses = '';\n  /** hides component when waiting to be displayed */\n  get displayStyle() {\n    if (this.state() === 'inactive') {\n      return 'none';\n    }\n    return null;\n  }\n  /** controls animation */\n  state = signal('inactive');\n  timeout;\n  intervalId;\n  hideTime;\n  sub;\n  sub1;\n  sub2;\n  sub3;\n  constructor(toastrService, toastPackage, appRef) {\n    this.toastrService = toastrService;\n    this.toastPackage = toastPackage;\n    this.appRef = appRef;\n    this.message = toastPackage.message;\n    this.title = toastPackage.title;\n    this.options = toastPackage.config;\n    this.originalTimeout = toastPackage.config.timeOut;\n    this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n    this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n      this.activateToast();\n    });\n    this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n      this.remove();\n    });\n    this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n      this.resetTimeout();\n    });\n    this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n      this.duplicatesCount = count;\n    });\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    this.sub1.unsubscribe();\n    this.sub2.unsubscribe();\n    this.sub3.unsubscribe();\n    clearInterval(this.intervalId);\n    clearTimeout(this.timeout);\n  }\n  /**\n   * activates toast and sets timeout\n   */\n  activateToast() {\n    this.state.set('active');\n    if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n      this.timeout = setTimeout(() => {\n        this.remove();\n      }, this.options.timeOut);\n      this.hideTime = new Date().getTime() + this.options.timeOut;\n      if (this.options.progressBar) {\n        this.intervalId = setInterval(() => this.updateProgress(), 10);\n      }\n    }\n    if (this.options.onActivateTick) {\n      this.appRef.tick();\n    }\n  }\n  /**\n   * updates progress bar width\n   */\n  updateProgress() {\n    if (this.width() === 0 || this.width() === 100 || !this.options.timeOut) {\n      return;\n    }\n    const now = new Date().getTime();\n    const remaining = this.hideTime - now;\n    this.width.set(remaining / this.options.timeOut * 100);\n    if (this.options.progressAnimation === 'increasing') {\n      this.width.update(width => 100 - width);\n    }\n    if (this.width() <= 0) {\n      this.width.set(0);\n    }\n    if (this.width() >= 100) {\n      this.width.set(100);\n    }\n  }\n  resetTimeout() {\n    clearTimeout(this.timeout);\n    clearInterval(this.intervalId);\n    this.state.set('active');\n    this.options.timeOut = this.originalTimeout;\n    this.timeout = setTimeout(() => this.remove(), this.originalTimeout);\n    this.hideTime = new Date().getTime() + (this.originalTimeout || 0);\n    this.width.set(-1);\n    if (this.options.progressBar) {\n      this.intervalId = setInterval(() => this.updateProgress(), 10);\n    }\n  }\n  /**\n   * tells toastrService to remove this toast after animation time\n   */\n  remove() {\n    if (this.state() === 'removed') {\n      return;\n    }\n    clearTimeout(this.timeout);\n    this.state.set('removed');\n    this.timeout = setTimeout(() => this.toastrService.remove(this.toastPackage.toastId));\n  }\n  tapToast() {\n    if (this.state() === 'removed') {\n      return;\n    }\n    this.toastPackage.triggerTap();\n    if (this.options.tapToDismiss) {\n      this.remove();\n    }\n  }\n  stickAround() {\n    if (this.state() === 'removed') {\n      return;\n    }\n    clearTimeout(this.timeout);\n    this.options.timeOut = 0;\n    this.hideTime = 0;\n    // disable progressBar\n    clearInterval(this.intervalId);\n    this.width.set(0);\n  }\n  delayedHideToast() {\n    if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state() === 'removed') {\n      return;\n    }\n    this.timeout = setTimeout(() => this.remove(), this.options.extendedTimeOut);\n    this.options.timeOut = this.options.extendedTimeOut;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width.set(-1);\n    if (this.options.progressBar) {\n      this.intervalId = setInterval(() => this.updateProgress(), 10);\n    }\n  }\n  static ɵfac = function ToastNoAnimation_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastNoAnimation)(i0.ɵɵdirectiveInject(ToastrService), i0.ɵɵdirectiveInject(ToastPackage), i0.ɵɵdirectiveInject(i0.ApplicationRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ToastNoAnimation,\n    selectors: [[\"\", \"toast-component\", \"\"]],\n    hostVars: 4,\n    hostBindings: function ToastNoAnimation_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function ToastNoAnimation_click_HostBindingHandler() {\n          return ctx.tapToast();\n        })(\"mouseenter\", function ToastNoAnimation_mouseenter_HostBindingHandler() {\n          return ctx.stickAround();\n        })(\"mouseleave\", function ToastNoAnimation_mouseleave_HostBindingHandler() {\n          return ctx.delayedHideToast();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.toastClasses);\n        i0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n      }\n    },\n    attrs: _c0,\n    decls: 5,\n    vars: 5,\n    consts: [[\"type\", \"button\", \"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alert\", 3, \"innerHTML\"], [\"role\", \"alert\"], [1, \"toast-progress\"]],\n    template: function ToastNoAnimation_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ToastNoAnimation_button_0_Template, 3, 0, \"button\", 0)(1, ToastNoAnimation_div_1_Template, 3, 5, \"div\", 1)(2, ToastNoAnimation_div_2_Template, 1, 3, \"div\", 2)(3, ToastNoAnimation_div_3_Template, 2, 4, \"div\", 3)(4, ToastNoAnimation_div_4_Template, 2, 2, \"div\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.title);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n      }\n    },\n    dependencies: [NgIf],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastNoAnimation, [{\n    type: Component,\n    args: [{\n      selector: '[toast-component]',\n      template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width() + '%'\"></div>\n  </div>\n  `,\n      standalone: true,\n      imports: [NgIf],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: ToastrService\n  }, {\n    type: ToastPackage\n  }, {\n    type: i0.ApplicationRef\n  }], {\n    toastClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    displayStyle: [{\n      type: HostBinding,\n      args: ['style.display']\n    }],\n    tapToast: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    stickAround: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    delayedHideToast: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\nconst DefaultNoAnimationsGlobalConfig = {\n  ...DefaultNoComponentGlobalConfig,\n  toastComponent: ToastNoAnimation\n};\nclass ToastNoAnimationModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastNoAnimationModule,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultNoAnimationsGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n  static ɵfac = function ToastNoAnimationModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastNoAnimationModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastNoAnimationModule,\n    imports: [ToastNoAnimation],\n    exports: [ToastNoAnimation]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastNoAnimationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ToastNoAnimation],\n      exports: [ToastNoAnimation]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, ComponentPortal, DefaultGlobalConfig, DefaultNoAnimationsGlobalConfig, DefaultNoComponentGlobalConfig, Overlay, OverlayContainer, OverlayRef, TOAST_CONFIG, Toast, ToastContainerDirective, ToastNoAnimation, ToastNoAnimationModule, ToastPackage, ToastRef, ToastrComponentlessModule, ToastrModule, ToastrService, provideToastr };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,mBAAmB,EAAE;AAClC,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,kDAAkD;AAChF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,GAAG,GAAM;AACnB,IAAG,aAAa,EAAE;AAAA,EACpB;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,kBAAkB,GAAG,GAAG;AAAA,EAC5D;AACF;AACA,SAAS,qBAAqB,IAAI,KAAK;AACrC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,OAAO,CAAC;AACX,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,gBAAgB,CAAC;AAC7E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,QAAQ,UAAU;AACvC,IAAG,YAAY,cAAc,OAAO,KAAK;AACzC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,GAAG;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,eAAe;AAAA,EAC9C;AACF;AACA,SAAS,qBAAqB,IAAI,KAAK;AACrC,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,QAAQ,YAAY;AACzC,IAAG,WAAW,aAAa,OAAO,SAAY,cAAc;AAAA,EAC9D;AACF;AACA,SAAS,qBAAqB,IAAI,KAAK;AACrC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,QAAQ,YAAY;AACzC,IAAG,YAAY,cAAc,OAAO,OAAO;AAC3C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,SAAS,GAAG;AAAA,EAChD;AACF;AACA,SAAS,qBAAqB,IAAI,KAAK;AACrC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,UAAU,GAAG,OAAO,CAAC;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,YAAY,SAAS,OAAO,MAAM,IAAI,GAAG;AAAA,EAC9C;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,6DAA6D;AAC3F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,GAAG,GAAM;AACnB,IAAG,aAAa,EAAE;AAAA,EACpB;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,kBAAkB,GAAG,GAAG;AAAA,EAC5D;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,OAAO,CAAC;AACX,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,gBAAgB,CAAC;AACxF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,QAAQ,UAAU;AACvC,IAAG,YAAY,cAAc,OAAO,KAAK;AACzC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,GAAG;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,eAAe;AAAA,EAC9C;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,QAAQ,YAAY;AACzC,IAAG,WAAW,aAAa,OAAO,SAAY,cAAc;AAAA,EAC9D;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,QAAQ,YAAY;AACzC,IAAG,YAAY,cAAc,OAAO,OAAO;AAC3C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,SAAS,GAAG;AAAA,EAChD;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,UAAU,GAAG,OAAO,CAAC;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,YAAY,SAAS,OAAO,MAAM,IAAI,GAAG;AAAA,EAC9C;AACF;AACA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B;AAAA,EACA,YAAY,IAAI;AACd,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,GAAG;AAAA,EACjB;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,UAAU,CAAC;AAAA,EAC/F;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,IACtC,UAAU,CAAC,gBAAgB;AAAA,EAC7B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA,EAEA;AAAA,EACA,YAAY,WAAW,UAAU;AAC/B,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,OAAO,MAAM,aAAa;AACxB,SAAK,gBAAgB;AACrB,WAAO,KAAK,OAAO,MAAM,WAAW;AAAA,EACtC;AAAA;AAAA,EAEA,SAAS;AACP,UAAM,OAAO,KAAK;AAClB,QAAI,MAAM;AACR,WAAK,gBAAgB;AACrB,aAAO,KAAK,OAAO;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAM;AACpB,SAAK,gBAAgB;AAAA,EACvB;AACF;AAKA,IAAM,iBAAN,MAAqB;AAAA;AAAA,EAEnB;AAAA;AAAA,EAEA;AAAA,EACA,OAAO,QAAQ,aAAa;AAC1B,SAAK,kBAAkB;AACvB,WAAO,KAAK,sBAAsB,QAAQ,WAAW;AAAA,EACvD;AAAA,EACA,SAAS;AACP,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,gBAAgB;AAAA,IACvC;AACA,SAAK,kBAAkB;AACvB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW;AAChB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,aAAa,IAAI;AACf,SAAK,aAAa;AAAA,EACpB;AACF;AAKA,IAAM,WAAN,MAAe;AAAA,EACb;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,kBAAkB;AAAA;AAAA,EAElB,eAAe,IAAI,QAAQ;AAAA;AAAA,EAE3B,YAAY,IAAI,QAAQ;AAAA;AAAA,EAExB,eAAe,IAAI,QAAQ;AAAA;AAAA,EAE3B,gBAAgB,IAAI,QAAQ;AAAA;AAAA,EAE5B,kBAAkB,IAAI,QAAQ;AAAA,EAC9B,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA,EACA,eAAe;AACb,WAAO,KAAK,aAAa,aAAa;AAAA,EACxC;AAAA,EACA,eAAe;AACb,WAAO,KAAK,cAAc,aAAa;AAAA,EACzC;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,gBAAgB,aAAa;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACN,SAAK,YAAY,OAAO;AACxB,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,SAAS;AAC3B,SAAK,aAAa,SAAS;AAC3B,SAAK,UAAU,SAAS;AACxB,SAAK,cAAc,SAAS;AAC5B,SAAK,gBAAgB,SAAS;AAAA,EAChC;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,aAAa,aAAa;AAAA,EACxC;AAAA,EACA,aAAa;AACX,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK,UAAU,aAAa;AAAA,EACrC;AAAA;AAAA,EAEA,YAAY,cAAc,gBAAgB;AACxC,QAAI,cAAc;AAChB,WAAK,cAAc,KAAK;AAAA,IAC1B;AACA,QAAI,gBAAgB;AAClB,WAAK,gBAAgB,KAAK,EAAE,KAAK,eAAe;AAAA,IAClD;AAAA,EACF;AACF;AAKA,IAAM,eAAN,MAAmB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,IAAI,QAAQ;AAAA,EACrB,YAAY,IAAI,QAAQ;AAAA,EACxB,YAAY,SAAS,QAAQ,SAAS,OAAO,WAAW,UAAU;AAChE,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,SAAS,YAAY,EAAE,UAAU,MAAM;AAC1C,WAAK,UAAU,SAAS;AACxB,WAAK,OAAO,SAAS;AAAA,IACvB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,OAAO,KAAK;AACjB,QAAI,KAAK,OAAO,cAAc;AAC5B,WAAK,OAAO,SAAS;AAAA,IACvB;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA;AAAA,EAEA,cAAc,QAAQ;AACpB,SAAK,UAAU,KAAK,MAAM;AAAA,EAC5B;AAAA,EACA,WAAW;AACT,WAAO,KAAK,UAAU,aAAa;AAAA,EACrC;AACF;AACA,IAAM,iCAAiC;AAAA,EACrC,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,aAAa;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,mBAAmB;AACrB;AACA,IAAM,eAAe,IAAI,eAAe,aAAa;AAQrD,IAAM,gBAAN,cAA4B,eAAe;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,iBAAiB,2BAA2B,SAAS;AAC/D,UAAM;AACN,SAAK,kBAAkB;AACvB,SAAK,4BAA4B;AACjC,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,QAAQ,aAAa;AACzC,UAAM,mBAAmB,KAAK,0BAA0B,wBAAwB,OAAO,SAAS;AAChG,QAAI;AAMJ,mBAAe,iBAAiB,OAAO,OAAO,QAAQ;AAKtD,SAAK,QAAQ,WAAW,aAAa,QAAQ;AAC7C,SAAK,aAAa,MAAM;AACtB,WAAK,QAAQ,WAAW,aAAa,QAAQ;AAC7C,mBAAa,QAAQ;AAAA,IACvB,CAAC;AAGD,QAAI,aAAa;AACf,WAAK,gBAAgB,aAAa,KAAK,sBAAsB,YAAY,GAAG,KAAK,gBAAgB,UAAU;AAAA,IAC7G,OAAO;AACL,WAAK,gBAAgB,YAAY,KAAK,sBAAsB,YAAY,CAAC;AAAA,IAC3E;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,sBAAsB,cAAc;AAClC,WAAO,aAAa,SAAS,UAAU,CAAC;AAAA,EAC1C;AACF;AAGA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,qBAAqB,KAAK,kBAAkB,YAAY;AAC/D,WAAK,kBAAkB,WAAW,YAAY,KAAK,iBAAiB;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB;AACpB,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,iBAAiB;AAAA,IACxB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACjB,UAAM,YAAY,KAAK,UAAU,cAAc,KAAK;AACpD,cAAU,UAAU,IAAI,mBAAmB;AAC3C,cAAU,aAAa,aAAa,QAAQ;AAC5C,SAAK,UAAU,KAAK,YAAY,SAAS;AACzC,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,aAAN,MAAiB;AAAA,EACf;AAAA,EACA,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,QAAQ,cAAc,MAAM;AACjC,WAAO,KAAK,YAAY,OAAO,QAAQ,WAAW;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO,KAAK,YAAY,OAAO;AAAA,EACjC;AACF;AAUA,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,4BAA4B,OAAO,0BAAwB;AAAA,EAC3D,UAAU,OAAO,cAAc;AAAA,EAC/B,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B,gBAAgB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,OAAO,eAAe,kBAAkB;AAEtC,WAAO,KAAK,kBAAkB,KAAK,eAAe,eAAe,gBAAgB,CAAC;AAAA,EACpF;AAAA,EACA,eAAe,gBAAgB,IAAI,kBAAkB;AACnD,QAAI,CAAC,KAAK,cAAc,IAAI,gBAAgB,GAAG;AAC7C,WAAK,cAAc,IAAI,kBAAkB,CAAC,CAAC;AAAA,IAC7C;AACA,QAAI,CAAC,KAAK,cAAc,IAAI,gBAAgB,EAAE,aAAa,GAAG;AAC5D,WAAK,cAAc,IAAI,gBAAgB,EAAE,aAAa,IAAI,KAAK,mBAAmB,eAAe,gBAAgB;AAAA,IACnH;AACA,WAAO,KAAK,cAAc,IAAI,gBAAgB,EAAE,aAAa;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,eAAe,kBAAkB;AAClD,UAAM,OAAO,KAAK,UAAU,cAAc,KAAK;AAC/C,SAAK,KAAK;AACV,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,UAAU,IAAI,iBAAiB;AACpC,QAAI,CAAC,kBAAkB;AACrB,WAAK,kBAAkB,oBAAoB,EAAE,YAAY,IAAI;AAAA,IAC/D,OAAO;AACL,uBAAiB,oBAAoB,EAAE,YAAY,IAAI;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,MAAM;AACtB,WAAO,IAAI,cAAc,MAAM,KAAK,2BAA2B,KAAK,OAAO;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,MAAM;AACtB,WAAO,IAAI,WAAW,KAAK,kBAAkB,IAAI,CAAC;AAAA,EACpD;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAS;AAAA,EAC5C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,SAAQ;AAAA,IACjB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,SAAS,CAAC;AAAA,EACV;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,YAAY,OAAO,SAAS,WAAW,WAAW,QAAQ;AACxD,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,eAAe,kCACf,MAAM,UACN,MAAM;AAEX,QAAI,MAAM,OAAO,aAAa;AAC5B,WAAK,aAAa,cAAc,kCAC3B,MAAM,QAAQ,cACd,MAAM,OAAO;AAAA,IAEpB;AAAA,EACF;AAAA;AAAA,EAEA,KAAK,SAAS,OAAO,WAAW,CAAC,GAAG,OAAO,IAAI;AAC7C,WAAO,KAAK,sBAAsB,MAAM,SAAS,OAAO,KAAK,YAAY,QAAQ,CAAC;AAAA,EACpF;AAAA;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,CAAC,GAAG;AACrC,UAAM,OAAO,KAAK,aAAa,YAAY,WAAW;AACtD,WAAO,KAAK,sBAAsB,MAAM,SAAS,OAAO,KAAK,YAAY,QAAQ,CAAC;AAAA,EACpF;AAAA;AAAA,EAEA,MAAM,SAAS,OAAO,WAAW,CAAC,GAAG;AACnC,UAAM,OAAO,KAAK,aAAa,YAAY,SAAS;AACpD,WAAO,KAAK,sBAAsB,MAAM,SAAS,OAAO,KAAK,YAAY,QAAQ,CAAC;AAAA,EACpF;AAAA;AAAA,EAEA,KAAK,SAAS,OAAO,WAAW,CAAC,GAAG;AAClC,UAAM,OAAO,KAAK,aAAa,YAAY,QAAQ;AACnD,WAAO,KAAK,sBAAsB,MAAM,SAAS,OAAO,KAAK,YAAY,QAAQ,CAAC;AAAA,EACpF;AAAA;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,CAAC,GAAG;AACrC,UAAM,OAAO,KAAK,aAAa,YAAY,WAAW;AACtD,WAAO,KAAK,sBAAsB,MAAM,SAAS,OAAO,KAAK,YAAY,QAAQ,CAAC;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,SAAS;AAEb,eAAW,SAAS,KAAK,QAAQ;AAC/B,UAAI,YAAY,QAAW;AACzB,YAAI,MAAM,YAAY,SAAS;AAC7B,gBAAM,SAAS,YAAY;AAC3B;AAAA,QACF;AAAA,MACF,OAAO;AACL,cAAM,SAAS,YAAY;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS;AACd,UAAM,QAAQ,KAAK,WAAW,OAAO;AACrC,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,UAAM,YAAY,SAAS,MAAM;AACjC,SAAK,OAAO,OAAO,MAAM,OAAO,CAAC;AACjC,SAAK,kBAAkB,KAAK,kBAAkB;AAC9C,QAAI,CAAC,KAAK,aAAa,aAAa,CAAC,KAAK,OAAO,QAAQ;AACvD,aAAO;AAAA,IACT;AACA,QAAI,KAAK,kBAAkB,KAAK,aAAa,aAAa,KAAK,OAAO,KAAK,eAAe,GAAG;AAC3F,YAAM,IAAI,KAAK,OAAO,KAAK,eAAe,EAAE;AAC5C,UAAI,CAAC,EAAE,WAAW,GAAG;AACnB,aAAK,kBAAkB,KAAK,kBAAkB;AAC9C,UAAE,SAAS;AAAA,MACb;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,QAAQ,IAAI,UAAU,IAAI,kBAAkB,iBAAiB;AACzE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,eAAW,SAAS,KAAK,QAAQ;AAC/B,YAAM,oBAAoB,0BAA0B,MAAM,UAAU;AACpE,WAAK,CAAC,0BAA0B,sBAAsB,MAAM,YAAY,SAAS;AAC/E,cAAM,SAAS,YAAY,kBAAkB,eAAe;AAC5D,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,YAAY,WAAW,CAAC,GAAG;AACzB,WAAO,kCACF,KAAK,eACL;AAAA,EAEP;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,SAAS;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,UAAI,KAAK,OAAO,CAAC,EAAE,YAAY,SAAS;AACtC,eAAO;AAAA,UACL,OAAO;AAAA,UACP,aAAa,KAAK,OAAO,CAAC;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB,WAAW,SAAS,OAAO,QAAQ;AACvD,QAAI,OAAO,gBAAgB;AACzB,aAAO,KAAK,OAAO,IAAI,MAAM,KAAK,mBAAmB,WAAW,SAAS,OAAO,MAAM,CAAC;AAAA,IACzF;AACA,WAAO,KAAK,mBAAmB,WAAW,SAAS,OAAO,MAAM;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,WAAW,SAAS,OAAO,QAAQ;AACpD,QAAI,CAAC,OAAO,gBAAgB;AAC1B,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AAIA,UAAM,YAAY,KAAK,cAAc,OAAO,SAAS,KAAK,aAAa,2BAA2B,OAAO,UAAU,GAAG,KAAK,aAAa,eAAe;AACvJ,SAAK,KAAK,aAAa,0BAA0B,SAAS,YAAY,KAAK,aAAa,qBAAqB,cAAc,MAAM;AAC/H,aAAO;AAAA,IACT;AACA,SAAK,uBAAuB;AAC5B,QAAI,eAAe;AACnB,QAAI,KAAK,aAAa,aAAa,KAAK,mBAAmB,KAAK,aAAa,WAAW;AACtF,qBAAe;AACf,UAAI,KAAK,aAAa,aAAa;AACjC,aAAK,MAAM,KAAK,OAAO,CAAC,EAAE,OAAO;AAAA,MACnC;AAAA,IACF;AACA,UAAM,aAAa,KAAK,QAAQ,OAAO,OAAO,eAAe,KAAK,gBAAgB;AAClF,SAAK,QAAQ,KAAK,QAAQ;AAC1B,QAAI,mBAAmB;AACvB,QAAI,WAAW,OAAO,YAAY;AAChC,yBAAmB,KAAK,UAAU,SAAS,gBAAgB,MAAM,OAAO;AAAA,IAC1E;AACA,UAAM,WAAW,IAAI,SAAS,UAAU;AACxC,UAAM,eAAe,IAAI,aAAa,KAAK,OAAO,QAAQ,kBAAkB,OAAO,WAAW,QAAQ;AAEtG,UAAM,YAAY,CAAC;AAAA,MACjB,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,gBAAgB,SAAS,OAAO;AAAA,MACpC;AAAA,MACA,QAAQ,KAAK;AAAA,IACf,CAAC;AACD,UAAM,YAAY,IAAI,gBAAgB,OAAO,gBAAgB,aAAa;AAC1E,UAAM,SAAS,WAAW,OAAO,WAAW,OAAO,WAAW;AAC9D,aAAS,oBAAoB,OAAO;AACpC,UAAM,MAAM;AAAA,MACV,SAAS,KAAK;AAAA,MACd,OAAO,SAAS;AAAA,MAChB,SAAS,WAAW;AAAA,MACpB;AAAA,MACA,SAAS,SAAS,cAAc;AAAA,MAChC,UAAU,SAAS,YAAY;AAAA,MAC/B,OAAO,aAAa,MAAM;AAAA,MAC1B,UAAU,aAAa,SAAS;AAAA,MAChC;AAAA,IACF;AACA,QAAI,CAAC,cAAc;AACjB,WAAK,kBAAkB,KAAK,kBAAkB;AAC9C,iBAAW,MAAM;AACf,YAAI,SAAS,SAAS;AAAA,MACxB,CAAC;AAAA,IACH;AACA,SAAK,OAAO,KAAK,GAAG;AACpB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAkB,SAAS,YAAY,GAAM,SAAS,OAAO,GAAM,SAAY,QAAQ,GAAM,SAAY,YAAY,GAAM,SAAY,MAAM,CAAC;AAAA,EACjL;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,IACvB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,QAAN,MAAM,OAAM;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,QAAQ,OAAO,EAAE;AAAA;AAAA,EAEjB,eAAe;AAAA,EACf;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,IAAI,eAAe;AACjB,QAAI,KAAK,MAAM,EAAE,UAAU,YAAY;AACrC,aAAO;AAAA,IACT;AACA;AAAA,EACF;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,eAAe,cAAc,QAAQ;AAC/C,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,UAAU,aAAa;AAC5B,SAAK,QAAQ,aAAa;AAC1B,SAAK,UAAU,aAAa;AAC5B,SAAK,kBAAkB,aAAa,OAAO;AAC3C,SAAK,eAAe,GAAG,aAAa,SAAS,IAAI,aAAa,OAAO,UAAU;AAC/E,SAAK,MAAM,aAAa,SAAS,cAAc,EAAE,UAAU,MAAM;AAC/D,WAAK,cAAc;AAAA,IACrB,CAAC;AACD,SAAK,OAAO,aAAa,SAAS,aAAa,EAAE,UAAU,MAAM;AAC/D,WAAK,OAAO;AAAA,IACd,CAAC;AACD,SAAK,OAAO,aAAa,SAAS,aAAa,EAAE,UAAU,MAAM;AAC/D,WAAK,aAAa;AAAA,IACpB,CAAC;AACD,SAAK,OAAO,aAAa,SAAS,eAAe,EAAE,UAAU,WAAS;AACpE,WAAK,kBAAkB;AAAA,IACzB,CAAC;AACD,SAAK,QAAQ,OAAO;AAAA,MAClB,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,UAAU,KAAK,aAAa,OAAO;AAAA,QACnC,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AACrB,SAAK,KAAK,YAAY;AACtB,SAAK,KAAK,YAAY;AACtB,SAAK,KAAK,YAAY;AACtB,kBAAc,KAAK,UAAU;AAC7B,iBAAa,KAAK,OAAO;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,SAAK,MAAM,OAAO,CAAAA,WAAU,iCACvBA,SADuB;AAAA,MAE1B,OAAO;AAAA,IACT,EAAE;AACF,QAAI,EAAE,KAAK,QAAQ,mBAAmB,QAAQ,KAAK,QAAQ,mBAAmB,cAAc,KAAK,QAAQ,SAAS;AAChH,WAAK,eAAe,MAAM,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO;AAC7D,WAAK,YAAW,oBAAI,KAAK,GAAE,QAAQ,IAAI,KAAK,QAAQ;AACpD,UAAI,KAAK,QAAQ,aAAa;AAC5B,aAAK,gBAAgB,MAAM,KAAK,eAAe,GAAG,EAAE;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,OAAO,CAAC,KAAK,QAAQ,SAAS;AACvE;AAAA,IACF;AACA,UAAM,OAAM,oBAAI,KAAK,GAAE,QAAQ;AAC/B,UAAM,YAAY,KAAK,WAAW;AAClC,SAAK,MAAM,IAAI,YAAY,KAAK,QAAQ,UAAU,GAAG;AACrD,QAAI,KAAK,QAAQ,sBAAsB,cAAc;AACnD,WAAK,MAAM,OAAO,WAAS,MAAM,KAAK;AAAA,IACxC;AACA,QAAI,KAAK,MAAM,KAAK,GAAG;AACrB,WAAK,MAAM,IAAI,CAAC;AAAA,IAClB;AACA,QAAI,KAAK,MAAM,KAAK,KAAK;AACvB,WAAK,MAAM,IAAI,GAAG;AAAA,IACpB;AAAA,EACF;AAAA,EACA,eAAe;AACb,iBAAa,KAAK,OAAO;AACzB,kBAAc,KAAK,UAAU;AAC7B,SAAK,MAAM,OAAO,CAAAA,WAAU,iCACvBA,SADuB;AAAA,MAE1B,OAAO;AAAA,IACT,EAAE;AACF,SAAK,eAAe,MAAM,KAAK,OAAO,GAAG,KAAK,eAAe;AAC7D,SAAK,QAAQ,UAAU,KAAK;AAC5B,SAAK,YAAW,oBAAI,KAAK,GAAE,QAAQ,KAAK,KAAK,QAAQ,WAAW;AAChE,SAAK,MAAM,IAAI,EAAE;AACjB,QAAI,KAAK,QAAQ,aAAa;AAC5B,WAAK,gBAAgB,MAAM,KAAK,eAAe,GAAG,EAAE;AAAA,IACtD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,QAAI,KAAK,MAAM,EAAE,UAAU,WAAW;AACpC;AAAA,IACF;AACA,iBAAa,KAAK,OAAO;AACzB,SAAK,MAAM,OAAO,CAAAA,WAAU,iCACvBA,SADuB;AAAA,MAE1B,OAAO;AAAA,IACT,EAAE;AACF,SAAK,eAAe,MAAM,KAAK,cAAc,OAAO,KAAK,aAAa,OAAO,GAAG,CAAC,KAAK,aAAa,OAAO,QAAQ;AAAA,EACpH;AAAA,EACA,WAAW;AACT,QAAI,KAAK,MAAM,EAAE,UAAU,WAAW;AACpC;AAAA,IACF;AACA,SAAK,aAAa,WAAW;AAC7B,QAAI,KAAK,QAAQ,cAAc;AAC7B,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,MAAM,EAAE,UAAU,WAAW;AACpC;AAAA,IACF;AACA,QAAI,KAAK,QAAQ,mBAAmB,mBAAmB;AACrD,mBAAa,KAAK,OAAO;AACzB,WAAK,QAAQ,UAAU;AACvB,WAAK,WAAW;AAEhB,oBAAc,KAAK,UAAU;AAC7B,WAAK,MAAM,IAAI,CAAC;AAAA,IAClB;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,QAAQ,mBAAmB,QAAQ,KAAK,QAAQ,mBAAmB,qBAAqB,KAAK,QAAQ,oBAAoB,KAAK,KAAK,MAAM,EAAE,UAAU,WAAW;AACvK;AAAA,IACF;AACA,SAAK,eAAe,MAAM,KAAK,OAAO,GAAG,KAAK,QAAQ,eAAe;AACrE,SAAK,QAAQ,UAAU,KAAK,QAAQ;AACpC,SAAK,YAAW,oBAAI,KAAK,GAAE,QAAQ,KAAK,KAAK,QAAQ,WAAW;AAChE,SAAK,MAAM,IAAI,EAAE;AACjB,QAAI,KAAK,QAAQ,aAAa;AAC5B,WAAK,gBAAgB,MAAM,KAAK,eAAe,GAAG,EAAE;AAAA,IACtD;AAAA,EACF;AAAA,EACA,eAAe,MAAM,SAAS;AAC5B,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,kBAAkB,MAAM,KAAK,UAAU,WAAW,MAAM,KAAK,iBAAiB,IAAI,GAAG,OAAO,CAAC;AAAA,IAC3G,OAAO;AACL,WAAK,UAAU,WAAW,MAAM,KAAK,GAAG,OAAO;AAAA,IACjD;AAAA,EACF;AAAA,EACA,gBAAgB,MAAM,SAAS;AAC7B,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,kBAAkB,MAAM,KAAK,aAAa,YAAY,MAAM,KAAK,iBAAiB,IAAI,GAAG,OAAO,CAAC;AAAA,IAC/G,OAAO;AACL,WAAK,aAAa,YAAY,MAAM,KAAK,GAAG,OAAO;AAAA,IACrD;AAAA,EACF;AAAA,EACA,iBAAiB,MAAM;AACrB,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,IAAI,MAAM,KAAK,CAAC;AAAA,IAC9B,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,cAAc,mBAAmB;AACtD,WAAO,KAAK,qBAAqB,QAAU,kBAAkB,aAAa,GAAM,kBAAkB,YAAY,GAAM,kBAAqB,MAAM,CAAC;AAAA,EAClJ;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,UAAU;AAAA,IACV,cAAc,SAAS,mBAAmB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,iCAAiC;AAC/D,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC,EAAE,cAAc,SAAS,sCAAsC;AAC9D,iBAAO,IAAI,YAAY;AAAA,QACzB,CAAC,EAAE,cAAc,SAAS,sCAAsC;AAC9D,iBAAO,IAAI,iBAAiB;AAAA,QAC9B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,wBAAwB,aAAa,IAAI,MAAM;AAClD,QAAG,WAAW,IAAI,YAAY;AAC9B,QAAG,YAAY,WAAW,IAAI,YAAY;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,UAAU,SAAS,sBAAsB,cAAc,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,SAAS,aAAa,GAAG,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,cAAc,SAAS,GAAG,sBAAsB,GAAG,OAAO,GAAG,CAAC,eAAe,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,WAAW,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,GAAG,gBAAgB,CAAC;AAAA,IACra,UAAU,SAAS,eAAe,IAAI,KAAK;AACzC,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,yBAAyB,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,sBAAsB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,sBAAsB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,sBAAsB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,sBAAsB,GAAG,GAAG,OAAO,CAAC;AAAA,MACjO;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,QAAQ,WAAW;AAC7C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,KAAK;AAC/B,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,WAAW,IAAI,QAAQ,UAAU;AAC3D,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,WAAW,CAAC,IAAI,QAAQ,UAAU;AAC5D,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,QAAQ,WAAW;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,IAAI;AAAA,IACnB,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,YAAY,CAAC,MAAM,YAAY,MAAM;AAAA,QACvD,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,MAAM,UAAU,MAAM;AAAA,QACzB,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QAC1B,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,WAAW,sBAAsB,QAAQ,+BAA+B,CAAC,GAAG,WAAW,qBAAqB,QAAQ,+BAA+B,CAAC,CAAC,CAAC,CAAC;AAAA,IAC9J;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,IAC9E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBV,YAAY,CAAC,QAAQ,YAAY,CAAC,MAAM,YAAY,MAAM;AAAA,QACxD,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,MAAM,UAAU,MAAM;AAAA,QACzB,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QAC1B,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,WAAW,sBAAsB,QAAQ,+BAA+B,CAAC,GAAG,WAAW,qBAAqB,QAAQ,+BAA+B,CAAC,CAAC,CAAC,CAAC;AAAA,MAC5J,qBAAqB;AAAA,MACrB,YAAY;AAAA,MACZ,SAAS,CAAC,IAAI;AAAA,MACd,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAsB,iCACvB,iCADuB;AAAA,EAE1B,gBAAgB;AAClB;AAqBA,IAAM,gBAAgB,CAAC,SAAS,CAAC,MAAM;AACrC,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,UAAU;AAAA,MACR,SAAS;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,yBAAyB,SAAS;AAC3C;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,QAAQ,SAAS,CAAC,GAAG;AAC1B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,cAAc,MAAM,CAAC;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,KAAK;AAAA,IACf,SAAS,CAAC,KAAK;AAAA,EACjB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,KAAK;AAAA,MACf,SAAS,CAAC,KAAK;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,OAAO,QAAQ,SAAS,CAAC,GAAG;AAC1B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,SAAS;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,QAAQ,OAAO,EAAE;AAAA;AAAA,EAEjB,eAAe;AAAA;AAAA,EAEf,IAAI,eAAe;AACjB,QAAI,KAAK,MAAM,MAAM,YAAY;AAC/B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,QAAQ,OAAO,UAAU;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,eAAe,cAAc,QAAQ;AAC/C,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,UAAU,aAAa;AAC5B,SAAK,QAAQ,aAAa;AAC1B,SAAK,UAAU,aAAa;AAC5B,SAAK,kBAAkB,aAAa,OAAO;AAC3C,SAAK,eAAe,GAAG,aAAa,SAAS,IAAI,aAAa,OAAO,UAAU;AAC/E,SAAK,MAAM,aAAa,SAAS,cAAc,EAAE,UAAU,MAAM;AAC/D,WAAK,cAAc;AAAA,IACrB,CAAC;AACD,SAAK,OAAO,aAAa,SAAS,aAAa,EAAE,UAAU,MAAM;AAC/D,WAAK,OAAO;AAAA,IACd,CAAC;AACD,SAAK,OAAO,aAAa,SAAS,aAAa,EAAE,UAAU,MAAM;AAC/D,WAAK,aAAa;AAAA,IACpB,CAAC;AACD,SAAK,OAAO,aAAa,SAAS,eAAe,EAAE,UAAU,WAAS;AACpE,WAAK,kBAAkB;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AACrB,SAAK,KAAK,YAAY;AACtB,SAAK,KAAK,YAAY;AACtB,SAAK,KAAK,YAAY;AACtB,kBAAc,KAAK,UAAU;AAC7B,iBAAa,KAAK,OAAO;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,SAAK,MAAM,IAAI,QAAQ;AACvB,QAAI,EAAE,KAAK,QAAQ,mBAAmB,QAAQ,KAAK,QAAQ,mBAAmB,cAAc,KAAK,QAAQ,SAAS;AAChH,WAAK,UAAU,WAAW,MAAM;AAC9B,aAAK,OAAO;AAAA,MACd,GAAG,KAAK,QAAQ,OAAO;AACvB,WAAK,YAAW,oBAAI,KAAK,GAAE,QAAQ,IAAI,KAAK,QAAQ;AACpD,UAAI,KAAK,QAAQ,aAAa;AAC5B,aAAK,aAAa,YAAY,MAAM,KAAK,eAAe,GAAG,EAAE;AAAA,MAC/D;AAAA,IACF;AACA,QAAI,KAAK,QAAQ,gBAAgB;AAC/B,WAAK,OAAO,KAAK;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,OAAO,CAAC,KAAK,QAAQ,SAAS;AACvE;AAAA,IACF;AACA,UAAM,OAAM,oBAAI,KAAK,GAAE,QAAQ;AAC/B,UAAM,YAAY,KAAK,WAAW;AAClC,SAAK,MAAM,IAAI,YAAY,KAAK,QAAQ,UAAU,GAAG;AACrD,QAAI,KAAK,QAAQ,sBAAsB,cAAc;AACnD,WAAK,MAAM,OAAO,WAAS,MAAM,KAAK;AAAA,IACxC;AACA,QAAI,KAAK,MAAM,KAAK,GAAG;AACrB,WAAK,MAAM,IAAI,CAAC;AAAA,IAClB;AACA,QAAI,KAAK,MAAM,KAAK,KAAK;AACvB,WAAK,MAAM,IAAI,GAAG;AAAA,IACpB;AAAA,EACF;AAAA,EACA,eAAe;AACb,iBAAa,KAAK,OAAO;AACzB,kBAAc,KAAK,UAAU;AAC7B,SAAK,MAAM,IAAI,QAAQ;AACvB,SAAK,QAAQ,UAAU,KAAK;AAC5B,SAAK,UAAU,WAAW,MAAM,KAAK,OAAO,GAAG,KAAK,eAAe;AACnE,SAAK,YAAW,oBAAI,KAAK,GAAE,QAAQ,KAAK,KAAK,mBAAmB;AAChE,SAAK,MAAM,IAAI,EAAE;AACjB,QAAI,KAAK,QAAQ,aAAa;AAC5B,WAAK,aAAa,YAAY,MAAM,KAAK,eAAe,GAAG,EAAE;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,QAAI,KAAK,MAAM,MAAM,WAAW;AAC9B;AAAA,IACF;AACA,iBAAa,KAAK,OAAO;AACzB,SAAK,MAAM,IAAI,SAAS;AACxB,SAAK,UAAU,WAAW,MAAM,KAAK,cAAc,OAAO,KAAK,aAAa,OAAO,CAAC;AAAA,EACtF;AAAA,EACA,WAAW;AACT,QAAI,KAAK,MAAM,MAAM,WAAW;AAC9B;AAAA,IACF;AACA,SAAK,aAAa,WAAW;AAC7B,QAAI,KAAK,QAAQ,cAAc;AAC7B,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,MAAM,MAAM,WAAW;AAC9B;AAAA,IACF;AACA,iBAAa,KAAK,OAAO;AACzB,SAAK,QAAQ,UAAU;AACvB,SAAK,WAAW;AAEhB,kBAAc,KAAK,UAAU;AAC7B,SAAK,MAAM,IAAI,CAAC;AAAA,EAClB;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,QAAQ,mBAAmB,QAAQ,KAAK,QAAQ,mBAAmB,qBAAqB,KAAK,QAAQ,oBAAoB,KAAK,KAAK,MAAM,MAAM,WAAW;AACjK;AAAA,IACF;AACA,SAAK,UAAU,WAAW,MAAM,KAAK,OAAO,GAAG,KAAK,QAAQ,eAAe;AAC3E,SAAK,QAAQ,UAAU,KAAK,QAAQ;AACpC,SAAK,YAAW,oBAAI,KAAK,GAAE,QAAQ,KAAK,KAAK,QAAQ,WAAW;AAChE,SAAK,MAAM,IAAI,EAAE;AACjB,QAAI,KAAK,QAAQ,aAAa;AAC5B,WAAK,aAAa,YAAY,MAAM,KAAK,eAAe,GAAG,EAAE;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,kBAAkB,aAAa,GAAM,kBAAkB,YAAY,GAAM,kBAAqB,cAAc,CAAC;AAAA,EACrK;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,4CAA4C;AAC1E,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC,EAAE,cAAc,SAAS,iDAAiD;AACzE,iBAAO,IAAI,YAAY;AAAA,QACzB,CAAC,EAAE,cAAc,SAAS,iDAAiD;AACzE,iBAAO,IAAI,iBAAiB;AAAA,QAC9B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,YAAY;AAC9B,QAAG,YAAY,WAAW,IAAI,YAAY;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,UAAU,SAAS,sBAAsB,cAAc,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,SAAS,aAAa,GAAG,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,cAAc,SAAS,GAAG,sBAAsB,GAAG,OAAO,GAAG,CAAC,eAAe,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,WAAW,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,GAAG,gBAAgB,CAAC;AAAA,IACra,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,oCAAoC,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,iCAAiC,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,iCAAiC,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,iCAAiC,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,iCAAiC,GAAG,GAAG,OAAO,CAAC;AAAA,MACxR;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,QAAQ,WAAW;AAC7C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,KAAK;AAC/B,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,WAAW,IAAI,QAAQ,UAAU;AAC3D,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,WAAW,CAAC,IAAI,QAAQ,UAAU;AAC5D,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,QAAQ,WAAW;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,IAAI;AAAA,IACnB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBV,YAAY;AAAA,MACZ,SAAS,CAAC,IAAI;AAAA,MACd,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kCAAkC,iCACnC,iCADmC;AAAA,EAEtC,gBAAgB;AAClB;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,QAAQ,SAAS,CAAC,GAAG;AAC1B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,SAAS;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,gBAAgB;AAAA,IAC1B,SAAS,CAAC,gBAAgB;AAAA,EAC5B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,gBAAgB;AAAA,MAC1B,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["state"]}