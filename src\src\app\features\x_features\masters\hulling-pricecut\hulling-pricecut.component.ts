import { Component, computed, inject, input, output, resource, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { MatNativeDateModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { HullingPricecut } from '../../../../models/x_models/masters/hulling-pricecut';
import { HullingPricecutService } from '../../../../services/x_apis/masters/hulling-pricecut.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-hulling-pricecut',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule,
    MatToolbarModule,
    MatFormFieldModule],
  templateUrl: './hulling-pricecut.component.html',
  styleUrl: './hulling-pricecut.component.scss'
})
export class HullingPricecutComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  hullingPricecutService = inject(HullingPricecutService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);


  translate = inject(TranslateService);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  seasons = resource({ loader: () => this.lookupService.getSeason() }).value;

  products = resource({ loader: () => this.lookupService.getProduct() }).value; // for temp
  pricetypes = resource({ loader: () => this.lookupService.getHullingPriceCut() }).value; // for temp


  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  router = inject(Router);

  uniqueId = input.required<any>();
  hullingPriceCutTypeName = input.required<any>();
  hullingpricecut = signal<HullingPricecut | null>(null);

  form = this.fb.group({
    id: this.fb.control<number>(0),
    hullingPriceCutType: this.fb.control<number | null>(null, Validators.required),
    productId: this.fb.control<number | null>(null, Validators.required),
    seasonId: this.fb.control<number | null>(null, Validators.required),
    regionId: this.fb.control<number | null>(null),
    minRange: this.fb.control<number | null>(null, Validators.required),
    maxRange: this.fb.control<number | null>(null, Validators.required),
    priceCut: this.fb.control<number | null>(null, Validators.required)
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

  readonly search = signal('');

  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  readonly filteredSeasons = computed(() =>
    this.seasons()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  readonly filteredProducts = computed(() =>
    this.products()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );
  
  readonly filteredPricetypes = computed(() =>
    this.pricetypes()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  filter(value: any) {
    this.search.set(value.target.value);
  }

  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
    this.products.set(await this.lookupService.getProduct());
  }

  async getFormData() {
    const res = await this.hullingPricecutService.getById(this.uniqueId());
    this.hullingpricecut.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.hullingPricecutService.create(formValue as HullingPricecut)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Hulling Price Cut Created successfully.`)
        }
        else {
          this.alertService.success(`Hulling Price Cut Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

}
