import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { catchError, Observable, of } from 'rxjs';
import { PayrollService } from '../../../services/m_apis/service/payroll.service';
import { MatSpinner } from '@angular/material/progress-spinner';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';

// Define Employee interface to match your data structure
interface Employee {
  id?: number;
  employeeRole: string;
  matricId: string;
  level: number;
  basicPay: number;
}

@Component({
  selector: 'app-matrixpay',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    MatTableModule,
    MatIconModule,
    MatSnackBarModule,
    MatDialogModule,
    MatSpinner
  ],
  templateUrl: './matrixpay.component.html',
  styleUrl: './matrixpay.component.scss'
})
export class MatrixpayComponent implements OnInit {

  CreateForm!: FormGroup;

  isLoading: boolean = false;  // dataSource = new MatTableDataSource<any>();
  displayedColumns: string[] = ['id','matricId', 'employeeRole',  'level', 'basicPay', 'actions'];
  isSubmitting = false;

  dataSource: any[] = [];
  mainList: any[] = [];
  editingEmployeeRole: string | null = null;
editingLevel: string | null = null;


  private snackBar = inject(MatSnackBar);
  List: any;

  get f() {
    return this.CreateForm.controls;
  }

  constructor(
    private fb: FormBuilder,
    private Service: PayrollService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    // this.loadEmployees();
    this.getMatrixList();
  }

  private initializeForm(): void {
    this.CreateForm = this.fb.group({
      employeeRole: ['', [Validators.required, Validators.minLength(2)]],
      matrixId: ['', [Validators.required]],
      level: ['', Validators.required],
      basicPay: [0, [Validators.required, Validators.min(1000)]]
    });
  }


  getMatrixList() {
    this.isLoading = true;

    this.Service.getAllmatrix().subscribe({
      next: (data) => {
        this.mainList = [];
        console.log('Employee data:', (data as any).responseData);

        this.mainList = (data as any).responseData || [];
        this.dataSource = this.mainList;

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching employee list:', error);
        this.isLoading = false;
        this.dataSource = [];
        this.mainList = [];
        

      }
    });
  }



  public submit(): void {
    if (this.CreateForm.valid) {
      this.isSubmitting = true;
      const employeeData: Employee = this.CreateForm.value;

      this.Service.creatematrix(employeeData).subscribe({
        next: (response: any) => {
          if (response) {
            this.showSnackBar('Employee created successfully!', 'success');
            this.CreateForm.reset();
            this.refreshData();
          }
          this.CreateForm.reset;
          this.isSubmitting = false;
        },
        error: () => {
          this.isSubmitting = false;
        }
      });
    } else {
      this.markFormGroupTouched();
      this.showSnackBar('Please fill all required fields correctly', 'error');
    }
  }

  refreshData(): void {
    this.getMatrixList();
  }

  // deleteEmployee(id: number): void {
  //   if (confirm('Are you sure you want to delete this employee?')) {
  //     this.Service.deleteEmployee(id.toString()).pipe(
  //       catchError(error => {
  //         console.error('Error deleting employee:', error);
  //         this.showSnackBar('Failed to delete employee', 'error');
  //         return of(null);
  //       })
  //     ).subscribe({
  //       next: (response: null) => {
  //         if (response !== null) {
  //           this.showSnackBar('Employee deleted successfully!', 'success');
  //           this.refreshData();
  //         }
  //       }
  //     });
  //   }
  // }


 deleteMatrix(employeeRole: string, level: string): void {
  if (confirm(`Are you sure you want to delete ${employeeRole} at level ${level}?`)) {
    this.Service.deleteByRoleAndLevel(employeeRole, level).subscribe({
      next: () => {
        Swal.fire('Deleted!', 'Employee matrix deleted successfully.', 'success');
        this.getMatrixList(); // reload list
      },
      error: () => {
        Swal.fire('Error!', 'Could not delete the employee matrix.', 'error');
        console.error();
      }
    });
  }
}


editEmployee(employee: Employee): void {
  // Patch the form with selected employee data
  this.CreateForm.patchValue({
    employeeRole: employee.employeeRole,
    matrixId: employee.matricId,
    level: employee.level,
    basicPay: employee.basicPay
  });

  // Store the original identifiers for update (used as path variables)
  this.editingEmployeeRole = employee.employeeRole;
  

  // Optionally disable the identifying fields to prevent accidental editing
  this.CreateForm.get('employeeRole')?.disable();
  this.CreateForm.get('level')?.disable();

  // Scroll to the form section for user convenience
  document.querySelector('.form-section')?.scrollIntoView({ behavior: 'smooth' });
}

updateEmployee(): void {
  if (this.CreateForm.valid && this.editingEmployeeRole && this.editingLevel) {
    const updatedEmployee: Employee = this.CreateForm.value;

    this.isSubmitting = true;

    this.Service.updateMatrixByRoleAndLevel(this.editingEmployeeRole, this.editingLevel, updatedEmployee).subscribe({
      next: () => {
        this.showSnackBar('Employee updated successfully!', 'success');
        this.CreateForm.reset();
        this.editingEmployeeRole = null;
        this.editingLevel = null;
        this.refreshData();
        this.isSubmitting = false;
      },
      error: () => {
        this.isSubmitting = false;
        this.showSnackBar('Failed to update employee', 'error');
      }
    });
  } else {
    this.markFormGroupTouched();
    this.showSnackBar('Please fill all required fields correctly', 'error');
  }
}
cancelEdit(): void {
  this.CreateForm.reset();
  this.editingEmployeeRole = null;
  this.editingLevel = null;

  // Re-enable fields
  this.CreateForm.get('employeeRole')?.enable();
  this.CreateForm.get('level')?.enable();
}




 
  private markFormGroupTouched(): void {
    Object.keys(this.CreateForm.controls).forEach(key => {
      this.CreateForm.get(key)?.markAsTouched();
    });
  }

  private showSnackBar(message: string, type: 'success' | 'error'): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: type === 'success' ? 'success-snackbar' : 'error-snackbar',
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.CreateForm.get(fieldName);
    if (control?.hasError('required')) {
      return `${fieldName} is required`;
    }
    if (control?.hasError('minlength')) {
      return `${fieldName} must be at least ${control.errors?.['minlength'].requiredLength} characters`;
    }
    if (control?.hasError('pattern')) {
      return `${fieldName} must contain only letters and numbers`;
    }
    if (control?.hasError('min')) {
      return `${fieldName} must be greater than ${control.errors?.['min'].min}`;
    }
    return '';
  }
}