@use '@angular/material' as mat;

table.flex-table {
    display: flex;
    flex-direction: column;
    overflow-x: auto;
    max-width: 100%;
    min-width: auto;
    width: fit-content;
    --mat-table-header-container-height: 50px;
    --mat-table-row-item-container-height: 40px;
    --mat-table-row-item-label-text-size: var(--12px);
    --mat-table-header-headline-size: var(--12px);
    --mat-table-background-color: transparent;
    --mat-table-header-headline-color: #7987a1;
    --mat-table-row-item-label-text-color: #000;
    --mat-table-row-item-outline-color: #E9E9E9;
    --mat-table-header-headline-weight: 500;
    --cell-padding-inline: 16px;

    thead,
    tbody,
    tfoot {
        display: flex;
        flex-direction: column;
    }

    tr {
        min-width: 100%;
        display: grid;
        grid-auto-flow: column;
        // grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        grid-template-columns: repeat(auto-fit, 100px);
        min-width: max-content;
        max-width: max-content;

        td, th {
            border-bottom: none;
        }
    }

    tbody tr:not(.mat-mdc-no-data-row) {
        &:hover, &:has(td.active) {
            background: var(--theme-10);
            border-radius: 0px;
            overflow: clip;
            font-weight: 550;
            width: fit-content;

            td {
                border-top-color: transparent;
            }

            & + tr td {
                border-top-color: transparent;
            }

        }
        td {
            border-top: 1px solid #e9e9e9;
        }
    }

    th {
        border-top-width: var(--mat-table-row-item-outline-width, 1px) !important;
        border-top-color: var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12))) !important;
        text-transform: uppercase;
        // background-color: #fafafa !important;
    }

    td,
    th {
        // flex-grow: 1;
        min-width: fit-content;
        display: flex;
        align-items: center;
        // border: 1px solid #f0f0f0;
        // justify-content: center;
    }

    .mat-mdc-no-data-row {
        min-height: 200px;
        display: flex;
        align-items: start;
        padding-top: 20px;
        justify-content: center;
        font-size: 16px;
    }

  .mdc-data-table__cell, .mdc-data-table__header-cell {
    padding: 0 var(--cell-padding-inline);
  }
}

:root {
    // @include mat.table-density(-3);
    @include mat.table-overrides((header-container-height: 50px,
            row-item-container-height: 45px,
            footer-container-height: 40px,
            header-headline-color: #4b5b79,
            header-headline-size: 12px,
            header-headline-weight: 500,
            // row-item-outline-color: #ffffff,
            // background-color: hsla(0, 0%, 100%, 0)
        ));
}

table.flex-table .mat-sort-header-arrow {
  width: 15px;
  height: 15px;
  margin: 0;
  svg {
    min-width: 15px;
    height: 15px;
    top: auto;
    left: auto;
    margin: 0;
  }
}
thead th { 
    position: sticky; 
    top: 0; 
    z-index: 1; }  
