@if (!showGrid()) {
  <mat-toolbar color="primary" class="navbar">
    <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
      <a routerLink="/" class="crumb">Master</a>
      <mat-icon>chevron_right</mat-icon>
      <span aria-current="page" class="nav-active">{{menuService.activeMenu()?.title}}</span>
    </nav>
  </mat-toolbar>

<div class="component card grid-aln">

  <div class="header">
    <div class="filters-wrapper">
      <mat-form-field class="search hide-subscript" appearance="outline">
        <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
        <input id="search" [formControl]="search" (input)="onSearch()" autocomplete="off" matInput placeholder="{{ 'Search' | translate }}">
      </mat-form-field>
      <div class="filters-more">
        @if(access()?.canAdd) {
        <button (click)="onAdd()" class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix>add</mat-icon>
        </button>
        }
        <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <div class="content">
    <div class="table-wrapper">

      <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">

        <ng-container matColumnDef="regionName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Agency' | translate }}</th>
          <td mat-cell *matCellDef="let row"> {{row.regionName}} </td>
        </ng-container>

        <ng-container matColumnDef="agencyName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Region' | translate }}</th>
          <td mat-cell *matCellDef="let row"> {{row.agencyName}} </td>
        </ng-container>

        <ng-container matColumnDef="millTypeName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'MillType' | translate }}</th>
          <td mat-cell *matCellDef="let row"> {{row.millTypeName}} </td>
        </ng-container>

        <ng-container matColumnDef="hullingCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Hulling' | translate }} {{'Code' | translate }}</th>
          <td mat-cell *matCellDef="let row"> {{row.hullingCode}} </td>
        </ng-container>

        <ng-container matColumnDef="hullingName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'MillName' | translate }}</th>
          <td mat-cell *matCellDef="let row"> {{row.hullingName}} </td>
        </ng-container>

        <ng-container matColumnDef="hullingRegionalName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'MillName' | translate }} {{'InTamil' | translate }}</th>
          <td mat-cell *matCellDef="let row"> {{row.hullingRegionalName}} </td>
        </ng-container>

        <ng-container matColumnDef="address">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'MillAddress' | translate }}</th>
          <td mat-cell *matCellDef="let row"> {{row.address}} </td>
        </ng-container>

        <ng-container matColumnDef="validFrom">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'AgreementValidFrom' | translate }}</th>
          <td mat-cell *matCellDef="let row"> {{row.validFrom | date : 'dd-MM-yyyy'}} </td>
        </ng-container>

        <ng-container matColumnDef="validTo">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'AgreementValidTo' | translate }}</th>
          <td mat-cell *matCellDef="let row"> {{row.validTo | date : 'dd-MM-yyyy'}} </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef class="action"> {{ 'Actions' | translate }}  </th>
          <td mat-cell *matCellDef="let row">
            <div class="table-controls">
              @if (access()?.canView) {
              <button title="{{ 'View' | translate }} " (click)="onView(row)" class="btn-icon btn-icon-unstyled ">
                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
              </button>
              }
              @if(access()?.canEdit) {
              <button title="{{ 'Edit' | translate }} " (click)="onEdit(row)" class="btn-icon btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">edit</mat-icon>
              </button>
              }
              @if(access()?.canDelete) {
              <button title="{{ 'Delete' | translate }} " (click)="onDelete(row)" class="btn-icon btn-delete btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
              </button>
              }
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" [attr.colspan]="displayedColumns.length" style="text-align: center;padding:10px">
            No data found
          </td>
        </tr>
        <!-- <tr *matNoDataRow>
          <ng-container *ngTemplateOutlet="shimmer"></ng-container>
        </tr> -->

      </table>
    </div>

    <!-- @if (dataSource().filteredData.length==0) {
      <div style="text-align: center;">No Data</div>

      } -->

    <mat-paginator  class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
      aria-label="Select page of users"></mat-paginator>

    <div class="mobile-wrapper">
      <div class="un-cards">
        @for (item of dataSource().data; track item) {
        <div class="un-card">
          <div class="desc">
            <div class="quote-no">{{item.hullingName}}&nbsp;-&nbsp;{{item.hullingCode }}</div>
            <div class="quote-no">{{item.hullingRegionalName }}</div>
          </div>
          <div class="actions">
            <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu" aria-label="Card actions">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">

              @if (access()?.canView) {
              <button (click)="onView(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                <span>{{ 'View' | translate }}</span>
              </button>
              }

              @if(access()?.canEdit) {
              <button (click)="onEdit(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">edit</mat-icon>
                <span>{{ 'Edit' | translate }}</span>
              </button>
              }

              @if(access()?.canDelete) {
              <button (click)="onDelete(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
                <span>{{ 'Delete' | translate }}</span>
              </button>
              }
            </mat-menu>
          </div>
        </div>
        }
      </div>
    </div>


  </div>
</div>



}
@else {
<app-hulling [uniqueId]="list().id" [mode]="mode()" (closed)="onClose()"></app-hulling>
}