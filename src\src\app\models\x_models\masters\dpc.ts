export interface dpcs {
    id: number,
    dpcCode: string,
    dpcName: string,
    permanentAddress: string,
    dpcTypeName: string,
    posTypeName: string,
    fromDate: string,
    toDate: string,
    villageName: string,
    agencyName: string,
    storageLocationName: string
}

export interface dpc {
    id: number,
    dpcCode: string,
    edpcDpcCode: string,
    dpcName: string,
    dpcRegionalName: string,
    permanentAddress: string,
    presentAddress: string,
    pincode: string,
    dpcType: number,
    posType: number,
    latitude: number,
    longitude: number,
    fromDate: string,
    toDate: string,
    authBypassFlag: boolean,
    villageId: number,
    agencyId: number,
    storageLocationId: number,
    regionId:number,
    unitId:number,
    talukId:number,
    blockId:number,
}

export type DpcResponse = dpcs[];
