import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCard, MatCardContent, MatCardSubtitle, MatCardTitle } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatMenu, MatMenuTrigger } from '@angular/material/menu';

@Component({
  selector: 'app-transfer-dashboard',
  imports: [CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatCard,
    MatCardSubtitle,
    MatCardTitle,
    MatCardContent,
    MatIcon,
    MatMenuTrigger,
    MatMenu

  ],
  templateUrl: './transfer-dashboard.component.html',
  styleUrl: './transfer-dashboard.component.scss'
})
export class TransferDashboardComponent {

}
