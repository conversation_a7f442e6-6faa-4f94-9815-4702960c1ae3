@use '@angular/material' as mat;

:root {
    @include mat.select-overrides((
        panel-background-color: #fff,
        enabled-trigger-text-color: var(--grey-100),
        disabled-trigger-text-color: var(--grey-100),
        placeholder-text-color: var(--grey-80),
        trigger-text-size: 12px,
        arrow-transform: rotate(90deg),
    ));
}

.mat-mdc-form-field-type-mat-select.raised {
    @include mat.form-field-overrides((
        outlined-outline-width: 0px
    ));

    .mdc-notched-outline {
        box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
        border-radius: 4px;
    }

    .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece {
        // border-color: var(--theme-40);
        border-width: 0;
    }

    .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
        .mat-mdc-select-arrow {
            &::after {
                color: var(--grey-100);
                font-weight: 400;
                font-size: 20px;
                left: -4px;
            }
        }
    }

    &:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover {
        .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
            .mat-mdc-select-arrow {
                &::after {
                    color: var(--grey-100);
                }
            }
        }
    }

    &.mat-focused {
        .mdc-notched-outline {
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
        }
        .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
            .mat-mdc-select-arrow {
                &::after {
                    color: var(--grey-100);
                }
            }
        }
    }
}


.example-additional-selection {
    font-size: 10px;
}

.mat-mdc-select-panel.mat-mdc-select-panel {
    box-shadow: 0px 6px 14px -6px rgba(19, 25, 39, 0.12), 0px 10px 32px -4px rgba(19, 25, 39, 0.10);
    border-radius: 4px;
    padding: 0;

    &::-webkit-scrollbar {
        width: 3px;
    }

    &::-webkit-scrollbar-track {
        // background: black;
        margin-block: 2px 5px;
        border-radius: 10px;
    }
}

.mat-mdc-select-panel-above .mat-mdc-select-panel.mat-mdc-select-panel {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    transform-origin: bottom center;
    translate: 0px -8px;
}
.cdk-overlay-pane:not(.mat-mdc-select-panel-above) .mat-mdc-select-panel.mat-mdc-select-panel {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    transform-origin: top center;
    translate: 0px 8px;
}

body:has(.cdk-overlay-backdrop) {
    .mat-mdc-form-field-type-mat-select {
        &.mat-focused {
            .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
                .mat-mdc-select-arrow {
                    &::after {
                        transform: rotateX(-180deg);
                    }
                }
            }
        }
    }
}

.mat-mdc-form-field-type-mat-select {
    &.mat-focused {
        .mdc-notched-outline {
            // box-shadow: 0 0 0 3px var(--theme-30);
            border-radius: 8px;
        }
        .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
            .mat-mdc-select-arrow {
                &::after {
                    color: var(--grey-40);
                }
            }
        }

        .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid  {
          .mdc-notched-outline {
            box-shadow: 0 0 0 3px var(--red-30);
          }
          .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
            .mat-mdc-select-arrow {
                &::after {
                    color: var(--red-80);
                }
            }
          }
        }
    }

    .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover {
      .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
          .mat-mdc-select-arrow {
              &::after {
                  color: var(--red-80);
              }
          }
      }
    }

    .mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece {
        background-color: transparent;
        border: 1.5px solid var(--grey-10);

        &.mdc-notched-outline__leading {
            border-right: none
        }
        &.mdc-notched-outline__trailing {
            border-left: none
        }
    }

    .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece {
        border-color: var(--grey-40);
        border-width: 1.5px;
    }
}

.mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
    .mat-mdc-select-arrow {
        position: relative;
        height: 100%;

        svg {
            display: none;
        }
        &::after {
            content: "\e313";
            font-family: "Material Symbols Rounded";
            font-size: 28px;
            color: var(--grey-40);
            position: absolute;
            // top: -9px;
            left: -10px;
            font-weight: 200;
            transition: transform 0.5s ease;
            max-height: 100%;
            display: flex;
            align-items: center;
        }
    }
}

.mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow::after {
  color: #d82927;
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover {
    .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper {
        .mat-mdc-select-arrow {
            &::after {
                color: var(--grey-40);
            }
        }
    }
}

.mat-mdc-option.mat-mdc-option {
    &:hover:not(.mdc-list-item--disabled) {
        background-color: var(--theme-30);
    }

    &:not(:has(.mat-pseudo-checkbox-full)) {
        min-height: 40px;
    }

    &:not(:last-child) {
        margin-bottom: 8px;
    }

    &.mdc-list-item--selected:not(.mdc-list-item--disabled){
        &:not(.mat-mdc-option-multiple) {
            background-color: var(--theme-80);

            .mdc-list-item__primary-text {
                color: #fff;
            }
        }
        .mat-icon {
            color: #fff;
        }
    }

    .mat-pseudo-checkbox-minimal {
      display: none;
    }

    &.mat-mdc-option-active.mdc-list-item {
        background-color: var(--theme-30);
    }

    .mat-mdc-option-ripple {
        display: none;
    }

    .mat-pseudo-checkbox {
        order: 3;
    }

    .mdc-list-item__primary-text {
        font-size: 12px;
        display: flex;
        align-items: center;
    }

    .mat-pseudo-checkbox-full {
        width: 15px;
        height: 15px;
        border-radius: 2px;
        border-color: var(--grey-100);
        border-width: 1px;
        margin-right: 0px;

        &.mat-pseudo-checkbox-checked::after {
            width: 8px;
            height: 3px;
            top: -3px;
        }

        &.mat-pseudo-checkbox-checked::after {
            border-left: 1.9px solid currentColor;
        }

        &.mat-pseudo-checkbox::after {
            border-bottom: 1.9px solid currentColor;
        }
    }


    .mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked, .mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate {
        background-color: var(--theme-80);
        border-color: rgba(0, 0, 0, 0);
    }

}

.mat-mdc-option[disabled] .mat-pseudo-checkbox {
  display: none;
}

.no-icon-prefix {
  &.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper {
    padding-left: 12px;
  }

  .mat-mdc-form-field-icon-prefix {
    display: none;
  }
}

.select-search {
  width: 100%;
  max-width: 100%;

  .mdc-notched-outline {
    --mdc-outlined-text-field-outline-color: transparent;
    --mdc-outlined-text-field-hover-outline-color: transparent;
    --mdc-outlined-text-field-focus-outline-color: transparent;
  }
}

.select-search-icon {
  color: var(--grey-60)
}
.mat-mdc-text-field-wrapper:disabled
{
    background: #f3f3f4;
}