<div class="main-content">
    <div class="container-fluid">
        <div class="payslip-container">
            <h2>Payslip for : {{ empObj.employeeName }}</h2>
            <p><strong>Pay Month:</strong> {{ empObj.payMonth | date: 'MMMM yyyy' }}</p>
            <p><strong>PF Number:</strong> {{ empObj.pfNumber }}</p>

            <table class="payslip-table">
                <thead>
                    <tr>
                        <th>Earnings</th>
                        <th>Amount</th>
                        <th>Deductions</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Basic Pay</td>
                        <td>{{ empObj.basicPay | number:'1.2-2' }}</td>
                        <td>EPF</td>
                        <td>{{ empObj.epf | number:'1.2-2' }}</td>
                    </tr>
                    <tr>
                        <td>Dearness Allowance</td>
                        <td>{{ empObj.dearnessAllowance | number:'1.2-2' }}</td>
                        <td>FBF</td>
                        <td>{{ empObj.fbf | number:'1.2-2' }}</td>
                    </tr>
                    <tr>
                        <td>House Rent Allowance</td>
                        <td>{{ empObj.houseRentAllowance | number:'1.2-2' }}</td>
                        <td>HIS</td>
                        <td>{{ empObj.his | number:'1.2-2' }}</td>
                    </tr>
                    <tr>
                        <td>City Compensatory Allowance</td>
                        <td>{{ empObj.cityCompensatoryAllowance | number:'1.2-2' }}</td>
                        <td>SPF</td>
                        <td>{{ empObj.spf | number:'1.2-2' }}</td>
                    </tr>
                    <tr>
                        <td>Cash Allowance</td>
                        <td>{{ empObj.cashAllowance | number:'1.2-2' }}</td>
                        <td>Loan Deduction</td>
                        <td>{{ empObj.loanDeduction || 0 | number:'1.2-2' }}</td>
                    </tr>
                    <tr>
                        <td>Conveyance Allowance</td>
                        <td>{{ empObj.conveyanceAllowance | number:'1.2-2' }}</td>
                        <td>Advance Deduction</td>
                        <td>{{ empObj.advanceDeduction || 0 | number:'1.2-2' }}</td>
                    </tr>
                    <tr>
                        <td>Medical Allowance</td>
                        <td>{{ empObj.medicalAllowance | number:'1.2-2' }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>Wash Allowance</td>
                        <td>{{ empObj.washAllowance | number:'1.2-2' }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>Special Pay</td>
                        <td>{{ empObj.specialPay | number:'1.2-2' }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <th>Total Earnings</th>
                        <th>{{ empObj.totalEarnings | number:'1.2-2' }}</th>
                        <th>Total Deductions</th>
                        <th>{{ empObj.totalDeductions | number:'1.2-2' }}</th>
                    </tr>
                    <tr>
                        <th colspan="3" style="text-align:right;">Net Salary</th>
                        <th>{{ empObj.netSalary | number:'1.2-2' }}</th>
                    </tr>
                </tfoot>
            </table>
            <div class="payslip-actions" style="margin-top: 30px; display: flex; gap: 20px; justify-content: flex-end;">
                <button mat-stroked-button color="primary" type="button" style="font-size: 1.2rem; padding: 12px 32px;">Back</button>
                <button type="button"  (click)="download()" style="font-size: 1.2rem; padding: 12px 32px; background:#203664; color: white; border-radius: 30px 5px 30px 5px;">Download</button>
            </div>
        </div>
    </div>
</div>