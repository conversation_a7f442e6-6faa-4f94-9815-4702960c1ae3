<div class="card">
  <div class="component1">
    <div class="page-header1">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a (click)="navigateBack()">{{menuService.activeMenu()?.title}} {{'Master' |
              translate }}</a>
          </li>
          @if(cropId() == 0) {
          <li aria-current="page" class="breadcrumb-item active">{{'New' | translate}}
            {{menuService.activeMenu()?.title}}</li>
          } @else {
          <!-- <li aria-current="page" class="breadcrumb-item active"> {{crop()?.cropName}}</li> -->
          }
        </ol>
      </nav>

      @if(cropId() == 0) {
      <h1>{{'New' | translate}} {{menuService.activeMenu()?.title}}</h1>
      } @else {
      <h1>
        <!-- {{crop()?.cropName}} -->

        @if(mode() === "view") {
        <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
          <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
        </button>
        }
      </h1>
      }
    </div>



    <form [formGroup]="form" (ngSubmit)="onSubmit()">

      <h1>GST Details</h1>

      <div class="form">


        <div class="field">
          <label for="PANNo" class="required-label">{{'PANNo' | translate }}</label>
          <mat-form-field>
            <input id="panNo" formControlName="panNo" matInput (input)="onInput($event,'/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.PANNo.errors?.['required']) {
              {{'PANNo' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.get('PANNo')?.hasError('invalidPan')) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="GSTNumber" class="required-label">{{'GSTNo' | translate }}</label>
          <mat-form-field>
            <input id="GSTNumber" formControlName="GSTNumber" matInput
              (input)="onInput($event,'/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/')" maxlength="15">
            <mat-error>
              @if(form.controls.GSTNumber.errors?.['required']) {
              {{'GSTNo' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.controls.GSTNumber.errors?.['invalidGst']) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="LegalBusinessName">{{'LegalBusinessName' | translate }}</label>
          <mat-form-field>
            <input id="LegalBusinessName" formControlName="LegalBusinessName" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="uin">{{'UIN ' | translate }}</label>
          <mat-form-field>
            <input id="uin" formControlName="uin" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="DateofRegistration">{{'DateofRegistration' | translate }}</label>
          <mat-form-field>
            <input id="DateofRegistration" formControlName="DateofRegistration" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="StatusofGSTIN">{{'StatusofGSTIN' | translate }}</label>
          <mat-form-field>
            <input id="StatusofGSTIN" formControlName="StatusofGSTIN" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="NatureofBusiness">{{'NatureofBusiness' | translate }}</label>
          <mat-form-field>
            <input id="NatureofBusiness" formControlName="NatureofBusiness" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="LandlineNumberofCompany">{{'LandlineNumberofCompany' | translate }}</label>
          <mat-form-field>
            <input id="LandlineNumberofCompany" formControlName="LandlineNumberofCompany" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="ContactPersonName">{{'ContactPersonName' | translate }}</label>
          <mat-form-field>
            <input id="ContactPersonName" formControlName="ContactPersonName" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Address">{{'Address' | translate }}</label>
          <mat-form-field>
            <input id="Address" formControlName="Address" matInput readonly>
          </mat-form-field>
        </div>
      </div>

      <!-- <h1>Other Details</h1> -->



      <h1>Bank Details</h1>

      <div class="form">
        <div class="field">
          <label for="BankAccountNumber" class="required-label">{{'BankAccountNumber' | translate }}</label>
          <mat-form-field>
            <input id="BankAccountNumber" formControlName="BankAccountNumber" matInput
              (input)="onInput($event,'/^[0-9A-Za-z]*$/')" maxlength="12">
            <mat-error>
              @if(form.controls.BankAccountNumber.errors?.['required']) {
              {{'BankAccountNumber' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

          <div class="field">
            <label for="ConfirmBankAccountNumber" class="required-label">{{'ConfirmBankAccountNumber' | translate }}</label>
            <mat-form-field>
              <input id="ConfirmBankAccountNumber" formControlName="ConfirmBankAccountNumber" matInput
                (input)="onInput($event,'/^[0-9A-Za-z]*$/')" maxlength="12">
              <mat-error>
                @if(form.controls.ConfirmBankAccountNumber.errors?.['required']) {
                {{'ConfirmBankAccountNumber' | translate}} {{'IsRequired' | translate}}
                }
                @else if (form.get('ConfirmBankAccountNumber')?.hasError('noMatch')) {
                  Account numbers do not match.
                  }
              </mat-error>
            </mat-form-field>
          </div>

        <div class="field">
          <label for="IFSCCode" class="required-label">{{'IFSCCode' | translate }}</label>
          <mat-form-field>
            <input id="IFSCCode" formControlName="IFSCCode" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.IFSCCode.errors?.['required']) {
              {{'IFSCCode' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="BankName" class="required-label">{{'BankName' | translate }}</label>
          <mat-form-field>
            <input id="BankName" formControlName="BankName" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.BankName.errors?.['required']) {
              {{'BankName' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Branch" class="required-label">{{'Branch' | translate }}</label>
          <mat-form-field>
            <input id="Branch" formControlName="Branch" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.Branch.errors?.['required']) {
              {{'Branch' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


       
      </div>

      <h1>Contract Details</h1>

      <div class="form">
        <div class="field">
          <label for="ContractID/WorkOrderID" class="required-label">{{'ContractID/WorkOrderID' | translate }}</label>
          <mat-form-field>
            <input id="ContractID/WorkOrderID" formControlName="ContractID" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.ContractID.errors?.['required']) {
              {{'ContractID/WorkOrderID' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="ContractStartDate" class="required-label">{{'ContractStartDate' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker5" formControlName="AgreementUpto">
            <mat-datepicker-toggle matIconSuffix [for]="picker5"></mat-datepicker-toggle>
            <mat-datepicker #picker5></mat-datepicker>
            <mat-error>
              @if(form.controls.ContractStartDate.errors?.['required']) {
              {{'ContractStartDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="ContractEndDate" class="required-label">{{'ContractEndDate' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker6" formControlName="AgreementUpto">
            <mat-datepicker-toggle matIconSuffix [for]="picker6"></mat-datepicker-toggle>
            <mat-datepicker #picker6></mat-datepicker>
            <mat-error>
              @if(form.controls.ContractEndDate.errors?.['required']) {
              {{'ContractEndDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="ContractDoc" class="required-label">{{'Contract/WorkOrderDocument' | translate }}</label>
          <mat-form-field>
            <input id="ContractDoc" formControlName="ContractDoc" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.ContractDoc.errors?.['required']) {
              {{'ContractDoc' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Commodity" class="required-label">{{'Commodity' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="Commodity" formControlName="Commodity">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (role of filteredTaluks(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.Commodity.errors?.['required']) {
              {{'Commodity' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

      </div>

      <h1>Mill Details</h1>


      <div class="form">
        <div class="field">
          <label for="FSSAI" class="required-label">{{'FSSAI' | translate }}</label>
          <mat-form-field>
            <input id="FSSAI" formControlName="FSSAI" matInput (input)="onInput($event,'/^[0-9]*$/')" maxlength="14">
            <mat-error>
              @if(form.controls.FSSAI.errors?.['required']) {
              {{'FSSAI' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="MSME" class="required-label">{{'MSME' | translate }}</label>
          <mat-form-field>
            <input id="MSME" formControlName="MSME" matInput (input)="onInput($event,'/^[0-9A-Za-z]*$/')"
              maxlength="16">
            <mat-error>
              @if(form.controls.MSME.errors?.['required']) {
              {{'MSME' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="AgmarkLicense" class="required-label">{{'AgmarkLicense' | translate }}</label>
          <mat-form-field>
            <input id="AgmarkLicense" formControlName="AgmarkLicense" matInput (input)="onInput($event,'/^[0-9]*$/')"
              maxlength="15">
            <mat-error>
              @if(form.controls.AgmarkLicense.errors?.['required']) {
              {{'AgmarkLicense' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="ContactEmail " class="required-label">{{'ContactEmail' | translate }}</label>
          <mat-form-field>
            <input id="ContactEmail" formControlName="ContactEmail" matInput maxlength="50">
            <mat-error>
              @if(form.controls.ContactEmail.errors?.['required']) {
              {{'ContactEmail' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.controls.ContactEmail.errors?.['invalidEmail']) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="OwnerName " class="required-label">{{'OwnerName ' | translate }}</label>
          <mat-form-field>
            <input id="OwnerName" formControlName="OwnerName" matInput (input)="onInput($event,'/^[a-zA-Z\s]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.OwnerName.errors?.['required']) {
              {{'OwnerName' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="MobileNo" class="required-label">{{'MobileNo' | translate }}</label>
          <mat-form-field>
            <input id="MobileNo" formControlName="MobileNo" matInput (input)="onInput($event,'/^[0-9]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.MobileNo.errors?.['required']) {
              {{'MobileNo' | translate}} {{'IsRequired' | translate}}
              } @else if (form.controls.MobileNo.errors?.['pattern']) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Region" class="required-label">{{'Region' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="Region" formControlName="Region" (selectionChange)="getTaluk()">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (item of regions(); track item) {
              <mat-option [value]="item.key">{{item.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.Region.errors?.['required']) {
              {{'Region' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Unit" class="required-label">{{'Unit' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="Unit" formControlName="Unit" (selectionChange)="getTaluk()">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (item of units(); track item) {
              <mat-option [value]="item.key">{{item.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.Unit.errors?.['required']) {
              {{'Unit' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        @if(form.value.Region && form.value.Unit){
        <div class="field">
          <label for="Taluk" class="required-label">{{'Taluk' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="Taluk" formControlName="Taluk" (selectionChange)="getBlock()">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (item of taluks(); track item) {
              <mat-option [value]="item.key">{{item.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.Taluk.errors?.['required']) {
              {{'Taluk' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>
        }


        @if(form.value.Taluk){
        <div class="field">
          <label for="Block" class="required-label">{{'Block ' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="Block" formControlName="Block" (selectionChange)="getVillage()">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (item of blocks(); track item) {
              <mat-option [value]="item.key">{{item.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.Block.errors?.['required']) {
              {{'Block' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>
        }

        @if(form.value.Block){
        <div class="field">
          <label for="Village" class="required-label">{{'Village' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="Village" formControlName="Village">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (item of villages(); track item) {
              <mat-option [value]="item.key">{{item.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.Village.errors?.['required']) {
              {{'Village' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>
        }

        <div class="field">
          <label for="Pincode" class="required-label">{{'Pincode' | translate }}</label>
          <mat-form-field>
            <input id="Pincode" formControlName="Pincode" matInput (input)="onInput($event,'/^[0-9]*$/')" maxlength="6"
            minlength="6">
            <mat-error>
              @if(form.controls.Pincode.errors?.['required']) {
              {{'Pincode' | translate}} {{'IsRequired' | translate}}
              } @else if (form.controls.Pincode.errors?.['minlength']) {
              {{'InvalidFormat' | translate}}
              }
              @else if (form.controls.Pincode.errors?.['maxlength']) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>



        <!-- <div class="form"> -->
        <div class="field">
          <label for="MillName" class="required-label">{{'MillName' | translate }}</label>
          <mat-form-field>
            <input id="MillName" formControlName="MillName" matInput (input)="onInput($event,'/^[.,/()a-zA-Z -]*$/')"
              maxlength="50">
            <mat-error>
              @if(form.controls.MillName.errors?.['required']) {
              {{'MillName' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="MillOwnerName" class="required-label">{{'MillOwnerName' | translate }}</label>
          <mat-form-field>
            <input id="MillOwnerName" formControlName="MillOwnerName" matInput
              (input)="onInput($event,'/^[a-zA-Z\s]*$/')" maxlength="100">
            <mat-error>
              @if(form.controls.MillOwnerName.errors?.['required']) {
              {{'MillOwnerName' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

       
          <div class="field">
            <label for="MillType" class="required-label">{{'MillType' | translate }}</label>
            <mat-form-field appearance="outline">
              <mat-select id="MillType" formControlName="MillType">
                <mat-form-field class="select-search hide-subscript" appearance="outline">
                  <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                  <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
                </mat-form-field>
                @for (item of milltype(); track item) {
                <mat-option [value]="item.key">{{item.value}}</mat-option>
                }
              </mat-select>
              <mat-error>
                @if(form.controls.MillType.errors?.['required']) {
                {{'MillType' | translate}} {{'IsRequired' | translate}}
                }
              </mat-error>
            </mat-form-field>
          </div>


        <div class="field">
          <label for="MillCode" class="required-label">{{'MillCode' | translate }}</label>
          <mat-form-field>
            <input id="MillCode" formControlName="MillCode" matInput (input)="onInput($event,'/^[0-9A-Za-z]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.MillCode.errors?.['required']) {
              {{'MillCode' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="Latitude" class="required-label">{{'Latitude' | translate }}</label>
          <mat-form-field>
            <input id="Latitude" formControlName="Latitude" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="50">
            <mat-error>
              @if(form.controls.Latitude.errors?.['required']) {
              {{'Latitude' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Longitude" class="required-label">{{'Longitude' | translate }}</label>
          <mat-form-field>
            <input id="Longitude" formControlName="Longitude" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="50">
            <mat-error>
              @if(form.controls.Longitude.errors?.['required']) {
              {{'Longitude' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="AdhaarNo" class="required-label">{{'AdhaarNo' | translate }}</label>
          <mat-form-field>
            <input id="AdhaarNo" formControlName="AdhaarNo" matInput (input)="onInput($event,'/^[0-9]*$/')"
              maxlength="12">
            <mat-error>
              @if(form.controls.AdhaarNo.errors?.['required']) {
              {{'AdhaarNo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="FactoryLicenseNo" class="required-label">{{'FactoryLicenseNo' | translate }}</label>
          <mat-form-field>
            <input id="FactoryLicenseNo" formControlName="FactoryLicenseNo" matInput
              (input)="onInput($event,'/^[0-9A-Za-z]*$/')" maxlength="20">
            <mat-error>
              @if(form.controls.FactoryLicenseNo.errors?.['required']) {
              {{'FactoryLicenseNo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="FactoryLicenseUpto" class="required-label">{{'FactoryLicenseUpto' | translate }}</label>
          <mat-form-field>
            <input id="FactoryLicenseUpto" formControlName="FactoryLicenseUpto" matInput
              (input)="onInput($event,'/^[0-9A-Za-z]*$/')" maxlength="20">
            <mat-error>
              @if(form.controls.FactoryLicenseUpto.errors?.['required']) {
              {{'FactoryLicenseUpto' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="HullingMonthlyCapacityinMTs" class="required-label">{{'HullingMonthlyCapacityinMTs' | translate
            }}</label>
          <mat-form-field>
            <input id="HullingMonthlyCapacityinMTs" formControlName="HullingMonthlyCapacityinMTs" matInput
              (input)="onInput($event,'/^[0-9]*$/')" maxlength="6">
            <mat-error>
              @if(form.controls.HullingMonthlyCapacityinMTs.errors?.['required']) {
              {{'HullingMonthlyCapacityinMTs' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="IsGeneratorAvailable" class="required-label">{{'IsGeneratorAvailable?' | translate }}</label>
          <mat-radio-group aria-labelledby="example-radio-group-label" class="example-radio-group"
            formControlName="IsGeneratorAvailable">
            <mat-radio-button class="example-radio-button" [value]="1" checked>Yes</mat-radio-button>
            <mat-radio-button class="example-radio-button" [value]="2">No</mat-radio-button>
          </mat-radio-group>
        </div>

        <!-- <div class="field">
          <label for="IsGeneratorAvailable" class="required-label">{{'IsGeneratorAvailable?' | translate }}</label>
          <mat-form-field>
            <input id="IsGeneratorAvailable" formControlName="IsGeneratorAvailable" matInput
              (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="14">
            <mat-error>
              @if(form.controls.IsGeneratorAvailable.errors?.['required']) {
              {{'IsGeneratorAvailable' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div> -->

        <div class="field">
          <label for="IsSeparateEBAvailable" class="required-label">{{'IsSeparateEBAvailable' | translate }} ?</label>
          <mat-radio-group aria-labelledby="example-radio-group-label" class="example-radio-group"
            formControlName="IsSeparateEBAvailable">
            <mat-radio-button class="example-radio-button" [value]="1" checked>Yes</mat-radio-button>
            <mat-radio-button class="example-radio-button" [value]="2">No</mat-radio-button>
          </mat-radio-group>
        </div>

        <!-- <div class="field">
          <label for="IsSeparateEBAvailable" class="required-label">{{'IsSeparateEBAvailable?' | translate }}</label>
          <mat-form-field>
            <input id="IsSeparateEBAvailable" formControlName="IsSeparateEBAvailable" matInput
              (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="14">
            <mat-error>
              @if(form.controls.IsSeparateEBAvailable.errors?.['required']) {
              {{'IsSeparateEBAvailable' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div> -->

        <div class="field">
          <label for="EBConsumptionNoBoiler" class="required-label">{{'EBConsumptionNoBoiler' | translate }}</label>
          <mat-form-field>
            <input id="EBConsumptionNoBoiler" formControlName="EBConsumptionNoBoiler" matInput
              (input)="onInput($event,'/^[0-9A-Za-z]*$/')" maxlength="5">
            <mat-error>
              @if(form.controls.EBConsumptionNoBoiler.errors?.['required']) {
              {{'EBConsumptionNoBoiler' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="TypeofEBServiceOffice" class="required-label">{{'TypeofEBServiceOffice' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="TypeofEBServiceOffice" formControlName="TypeofEBServiceOffice">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (item of milltype(); track item) {
              <mat-option [value]="item.key">{{item.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.TypeofEBServiceOffice.errors?.['required']) {
              {{'TypeofEBServiceOffice' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <!-- <div class="field">
          <label for="TypeofEBServiceOffice" class="required-label">{{'TypeofEBServiceOffice' | translate }}</label>
          <mat-form-field>
            <input id="TypeofEBServiceOffice" formControlName="TypeofEBServiceOffice" matInput
              (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="14">
            <mat-error>
              @if(form.controls.TypeofEBServiceOffice.errors?.['required']) {
              {{'TypeofEBServiceOffice' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div> -->

        <div class="field">
          <label for="BoilerLicenseNo" class="required-label">{{'BoilerLicenseNo' | translate }}</label>
          <mat-form-field>
            <input id="BoilerLicenseNo" formControlName="BoilerLicenseNo" matInput
              (input)="onInput($event,'/^[0-9A-Za-z]*$/')" maxlength="20">
            <mat-error>
              @if(form.controls.BoilerLicenseNo.errors?.['required']) {
              {{'BoilerLicenseNo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="MRMType" class="required-label">{{'MRMType' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="MRMType" formControlName="MRMType">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (item of mrmType(); track item) {
              <mat-option [value]="item.key">{{item.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.MRMType.errors?.['required']) {
              {{'MRMType' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="AgreementUpto" class="required-label">{{'AgreementUpto' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker1" formControlName="AgreementUpto">
            <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
            <mat-error>
              @if(form.controls.AgreementUpto.errors?.['required']) {
              {{'AgreementUpto' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="SecurityDepositValue" class="required-label">{{'SecurityDepositValue' | translate }}</label>
          <mat-form-field>
            <input id="SecurityDepositValue" formControlName="SecurityDepositValue" matInput
              (input)="onInput($event,'/^[0-9]*$/')" maxlength="5">
            <mat-error>
              @if(form.controls.SecurityDepositValue.errors?.['required']) {
              {{'SecurityDepositValue' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="BankGuaranteeValue" class="required-label">{{'BankGuaranteeValue' | translate }}</label>
          <mat-form-field>
            <input id="BankGuaranteeValue" formControlName="BankGuaranteeValue" matInput
              (input)="onInput($event,'/^[0-9]*$/')" maxlength="5">
            <mat-error>
              @if(form.controls.BankGuaranteeValue.errors?.['required']) {
              {{'BankGuaranteeValue' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="BGNumber" class="required-label">{{'BGNumber' | translate }}</label>
          <mat-form-field>
            <input id="BGNumber" formControlName="BGNumber" matInput (input)="onInput($event,'/^[0-9A-Za-z]*$/')"
              maxlength="5">
            <mat-error>
              @if(form.controls.BGNumber.errors?.['required']) {
              {{'BGNumber' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="BGValidUpTo" class="required-label">{{'BGValidUpTo' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker2" formControlName="AgreementUpto">
            <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
            <mat-datepicker #picker2></mat-datepicker>
            <mat-error>
              @if(form.controls.BGValidUpTo.errors?.['required']) {
              {{'BGValidUpTo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <!-- <div class="field">
          <label for="BGValidUpTo" class="required-label">{{'BGValidUpTo' | translate }}</label>
          <mat-form-field>
            <input id="BGValidUpTo" formControlName="BGValidUpTo" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.BGValidUpTo.errors?.['required']) {
              {{'BGValidUpTo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div> -->

        <div class="field">
          <label for="GenModel" class="required-label">{{'GenModel' | translate }}</label>
          <mat-form-field>
            <input id="GenModel" formControlName="GenModel" matInput (input)="onInput($event,'/^[0-9A-Za-z]*$/')"
              maxlength="20">
            <mat-error>
              @if(form.controls.GenModel.errors?.['required']) {
              {{'GenModel' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="EBConsumptionNoMill" class="required-label">{{'EBConsumptionNoMill' | translate }}</label>
          <mat-form-field>
            <input id="EBConsumptionNoMill" formControlName="EBConsumptionNoMill" matInput
              (input)="onInput($event,'/^[0-9A-Za-z]*$/')" maxlength="20">
            <mat-error>
              @if(form.controls.EBConsumptionNoMill.errors?.['required']) {
              {{'EBConsumptionNoMill' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="TypeofEBServiceBoiler" class="required-label">{{'TypeofEBServiceBoiler' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="TypeofEBServiceBoiler" formControlName="MRMType">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (item of mrmType(); track item) {
              <mat-option [value]="item.key">{{item.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.TypeofEBServiceBoiler.errors?.['required']) {
              {{'TypeofEBServiceBoiler' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <!-- <div class="field">
          <label for="TypeofEBServiceBoiler" class="required-label">{{'TypeofEBServiceBoiler' | translate }}</label>
          <mat-form-field>
            <input id="ContractDoc" formControlName="ContractDoc" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.ContractDoc.errors?.['required']) {
              {{'ContractDoc' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div> -->


        <div class="field">
          <label for="BoilerLicenseUpto" class="required-label">{{'BoilerLicenseUpto' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker3" formControlName="AgreementUpto">
            <mat-datepicker-toggle matIconSuffix [for]="picker3"></mat-datepicker-toggle>
            <mat-datepicker #picker3></mat-datepicker>
            <mat-error>
              @if(form.controls.BoilerLicenseUpto.errors?.['required']) {
              {{'BoilerLicenseUpto' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="DateOfAppointment" class="required-label">{{'DateOfAppointment' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker4" formControlName="AgreementUpto">
            <mat-datepicker-toggle matIconSuffix [for]="picker4"></mat-datepicker-toggle>
            <mat-datepicker #picker4></mat-datepicker>
            <mat-error>
              @if(form.controls.DateOfAppointment.errors?.['required']) {
              {{'DateOfAppointment' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="ContractDoc" class="required-label">{{'Capacity(Kwh.)' | translate }}</label>
          <mat-form-field>
            <input id="ContractDoc" formControlName="ContractDoc" matInput (input)="onInput($event,'/^[0-9]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.ContractDoc.errors?.['required']) {
              {{'ContractDoc' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="TypeofEBServiceMill" class="required-label">{{'TypeofEBServiceMill' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="TypeofEBServiceMill" formControlName="MRMType">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (item of mrmType(); track item) {
              <mat-option [value]="item.key">{{item.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.TypeofEBServiceMill.errors?.['required']) {
              {{'TypeofEBServiceMill' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <!-- <div class="field">
          <label for="ContractDoc" class="required-label">{{'TypeofEBServiceMill' | translate }}</label>
          <mat-form-field>
            <input id="ContractDoc" formControlName="ContractDoc" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.ContractDoc.errors?.['required']) {
              {{'ContractDoc' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div> -->

        <div class="field">
          <label for="ContractDoc" class="required-label">{{'IsColourSortexAvailable' | translate }}</label>
          <mat-form-field>
            <input id="ContractDoc" formControlName="ContractDoc" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.ContractDoc.errors?.['required']) {
              {{'ContractDoc' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

   
      </div>

      <!-- </div> -->

      <div class="actions">
        <button type="button" mat-flat-button (click)="navigateBack()" class="btn btn-secondary">{{'Reset' |
          translate}}</button>
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
          translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{cropId() == 0 ?
          ('Create' | translate) : ('Create' | translate)}}</button>
        }
      </div>
    </form>
  </div>
</div>