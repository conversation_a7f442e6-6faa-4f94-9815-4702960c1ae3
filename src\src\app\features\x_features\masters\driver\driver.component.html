<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{driver()?.driverName}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

  <div class="card component">
   
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="driverName" class="required-label">{{'DriverName' | translate }}</label>
          <mat-form-field>
            <input id="driverName" formControlName="driverName" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.driverName.errors?.['required']) {
                {{'DriverName' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="driverMobileNo" class="required-label">{{'DriverMobileNo' | translate }}</label>
          <mat-form-field>
            <input id="driverMobileNo" formControlName="driverMobileNo" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.driverMobileNo.errors?.['required']) {
                {{'DriverMobileNo' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.controls.driverMobileNo.errors?.['pattern']) {
                {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="dob" class="required-label">{{'DateOfBirth' | translate }}</label>
          <mat-form-field>
            <input matInput id="dob" formControlName="dob" [matDatepicker]="picker1">
            <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
            <mat-error>
              @if(form.controls.dob.errors?.['required']) {
                {{'DateOfBirth' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="licenseNo" class="required-label">{{'LicenseNo' | translate }}</label>
          <mat-form-field>
            <input id="licenseNo" formControlName="licenseNo" matInput>
            <mat-error>
              @if(form.controls.licenseNo.errors?.['required']) {
                {{'LicenseNo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

      </div>

      <div class="actions">
        
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' | translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0 ?
          ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>
    </form>
  </div>
