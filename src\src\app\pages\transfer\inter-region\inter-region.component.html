<h2>Transfer to Another Region</h2>
<form [formGroup]="transferForm" (ngSubmit)="onSubmit()">
  <mat-form-field appearance="outline">
    <mat-label>Current Region</mat-label>
    <input matInput formControlName="currentRegion" readonly>
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Proposed Region</mat-label>
    <mat-select formControlName="proposedRegion">
      <mat-option *ngFor="let r of regions" [value]="r.id">{{ r.name }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Proposed Position</mat-label>
    <input matInput formControlName="proposedPosition">
  </mat-form-field>

  <mat-slide-toggle formControlName="relocationRequired">Request Relocation Support</mat-slide-toggle>

  <button mat-raised-button color="accent" type="submit">Submit</button>
</form>
