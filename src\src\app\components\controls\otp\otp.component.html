<div [formGroup]="form" class="code">
  <mat-form-field class="hide-subscript">
    <input #one appOnlyNumbers id="digit-one" maxlength="1" autocomplete="off" formControlName="one" matInput (input)="onChange($event, one, two)" (keydown.backspace)="onBackSpace(null, one)">
</mat-form-field>
<mat-form-field class="hide-subscript">
  <input #two appOnlyNumbers id="digit-two" maxlength="1" autocomplete="off" formControlName="two" matInput (input)="onChange($event, two, three)" (keydown.backspace)="onBackSpace(one, two)">
</mat-form-field>
<mat-form-field class="hide-subscript">
  <input #three appOnlyNumbers id="digit-three" maxlength="1" autocomplete="off" formControlName="three" matInput (input)="onChange($event, three, four)" (keydown.backspace)="onBackSpace(two, three)">
</mat-form-field>
<mat-form-field class="hide-subscript">
  <input #four appOnlyNumbers id="digit-four" maxlength="1" autocomplete="off" formControlName="four" matInput (input)="onChange($event, four, five)" (keydown.backspace)="onBackSpace(three, four)">
</mat-form-field>
<mat-form-field class="hide-subscript">
  <input #five appOnlyNumbers id="digit-five" maxlength="1" autocomplete="off" formControlName="five" matInput (input)="onChange($event, five, six)" (keydown.backspace)="onBackSpace(four, five)">
</mat-form-field>
<mat-form-field class="hide-subscript">
  <input #six appOnlyNumbers id="digit-six" maxlength="1" autocomplete="off" formControlName="six" matInput (input)="onChange($event, six, null)" (keydown.backspace)="onBackSpace(five, six)">
</mat-form-field>
</div>
