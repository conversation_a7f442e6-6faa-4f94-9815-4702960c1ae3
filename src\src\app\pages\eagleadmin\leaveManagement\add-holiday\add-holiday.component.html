<!-- <div class="add-holiday-container">
  <div class="header">
    <h1 class="page-title">
      <mat-icon>event_add</mat-icon>
      Add Holiday
    </h1>
  </div>
    <mat-card class="form-card">
      
      <mat-card-content>
        <form [formGroup]="holidayForm" (ngSubmit)="addHoliday()">
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Holiday Name</mat-label>
              <input matInput formControlName="name" required aria-required="true">
              <mat-error *ngIf="holidayForm.get('name')?.hasError('required')">Name is required</mat-error>
            </mat-form-field>
          </div>
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="datePicker" formControlName="date" required aria-required="true">
              <mat-datepicker-toggle matSuffix [for]="datePicker"></mat-datepicker-toggle>
              <mat-datepicker #datePicker [startView]="'multi-year'"></mat-datepicker>
              <mat-error *ngIf="holidayForm.get('date')?.hasError('required')">Date is required</mat-error>
            </mat-form-field>
          </div>
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Day</mat-label>
              <input matInput formControlName="day" readonly aria-readonly="true">
            </mat-form-field>
          </div>
          <div class="form-section submit-section">
            <button mat-button type="button" (click)="cancel()">Cancel</button>
            <button mat-raised-button color="primary" type="submit" [disabled]="holidayForm.invalid">
              <mat-icon>save</mat-icon>
              Add Holiday
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div> -->

  <div class="add-holiday-container">
    <mat-card class="header-card">
      <mat-card-header class="form-header">
        <div class="header-content">
          <div class="logo-section">
            <mat-icon class="tncsc-logo">event_add</mat-icon>
          </div>
          <div class="title-section">
            <h2 class="form-subtitle">Add Holiday</h2>
            <p class="subtitle">Add a new holiday</p>
          </div>
        </div>
      </mat-card-header>
    </mat-card>
    <mat-card class="form-card">
      <mat-card-content>
        <form [formGroup]="holidayForm" (ngSubmit)="addHoliday()">
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Holiday Name</mat-label>
              <input matInput formControlName="name" required aria-required="true">
              <mat-error *ngIf="holidayForm.get('name')?.hasError('required')">Name is required</mat-error>
            </mat-form-field>
          </div>
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="datePicker" formControlName="date" required aria-required="true">
              <mat-datepicker-toggle matSuffix [for]="datePicker"></mat-datepicker-toggle>
              <mat-datepicker #datePicker [startView]="'multi-year'"></mat-datepicker>
              <mat-error *ngIf="holidayForm.get('date')?.hasError('required')">Date is required</mat-error>
            </mat-form-field>
          </div>
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Day</mat-label>
              <input matInput formControlName="day" readonly aria-readonly="true">
            </mat-form-field>
          </div>
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description"></textarea>
            </mat-form-field>
          </div>
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Holiday Type</mat-label>
              <mat-select formControlName="holidayType" required>
                <mat-option *ngFor="let type of holidayTypes" [value]="type">{{ type }}</mat-option>
              </mat-select>
              <mat-error *ngIf="holidayForm.get('holidayType')?.hasError('required')">Holiday Type is required</mat-error>
            </mat-form-field>
          </div>
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Region</mat-label>
              <input matInput formControlName="region">
            </mat-form-field>
          </div>
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>District</mat-label>
              <input matInput formControlName="district">
            </mat-form-field>
          </div>
          <div class="form-section">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Year</mat-label>
              <input matInput type="number" formControlName="year" required>
              <mat-error *ngIf="holidayForm.get('year')?.hasError('required')">Year is required</mat-error>
            </mat-form-field>
          </div>
      
          <div class="form-section submit-section">
            <button mat-button type="button" (click)="cancel()">Cancel</button>
            <button mat-raised-button color="primary" type="submit" [disabled]="holidayForm.invalid">
              <mat-icon>save</mat-icon>
              Add Holiday
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>