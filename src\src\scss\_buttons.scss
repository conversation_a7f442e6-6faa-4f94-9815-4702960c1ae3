a {
  color: var(--blue-80);
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;
  text-decoration: none;
}

:root {
  --btn-icon-radius: 50%;
}

.btn-icon-unstyled {
  border: none;
  padding: 0;
  background: transparent;
  width: min-content;
  height: min-content;
  line-height: 0;
  cursor: pointer;
}

.mdc-button.mdc-button {
    height: var(--button-height);
    line-height: 1em;
    min-width: 121px;
    @media (max-width: 600px) {
    min-width: 70px !important;
    }
    padding-inline: 11px;

}

.btn.mat-mdc-icon-button {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mat-mdc-icon-button .mat-ripple-element {
    background-color: var(--theme-80) !important;
    opacity: 0.12;
}

// icon button radius
.btn.mat-mdc-icon-button, .btn.mat-mdc-icon-button .mat-mdc-button-persistent-ripple {
  border-radius: var(--btn-icon-radius);
}
.btn {
    --button-height: var(--35px);

    --mdc-text-button-label-text-size: var(--14px);
    --mdc-text-button-label-text-weight: 500;
    --mdc-text-button-disabled-label-text-color: #9EA2AE;
    --mdc-text-button-container-height: var(--35px);

    --mdc-filled-button-label-text-size: var(--14px);
    --mdc-filled-button-label-text-weight: 500;
    --mdc-filled-button-disabled-label-text-color: #9EA2AE;
    --mdc-filled-button-container-height: var(--35px);
    --mdc-filled-button-container-shape: 4px;

    --mdc-outlined-button-label-text-size: var(--14px);
    --mdc-outlined-button-label-text-weight: 500;
    --mdc-outlined-button-disabled-label-text-color: #9EA2AE;
    --mdc-outlined-button-container-height: var(--35px);
    --mdc-outlined-button-container-shape: 4px;

    --mat-protected-button-state-layer-color: var(--theme-80);
    --mdc-protected-button-label-text-size: var(--12px);
    --mdc-protected-button-container-shape: var(--8px);
    --mdc-protected-button-container-height: var(--32px);
    --mdc-protected-button-container-elevation-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
    --mdc-protected-button-disabled-container-elevation-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
    --mdc-protected-button-focus-container-elevation-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
    --mdc-protected-button-hover-container-elevation-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
    --mdc-protected-button-pressed-container-elevation-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);

    --mdc-fab-container-elevation-shadow: none;
    --mdc-fab-focus-container-elevation-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
    --mdc-fab-hover-container-elevation-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
    --mdc-fab-pressed-container-elevation-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
    --mdc-fab-container-color: var(--sys-primary-container);
    --mdc-fab-container-shape: 99999px;

    --mat-icon-button-press-hover-focus-opacity: 1;
    --mat-icon-button-state-layer-color: var(--theme-30);
    --mat-icon-button-pressed-state-layer-opacity: var(--mat-icon-button-press-hover-focus-opacity);
    --mat-icon-button-hover-state-layer-opacity: var(--mat-icon-button-press-hover-focus-opacity);
    --mat-icon-button-focus-state-layer-opacity: var(--mat-icon-button-press-hover-focus-opacity);

    &.mat-mdc-fab-base {
        width: 59px;
        height: 59px;
    }

    &.mat-mdc-icon-button {
        display: flex;
    }
}

.btn-theme {
    --mdc-filled-button-container-color: var(--theme-90);
    --mdc-filled-button-label-text-color: #fff;

    --mdc-outlined-button-outline-color: var(--theme-90);
    --mdc-outlined-button-label-text-color: var(--grey-100);
    --mat-outlined-button-state-layer-color: var(--theme-90);
    --mat-outlined-button-ripple-color: var(--theme-90);

    --mdc-text-button-label-text-color: var(--theme-90);

    --mdc-protected-button-container-color: var(--theme-90);
    --mat-protected-button-ripple-color: var(--theme-70);
    --mdc-protected-button-label-text-color: #fff;
    --mdc-protected-button-label-text-weight: 600;

    --mat-protected-button-hover-state-layer-opacity: 0.08;
    --mat-protected-button-focus-state-layer-opacity: 0.12;
    --mat-protected-button-pressed-state-layer-opacity: 0.12;

    --mdc-fab-container-color: var(--theme-90);
    --mat-fab-foreground-color: #fff;
}

.btn-secondary {
  --mdc-filled-button-container-color: var(--orange-80);
  --mdc-filled-button-label-text-color: #fff;

  --mdc-outlined-button-outline-color: var(--orange-80);
  --mdc-outlined-button-label-text-color: var(--grey-100);
  --mat-outlined-button-state-layer-color: var(--orange-80);
  --mat-outlined-button-ripple-color: var(--orange-80);

  --mdc-text-button-label-text-color: var(--orange-80);

  --mdc-protected-button-container-color: var(--orange-80);
  --mat-protected-button-ripple-color: var(--orange-70);
  --mdc-protected-button-label-text-color: #fff;
  --mdc-protected-button-label-text-weight: 600;

  --mat-protected-button-hover-state-layer-opacity: 0.08;
  --mat-protected-button-focus-state-layer-opacity: 0.12;
  --mat-protected-button-pressed-state-layer-opacity: 0.12;

  --mdc-fab-container-color: var(--theme-80);
  --mat-fab-foreground-color: #fff;
}

.btn-danger {
  --mdc-filled-button-container-color: var(--red-80);
  --mdc-filled-button-label-text-color: #fff;

  --mdc-outlined-button-outline-color: var(--red-80);
  --mdc-outlined-button-label-text-color: var(--grey-100);
  --mat-outlined-button-state-layer-color: var(--red-80);
  --mat-outlined-button-ripple-color: var(--red-80);

  --mdc-text-button-label-text-color: var(--red-80);

  --mdc-protected-button-container-color: var(--red-80);
  --mat-protected-button-ripple-color: var(--orange-70);
  --mdc-protected-button-label-text-color: #fff;
  --mdc-protected-button-label-text-weight: 600;

  --mat-protected-button-hover-state-layer-opacity: 0.08;
  --mat-protected-button-focus-state-layer-opacity: 0.12;
  --mat-protected-button-pressed-state-layer-opacity: 0.12;

  --mdc-fab-container-color: var(--theme-80);
  --mat-fab-foreground-color: #fff;
}

.btn-white {
    --mdc-filled-button-container-color: #fff;
    --mdc-filled-button-label-text-color: var(--grey-100);
    --mat-filled-button-state-layer-color: var(--theme-80);

    --mdc-outlined-button-outline-color: #fff;
    --mdc-outlined-button-container-color: transparent;
    --mdc-outlined-button-label-text-color: var(--grey-100);
    --mat-outlined-button-state-layer-color: var(--theme-80);
    --mat-outlined-button-ripple-color: var(--theme-30);

    --mdc-text-button-label-text-color: var(--grey-100);
    --mdc-outlined-button-outline-width: 1.5px;

    --mat-protected-button-state-layer-color: var(--theme-80);
    --mdc-protected-button-container-color: #fff;
    --mat-protected-button-ripple-color: var(--theme-30);
    --mdc-protected-button-label-text-color: var(--grey-100);
    --mdc-protected-button-label-text-weight: 400;

    --mdc-fab-container-color: #fff;
    --mat-fab-foreground-color: var(--grey-100);
}

.btn-black {
    --mdc-filled-button-container-color: var(--grey-100);
    --mdc-filled-button-label-text-color: #fff;

    --mdc-outlined-button-outline-color: var(--grey-100);
    --mdc-outlined-button-container-color: transparent;
    --mdc-outlined-button-label-text-color: var(--grey-100);

    --mdc-text-button-label-text-color: var(--grey-100);

    --mat-protected-button-state-layer-color: var(--grey-100);
    --mdc-protected-button-container-color: var(--grey-100);
    --mdc-protected-button-label-text-color: #fff;
    --mdc-protected-button-label-text-weight: 700;

    --mdc-fab-container-color: var(--grey-100);
    --mat-fab-foreground-color: #fff;
}

.btn-link {
    background-color: transparent;
    padding: 0;
    border: none;
    cursor: pointer;
}

.btn-loader {
  height: 80%;
  aspect-ratio: 1;
  border-radius: 50%;
  border: 2px solid currentColor;
  display: inline-block;
  height: 1.6em;
  border-width: 2px;
  // border: 5px solid var(--green-80);
  animation:
    l20-1 0.8s infinite linear alternate,
    l20-2 1.6s infinite linear;
}

.btn-info {
  position: relative;
  // position: relative;
  line-height: 1.4em;
  display: inline-flex;
  justify-content: center;

  .info {
    width: 15px;
    height: 15px;
    font-size: 16px;
  }

  &:hover {
      app-teaching-bubble {
          display: inline-block;
      }
  }
}

.mat-mdc-button .mat-mdc-button-touch-target {
  height: var(--button-height) !important;
}

@keyframes l20-1{
   0%    {clip-path: polygon(50% 50%,0       0,  50%   0%,  50%    0%, 50%    0%, 50%    0%, 50%    0% )}
   12.5% {clip-path: polygon(50% 50%,0       0,  50%   0%,  100%   0%, 100%   0%, 100%   0%, 100%   0% )}
   25%   {clip-path: polygon(50% 50%,0       0,  50%   0%,  100%   0%, 100% 100%, 100% 100%, 100% 100% )}
   50%   {clip-path: polygon(50% 50%,0       0,  50%   0%,  100%   0%, 100% 100%, 50%  100%, 0%   100% )}
   62.5% {clip-path: polygon(50% 50%,100%    0, 100%   0%,  100%   0%, 100% 100%, 50%  100%, 0%   100% )}
   75%   {clip-path: polygon(50% 50%,100% 100%, 100% 100%,  100% 100%, 100% 100%, 50%  100%, 0%   100% )}
   100%  {clip-path: polygon(50% 50%,50%  100%,  50% 100%,   50% 100%,  50% 100%, 50%  100%, 0%   100% )}
}
@keyframes l20-2{
  0%    {transform:scaleY(1)  rotate(0deg)}
  49.99%{transform:scaleY(1)  rotate(135deg)}
  50%   {transform:scaleY(-1) rotate(0deg)}
  100%  {transform:scaleY(-1) rotate(-135deg)}
}
