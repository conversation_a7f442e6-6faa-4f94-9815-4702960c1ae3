import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { DeviceService } from '../../../../services/x_apis/masters/device.service';
import { Device, Devices } from '../../../../models/x_models/masters/device';
import { AppRoutes } from '../../../../enums/app-routes';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { DeviceComponent } from './device.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-devices',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    MatSortModule,
    MatFormFieldModule,
    DeviceComponent ,
    TranslateModule,
    MatToolbarModule
  ],
  templateUrl: './devices.component.html',
  styleUrls: ['./devices.component.scss']
})

export class DevicesComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  deviceService = inject(DeviceService)
  deviceResource = resource({ loader: () => this.deviceService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.deviceResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  device = signal<Device>({
    id: 0,
    imeiNo: "",
    deviceManufacturerTypeName: "",
    providerName: "",
    protocolName: ""
  });

  displayedColumns: string[] = [
    'imeino',
    'Name',
    'provider',
    'protocol',
    'actions',
  ];
  devices = signal<Devices[]>([]);
  protected readonly AppRoutes = AppRoutes;  // Need clarification

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.device.set({
      id: 0,
      imeiNo: "",
      deviceManufacturerTypeName: "",
      providerName: "",
      protocolName: ""
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(device: Device) {
    this.device.set(device);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(device: Device) {
    this.device.set(device);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.deviceResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.deviceResource.reload();
    this.search.setValue("");
  }

  async onDelete(device: Device) {
    await confirmAndDelete(device, device.deviceManufacturerTypeName, 'device', this.deviceService, () => this.deviceResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
