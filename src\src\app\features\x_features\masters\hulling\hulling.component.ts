import { Component, computed, ElementRef, inject, input, output, resource, signal, viewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormsModule, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { <PERSON><PERSON><PERSON>on } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { MatNativeDateModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { Hulling } from '../../../../models/x_models/masters/hulling';
import { HullingService } from '../../../../services/x_apis/masters/hulling.service';
import { MatRadioModule } from '@angular/material/radio';
import { LookUpResponse } from '../../../../models/x_models/lookup';
import { DatePipe } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';
@Component({
  selector: 'app-hulling',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatNativeDateModule,
    MatRadioModule,
    MatDatepickerModule,
    MatToolbarModule,
    TranslateModule,
    MatFormFieldModule],
  providers: [DatePipe],
  templateUrl: './hulling.component.html',
  styleUrl: './hulling.component.scss'
})
export class HullingComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  hullingService = inject(HullingService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);


  translate = inject(TranslateService);
  taluks = signal<LookUpResponse[]>([]);
  paddys = resource({ loader: () => this.lookupService.getPaddy() }).value;
  mrms = resource({ loader: () => this.lookupService.getMrm() }).value;
  mills = resource({ loader: () => this.lookupService.getMill() }).value;
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  agencys = resource({ loader: () => this.lookupService.getAgency() }).value;

  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  router = inject(Router);

  uniqueId = input.required<any>();
  hulling = signal<Hulling | null>(null);
  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')
  search3 = viewChild.required<ElementRef<HTMLInputElement>>('search3')
  search4 = viewChild.required<ElementRef<HTMLInputElement>>('search4')
  search5 = viewChild.required<ElementRef<HTMLInputElement>>('search5')
  search6 = viewChild.required<ElementRef<HTMLInputElement>>('search6')

  form = this.fb.group({
    id: this.fb.control<number>(0),
    hullingCode: this.fb.control<string>('0', Validators.required),
    hullingName: this.fb.control<string>('', Validators.required),
    hullingRegionalName: this.fb.control<string>('', Validators.required),
    address: this.fb.control<string>('', Validators.required),
    presentAddress: this.fb.control<string>('', Validators.required),
    pincode: this.fb.control<string>('', Validators.required),
    fortifiedType: this.fb.control<number>(1, Validators.required),
    paddyType: this.fb.control<number | null>(null, Validators.required),
    mrmType: this.fb.control<number | null>(null, Validators.required),
    latitude: this.fb.control<number | null>(null, Validators.required),
    longitude: this.fb.control<number | null>(null, Validators.required),
    landmark: this.fb.control<string>('', Validators.required),
    millerName: this.fb.control<string>('', Validators.required),
    millType: this.fb.control<number | null>(null, Validators.required),
    factoryLicenseNo: this.fb.control<string>('', Validators.required),
    factoryLicenseExpiryTs: this.fb.control<string>('', Validators.required),
    boilerLicenseNo: this.fb.control<string>('', Validators.required),
    boilerLicenseExpiryTs: this.fb.control<string>('', Validators.required),
    fromDate: this.fb.control<string>('', Validators.required),
    toDate: this.fb.control<string>('', null),
    validFrom: this.fb.control<string>('', Validators.required),
    validTo: this.fb.control<string>('', Validators.required),
    modelNo: this.fb.control<string>('', Validators.required),
    gstNo: this.fb.control<string>('', [Validators.required, this.gstValidator]),
    panNo: this.fb.control<string>('', [Validators.required, this.panValidator]),
    isGeneratorAvailable: this.fb.control<boolean>(true, Validators.required),
    generatorCapacity: this.fb.control<number | null>(null, Validators.required),
    isSeparateEbMeter: this.fb.control<boolean>(true, Validators.required),
    regionId: this.fb.control<number | null>(null, Validators.required),
    talukId: this.fb.control<number | null>(1, Validators.required),
    agencyId: this.fb.control<number | null>(null, Validators.required),
  });

  constructor(private datePipe: DatePipe) { }

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
    this.form.get("regionId")?.valueChanges.subscribe(res => {
      this.getTaluk();
    })
  }

  readonly regionSearch = signal('');
  readonly talukSearch = signal('');
  readonly agencySearch = signal('');
  readonly millSearch = signal('');
  readonly mrmSearch = signal('');
  readonly paddySearch = signal('');



  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.regionSearch().toLowerCase())
    )
  );

  readonly filteredTaluks = computed(() =>
    this.taluks()?.filter(x =>
      x.value.toLowerCase().includes(this.talukSearch().toLowerCase())
    )
  );

  readonly filteredAgency = computed(() =>
    this.agencys()?.filter(x =>
      x.value.toLowerCase().includes(this.agencySearch().toLowerCase())
    )
  );

   readonly filteredMill = computed(() =>
    this.mills()?.filter(x =>
      x.value.toLowerCase().includes(this.millSearch().toLowerCase())
    )
  );

   readonly filteredMRM = computed(() =>
    this.mrms()?.filter(x =>
      x.value.toLowerCase().includes(this.mrmSearch().toLowerCase())
    )
  );

  readonly filteredPaddy = computed(() =>
    this.paddys()?.filter(x =>
      x.value.toLowerCase().includes(this.paddySearch().toLowerCase())
    )
  );

  filter(value: any, type:string) {
     if (type === 'region') {
      this.regionSearch.set(value.target.value);
    } else if (type === 'taluk') {
      this.talukSearch.set(value.target.value);
    } else if(type=='agency'){
      this.agencySearch.set(value.target.value);
    } else if(type=='mill'){
      this.millSearch.set(value.target.value);
    } else if(type=='mrm'){
      this.mrmSearch.set(value.target.value);
    } else {
      this.paddySearch.set(value.target.value);
    }
  }
    resetSearch(type: any) {

    if (type === 'region') {
      this.search1().nativeElement.value = '';
      this.regionSearch.set('');
    } else if (type === 'taluk') {
      this.talukSearch.set('');
      this.search2().nativeElement.value = '';
    } else if(type=='agency') {
      this.agencySearch.set('');
      this.search3().nativeElement.value = '';
    } else if(type=='mill') {
      this.millSearch.set('');
      this.search4().nativeElement.value = '';
    } else if(type=='mrm') {
      this.mrmSearch.set('');
      this.search5().nativeElement.value = '';
    } else {
      this.paddySearch.set('');
      this.search6().nativeElement.value = '';
    } 

  }

  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
    // this.mrms.set(await this.lookupService.getMrm());
    // this.mills.set(await this.lookupService.getMill());
    this.getTaluk();
  }

  async getTaluk() {
    const res = await this.lookupService.getTaluk(this.form.value.regionId!, 0)
    this.taluks.set(res)
  }

  async getFormData() {
    const res = await this.hullingService.getById(this.uniqueId());
    this.hulling.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
    this.form.get('hullingCode')?.disable();
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      formValue.factoryLicenseExpiryTs = this.formatDateToISOString(formValue.factoryLicenseExpiryTs);
      formValue.boilerLicenseExpiryTs = this.formatDateToISOString(formValue.boilerLicenseExpiryTs);
      formValue.fromDate = this.formatDateToISOString(formValue.fromDate);
      formValue.toDate = this.formatDateToISOString(formValue.toDate);
      formValue.validFrom = this.formatDateToISOString(formValue.validFrom);
      formValue.validTo = this.formatDateToISOString(formValue.validTo);
      const res = await this.hullingService.create(formValue as Hulling);
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Hulling ${this.form.value.hullingName} Created successfully.`)
        }
        else {
          this.alertService.success(`Hulling ${this.form.value.hullingName} Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }



  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

  gstValidator(control: AbstractControl): ValidationErrors | null {
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    const value = control.value?.toUpperCase();
    return gstRegex.test(value) ? null : { invalidGst: true };
  }

  panValidator(control: AbstractControl): ValidationErrors | null {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    const value = control.value?.toUpperCase();
    return panRegex.test(value) ? null : { invalidPan: true };
  }

  formatDateToISOString(date: any): string | null {
    if (!date) return null;
    const parsedDate = new Date(date);
    return parsedDate.toISOString();
  }

}
