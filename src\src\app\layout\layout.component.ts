import { Component, effect, inject, signal, viewChild } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { SidebarComponent } from './sidebar/sidebar.component';
import { MatSidenav, MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ViewModeService } from '../services/view-mode.service';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './header/header.component';
import { MatIconModule } from '@angular/material/icon';
import { SideBarService } from '../services/side-bar.service';
import { FooterComponent } from './footer/footer.component';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-layout',
  imports: [
    RouterOutlet,
    SidebarComponent,
    MatSidenavModule,
    MatToolbarModule,
    CommonModule,
    HeaderComponent,
    MatIconModule,
    FooterComponent,
    TranslateModule
],
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss'
})
export class LayoutComponent {
  viewModeService = inject(ViewModeService)
  sideBarEndSerivce = inject(SideBarService)
  viewMode = this.viewModeService.viewMode

  sideNavCollapsed = signal<boolean | null>(null);
  sideNav = viewChild<MatSidenav>("snav")
  sideNavFocused = signal<boolean>(false)

  sideNavEnd = viewChild<MatSidenav>("snavEnd")
  sideBarInputs = this.sideBarEndSerivce.sideBarInput
  sideBarComponent = this.sideBarEndSerivce.sideBarEndOpened

  sidebarHeight = this.sideBarEndSerivce.sidebarHeight
  sidebarMarginTop = this.sideBarEndSerivce.sidebarMarginTop

  constructor() {
    effect(() => {
      if (this.viewMode() === 'lg') {
        this.sideNav()?.open()
      } else {
        this.sideNav()?.close()
        this.sideNavCollapsed.set(false)
      }
    })

    effect(() => {
      const toggled = this.viewModeService.sideNavToggled()
      if (toggled) {
        this.sideNav()?.open()
      } else {
        this.sideNav()?.close()
      }
    })

    effect(() => {
      const sideBarEndComponent = this.sideBarEndSerivce.sideBarEndOpened()

      const closed = this.sideBarEndSerivce.sideBarEndClosed()
      if (sideBarEndComponent) {
        this.sideNavEnd()?.open()
      }
      if (closed) {
        this.sideNavEnd()?.close()
      }
    })
  }

  onSubmenuSelected() {
  if (this.viewMode() === 'lg') {
      this.sideNavCollapsed.set(!this.sideNavCollapsed())
    } else {
      this.sideNavCollapsed.set(false)
      this.sideNav()?.toggle()
    }
  }

  onSideNavToggle() {
    debugger
    if (this.viewMode() === 'lg') {
      this.sideNavCollapsed.set(!this.sideNavCollapsed())
    } else {
      this.sideNavCollapsed.set(false)
      this.sideNav()?.toggle()
    }
  }
  setSideNavFocused(value: boolean) {
    // if (this.viewMode() === 'md') {
    //   this.sideNavCollapsed.set(!value)
    // }
  }

   onMenuSelected(Menu:any) {
    if (this.viewMode() !== 'lg' && Menu.submenu.length==0) {
      this.sideNav()?.close()
    }
  }
}
