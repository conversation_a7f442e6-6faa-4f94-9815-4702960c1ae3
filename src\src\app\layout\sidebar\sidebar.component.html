<aside class="sidebar">
  <nav>
      <ul class="menus">
          @for(item of menu(); track item.title; let i = $index) {
          <!-- <li class="item" [style.--submenuCount]="item.submenu.length" [ngClass]="{'active': item.active, 'closing': closingIndex() === i, 'collapsed': sideNavCollapsed()}" (animationend)="onListItemAnimationEnd($event)"> -->

          <li class="item" [ngClass]="{'active': item.active, 'closing': closingIndex() === i, 'collapsed': sideNavCollapsed()}" (animationend)="onListItemAnimationEnd($event)">

              @if (item.submenu.length) {
                  <button class="btn-link" [class.active]="item.active" (click)="onMenuItemClick(i)">
                    <div class="link-content">
                      <div class="link-icon-wrapper">
                        <span class="material-symbols-outlined">{{item.icon}}</span>
                      </div>

                      @if (!sideNavCollapsed()) {
                        <div class="link-label">
                          <span class="label">{{item.title}}</span>
                        </div>
                        @if(item.submenu.length) {
                          <div class="link-expand-wrapper">
                            <span class="expand material-symbols-outlined">keyboard_arrow_down</span>
                          </div>
                        }
                      }
                    </div>
                  </button>
              } @else {
                  <a [class.active]="item.active" [routerLink]="item.path" (click)="onMenuItemClick(i)">
                      <div class="link-content">
                        <div class="link-icon-wrapper">
                          <span class="material-symbols-outlined">{{item.icon}}</span>
                        </div>
                        @if (!sideNavCollapsed()) {
                          <div class="link-label">
                            <span class="label">{{item.title}}</span>
                          </div>
                        }
                      </div>

                      @if(item.submenu.length) {
                        <span class="expand material-symbols-outlined">keyboard_arrow_down</span>
                      }
                  </a>
              }

              @if (item.active ) {
              <ul class="submenus">
                  @for(subitem of item.submenu; track subitem.title; let j = $index) {
                  <li tabindex="-1" class="sub-item" [routerLink]="subitem.path" (click)="onSubmenuItemClick(i, j)">
                      <a [class.active]="subitem.active" [routerLink]="subitem.path">
                        <div class="link-content">
                          <div class="link-icon-wrapper" [class.collapsed]="sideNavCollapsed()">
                            @if (this.sideNavCollapsed()) {
                              <mat-icon [class]="'icon' + ' ' +  subitem.icon" [svgIcon]="subitem.icon"></mat-icon>
                            } @else {
                              <div class="sub-item-dot"></div>
                               <!-- <span class="material-symbols-outlined">{{subitem.icon}}</span> -->

                            }

                          </div>
                          @if (!sideNavCollapsed()) {
                            <div class="link-label">
                              <span class="label">{{subitem.title}}</span>
                            </div>
                          }
                        </div>
                      </a>
                  </li>
                  }
              </ul>
              }
          </li>
          }
      </ul>
      <ul class="menus">
        <li class="item">
          <a [routerLink]="'/m/payroll'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
        <div class="link-content">
          <div class="link-icon-wrapper">
            <span class="material-symbols-outlined" style="color: #11074E;">insert_drive_file</span>
          </div>
          <div class="link-label">
            <span class="label" style="color: #11074E;">Payroll</span>
          </div>
        </div>
          </a>
        </li>
      </ul>
      <ul class="menus">
        <li class="item">
          <a [routerLink]="'/m/approvedlist'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
        <div class="link-content">
          <div class="link-icon-wrapper">
            <span class="material-symbols-outlined" style="color: #11074E;">receipt_long</span>
          </div>
          <div class="link-label">
            <span class="label" style="color: #11074E;">Payslip Approved List</span>
          </div>
        </div>
          </a>
        </li>
      </ul>
       <ul class="menus">
        <li class="item">
          <a [routerLink]="'/m/attendence'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
        <div class="link-content">
          <div class="link-icon-wrapper">
            <span class="material-symbols-outlined" style="color: #11074E;">person_add</span>
          </div>
          <div class="link-label">
            <span class="label" style="color: #11074E;">Attendance</span>
          </div>
        </div>
          </a>
        </li>
      </ul>
      <ul class="menus">
        <li class="item">
          <a [routerLink]="'/m/paymatrix'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
        <div class="link-content">
          <div class="link-icon-wrapper">
            <span class="material-symbols-outlined" style="color: #11074E;">grid_view</span>
          </div>
          <div class="link-label">
            <span class="label" style="color: #11074E;">Matrix pay</span>
          </div>
        </div>
          </a>
        </li>
      </ul>
       <ul class="menus">
        <li class="item">
          <a [routerLink]="'/m/loanlist'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
        <div class="link-content">
          <div class="link-icon-wrapper">
            <span class="material-symbols-outlined" style="color: #11074E;">account_balance</span>
          </div>
          <div class="link-label">
            <span class="label" style="color: #11074E;">Loan Pending List </span>
          </div>
        </div>
          </a>
        </li>
      </ul>
     <ul class="menus">
      <li class="item">
        <a [routerLink]="'/m/leave-apply-new'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
          <div class="link-content">
            <div class="link-icon-wrapper">
              <span class="material-symbols-outlined" style="color: #11074E;">calendar_today</span>
            </div>
            <div class="link-label">
              <span class="label" style="color: #11074E;">Leave Apply</span>
            </div>
          </div>
        </a>
      </li>
     </ul>
     <ul class="menus">
      <li class="item">
        <a [routerLink]="'/m/pending-leaves'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
          <div class="link-content">
            <div class="link-icon-wrapper">
              <span class="material-symbols-outlined" style="color: #11074E;">calendar_today</span>
            </div>
            <div class="link-label">
              <span class="label" style="color: #11074E;">Pending Leaves</span>
            </div>
          </div>
        </a>
      </li>
     </ul>
     <ul class="menus">
      <li class="item">
        <a [routerLink]="'/m/history-leaves'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
          <div class="link-content">
            <div class="link-icon-wrapper">
              <span class="material-symbols-outlined" style="color: #11074E;">calendar_today</span>
            </div>
            <div class="link-label">
              <span class="label" style="color: #11074E;">History Leaves</span>
            </div>
          </div>
        </a>
      </li>
     </ul>
     <!-- <ul class="menus">
      <li class="item">
        <a [routerLink]="'/m/leave-balance'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
          <div class="link-content">
            <div class="link-icon-wrapper">
              <span class="material-symbols-outlined" style="color: #11074E;">calendar_today</span>
            </div>
            <div class="link-label">
              <span class="label" style="color: #11074E;">Leave Balance</span>
            </div>
          </div>
        </a>
      </li>
     </ul> -->
     <ul class="menus">
      <li class="item">
        <a [routerLink]="'/m/holiday-leave'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
          <div class="link-content">
            <div class="link-icon-wrapper">
              <span class="material-symbols-outlined" style="color: #11074E;">calendar_today</span>
            </div>
            <div class="link-label">
              <span class="label" style="color: #11074E;">Holiday Calendar</span>
            </div>
          </div>
        </a>
      </li>
     </ul>
     <ul class="menus">
      <li class="item">
        <a [routerLink]="'/m/add-holiday'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
          <div class="link-content">
            <div class="link-icon-wrapper">
              <span class="material-symbols-outlined" style="color: #11074E;">calendar_today</span>
            </div>
            <div class="link-label">
              <span class="label" style="color: #11074E;">Add Holiday</span>
            </div>
          </div>
        </a>
      </li>
     </ul>
     <ul class="menus">
      <li class="item">
        <a [routerLink]="'/m/leave-balance-add'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
          <div class="link-content">
            <div class="link-icon-wrapper">
              <span class="material-symbols-outlined" style="color: #11074E;">calendar_today</span>
            </div>
            <div class="link-label">
              <span class="label" style="color: #11074E;">Leave Balance Add</span>
            </div>
          </div>
        </a>
      </li>
     </ul>
        <!-- 👇 Transfer Module Menu -->
      <ul class="menus">
        <!-- Transfer Dashboard -->
        <li class="item">
          <a [routerLink]="'/m/transfer-dashboard'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
            <div class="link-content">
              <div class="link-icon-wrapper">
                <span class="material-symbols-outlined" style="color: #11074E;">dashboard</span>
              </div>
              <div class="link-label">
                <span class="label" style="color: #11074E;">Transfer Dashboard</span>
              </div>
            </div>
          </a>
        </li>

        <!-- Within Region Transfer -->
        <li class="item">
          <a [routerLink]="'/m/within-region'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
            <div class="link-content">
              <div class="link-icon-wrapper">
                <span class="material-symbols-outlined" style="color: #11074E;">swap_horiz</span>
              </div>
              <div class="link-label">
                <span class="label" style="color: #11074E;">Within Region Transfer</span>
              </div>
            </div>
          </a>
        </li>

        <!-- Inter Region Transfer -->
        <li class="item">
          <a [routerLink]="'/m/inter-region'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
            <div class="link-content">
              <div class="link-icon-wrapper">
                <span class="material-symbols-outlined" style="color: #11074E;">flight_takeoff</span>
              </div>
              <div class="link-label">
                <span class="label" style="color: #11074E;">Inter Region Transfer</span>
              </div>
            </div>
          </a>
        </li>

        <!-- Mutual Transfer -->
        <li class="item">
          <a [routerLink]="'/m/mutual-transfer'" class="btn-link" style="display: flex; align-items: center; text-decoration: none; color: #11074E;">
            <div class="link-content">
              <div class="link-icon-wrapper">
                <span class="material-symbols-outlined" style="color: #11074E;">swap_horizontal_circle</span>
              </div>
              <div class="link-label">
                <span class="label" style="color: #11074E;">Mutual Transfer</span>
              </div>
            </div>
          </a>
        </li>
      </ul>
           
          
      

  </nav>

   
</aside>
