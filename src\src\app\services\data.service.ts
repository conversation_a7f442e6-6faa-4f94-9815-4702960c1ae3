import { HttpClient, HttpContext } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { firstValueFrom, map } from 'rxjs';
import { HttpContextToken } from "@angular/common/http";

interface Options {skipLoader?: boolean, skipToken?: boolean}

@Injectable({
  providedIn: 'root',
})
export class DataService {
  http = inject(HttpClient);
  #baseUrl = environment.apiBaseUrl + "/api";
  #cache: any = {};

  constructor() {}

  async get<T>(route: any, options: Options={}): Promise<T> {
    const res$ = this.http.get<T>(this.#baseUrl + route, {context: this.setContext(options)})
    return await firstValueFrom(res$)
  }

  async getWithParams<T>(route:any, params:any, options:Options={}): Promise<T> {
      const res$ = this.http.get<T>(this.#baseUrl + route, {params: params, context: this.setContext(options) });
      return await firstValueFrom(res$)
  }

  async post<T>(route:any, data:any, options:Options={}): Promise<T> {
    const res$ = this.http.post<T>(this.#baseUrl + route, data, {context: this.setContext(options)});
    return await firstValueFrom(res$)
  }

  async put<T>(route:any, data:any, options:Options={}): Promise<T> {
    const res$ = this.http.put<T>(this.#baseUrl + route, data, {context: this.setContext(options)});
    return await firstValueFrom(res$)
  }

  async delete<T>(route: any, data: any = {}, options:Options={}): Promise<T> {
    const res$ = this.http.delete<T>(this.#baseUrl + route, {body: data, context: this.setContext(options)});
    return await firstValueFrom(res$)
  }

  async getExternal<T>(route:any, options:Options={}): Promise<T> {
    const res$ = this.http.get<T>(route, {context: this.setContext(options)});
    return await firstValueFrom(res$)
  }

  addToCache(key: string, value: any) {
    this.#cache[key] = value
  }

  getFromCache(key: string) {
    return this.#cache[key]
  }

  clearFromCache(key: string) {
    delete this.#cache[key];
  }

  clearCache() {
    this.#cache = {};
  }

  clearRouteCache(route:any) {
    this.#cache[route] = null;
  }

  dataForRouteIsCached(route: any, refresh:any) {
    return this.#cache[route] && (refresh === false || refresh === undefined);
  }

  setContext(options:Options) {
    const context = new HttpContext()
    if (options.skipLoader) {
      context.set(SkipLoader, true)
    }
    if (options.skipToken) {
      context.set(SkipToken, true)
    }
    return context
  }
}

export const SkipLoader = new HttpContextToken(() => false)
export const SkipToken = new HttpContextToken(() => false)
