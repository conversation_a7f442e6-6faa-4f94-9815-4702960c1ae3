@if (!showUser()) {
  <div class="component card">
    <div class="page-header">
      <h1>{{menuService.activeMenu()?.title}}</h1>
    </div>
    <div class="header">
      <div class="filters-wrapper">
        <mat-form-field class="search hide-subscript" appearance="outline">
          <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
          <input id="search" [formControl]="search" (keydown.enter)="onSearch()" autocomplete="off" matInput placeholder="Search">
        </mat-form-field>
        <div class="filters-more">
          @if(access()?.canAdd) {
            <button (click)="onAddUser()" class="btn btn-filter" mat-icon-button>
              <mat-icon class="icon-filter material-symbols-outlined" matPrefix >add</mat-icon>
            </button>
          }
          <button (click)="getUsers()" class="btn btn-filter" mat-icon-button>
            <mat-icon class="icon-filter material-symbols-outlined" matPrefix >sync</mat-icon>
          </button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="table-wrapper">
        <table mat-table [dataSource]="dataSource()" matSort class="flex-table">
  
          <ng-container matColumnDef="fullName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Full Name </th>
            <td mat-cell *matCellDef="let row"> {{row.fullName}} </td>
          </ng-container>
    
          <ng-container matColumnDef="roleName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Role </th>
            <td mat-cell *matCellDef="let row"> {{row.roleName }} </td>
          </ng-container>
    
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Email </th>
            <td mat-cell *matCellDef="let row"> {{row.email }} </td>
          </ng-container>
    
          <ng-container matColumnDef="mobileNo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Mobile Number </th>
            <td mat-cell *matCellDef="let row"> {{row.mobileNo }} </td>
          </ng-container>
    
          <ng-container matColumnDef="statusType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td [attr.statusType]="row.statusType" mat-cell *matCellDef="let row"> {{status[row.statusType]
              }} </td>
          </ng-container>
    
          <ng-container matColumnDef="actions">
            <th class="actions" mat-header-cell *matHeaderCellDef> Actions </th>
            <td class="actions" mat-cell *matCellDef="let row">
              @if (access()?.canView) {
              <button title="View" (click)="onViewUser(row)" class="btn-icon btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
              </button>
              }
    
              @if(access()?.canEdit) {
              <button title="Edit" (click)="onEditUser(row)" class="btn-icon btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">edit</mat-icon>
              </button>
              }
    
              @if(access()?.canDeActivate) {
              <button title="Deactivate" (click)="onDeactivateUser(row)" class="btn-icon btn-delete btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">close</mat-icon>
              </button>
              }
    
              @if(access()?.canActivate) {
              <button title="Activate" (click)="onActivateUser(row)" class="btn-icon btn-activate btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">check</mat-icon>
              </button>
              }
    
              @if(access()?.canReset) {
              <button title="Reset password" class="btn-icon btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">lock_reset</mat-icon>
              </button>
              }
    
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  
          <tr *matNoDataRow>
            <ng-container *ngTemplateOutlet="shimmer"></ng-container>
          </tr>
        </table>
        <mat-paginator class="paginator" pageSize="8" [pageSizeOptions]="[5, 10, 15]" aria-label="Select page of users"></mat-paginator>
      </div>
      <div class="mobile-wrapper">
        <div class="un-cards">
          @for (item of dataSource().data; track item) {
            <div class="un-card">
              <div class="desc">
                <div class="quote-no">{{item.fullName}}</div>
                <div style="line-height: 1em;"><span class="tracking-no">{{item.roleName}}</span> &nbsp; <span class="tracking-no">{{status[item.statusType]}}</span></div>
                <div class="quote-no">{{item.email}}</div>
                <div class="quote-no">{{item.mobileNo}}</div>
              </div>
              <div class="actions">
                <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu" aria-label="Card actions">
                  <mat-icon>more_vert</mat-icon>
                </button>
                <mat-menu #menu="matMenu">
  
                  @if (access()?.canView) {
                    <button (click)="onViewUser(item)" mat-menu-item>
                      <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                      <span>View</span>
                    </button>
                    }
          
                    @if(access()?.canEdit) {
                    <button (click)="onEditUser(item)" mat-menu-item>
                      <mat-icon class="material-symbols-rounded">edit</mat-icon>
                      <span>Edit</span>
                    </button>
                    }
          
                    @if(access()?.canDeActivate) {
                    <button (click)="onDeactivateUser(item)" mat-menu-item>
                      <mat-icon class="material-symbols-rounded">close</mat-icon>
                      <span>Deactivate</span>
                    </button>
                    }
          
                    @if(access()?.canActivate) {
                    <button (click)="onActivateUser(item)" mat-menu-item>
                      <mat-icon class="material-symbols-rounded">check</mat-icon>
                      <span>Activate</span>
                    </button>
                    }
          
                    @if(access()?.canReset) {
                    <button mat-menu-item>
                      <mat-icon class="material-symbols-rounded">lock_reset</mat-icon>
                      <span>Reset password</span>
                    </button>
                    }
                </mat-menu>
              </div>
            </div>
          }
        </div>
      </div>
    </div>
  </div>
  
  <ng-template #shimmer>
    <div class="grid-shimmer">
      @for (num of [1, 2, 3, 4, 5, 6, 7, 8]; track $index) {
        <div class="row">
          @for (item of [1, 2, 3, 4, 5, 6, 7, ]; track $index) {
            <div class="cell">
              <div class="shimmer-container">
                <div class="shimmer" [attr.col]="item"></div>
              </div>
            </div>
          }
        </div>
      }
    </div>
  </ng-template>
} @else {
  <app-user [userId]="user().id" [mode]="mode()" (closed)="onUserClose()"></app-user>
}
