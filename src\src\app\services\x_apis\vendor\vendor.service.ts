import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Vendor, VendorResponse } from '../../../models/x_models/vendor';

@Injectable({
    providedIn: 'root'
})
export class VendorService {
    dataService = inject(DataService)

    create(data: Vendor) {
        return this.dataService.post<Response>("/vendor", data)
    }

    get() {
        return this.dataService.get<VendorResponse>("/vendor")
    }

    getById(id: number) {
        return this.dataService.get<Vendor>(`/vendor/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/vendor/${id}`)
    }
}
