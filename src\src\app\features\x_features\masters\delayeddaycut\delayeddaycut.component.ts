import { Component, computed, inject, input, OnInit, output, resource, signal } from '@angular/core';
import { Form<PERSON>uilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { DelayeddaycutService } from '../../../../services/x_apis/masters/delayeddaycut.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { Delayeddaycut, DelayedDayCutById } from '../../../../models/x_models/masters/delayeddaycut';
import { MatButton } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { Mat<PERSON>orm<PERSON>ield, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-delayeddaycut',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatToolbarModule,
    MatFormFieldModule, TranslateModule
  ],
  templateUrl: './delayeddaycut.component.html',
  styleUrls: ['./delayeddaycut.component.scss']
})

export class DelayeddaycutComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  delayeddaysCutService = inject(DelayeddaycutService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);

  translate = inject(TranslateService);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  seasons = resource({ loader: () => this.lookupService.getSeason() }).value;
  delayeddaycut = resource({ loader: () => this.lookupService.getDelayedDayCut() }).value;


  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  router = inject(Router);

  uniqueId = input.required<any>();
  delayedDaysTypeName = input.required<any>();
  delayedDayCut = signal<DelayedDayCutById | null>(null);

  form = this.fb.group({
    id: this.fb.control<number>(0),
    delayedDaysType: this.fb.control<number | null>(null, Validators.required),
    seasonId: this.fb.control<number | null>(null, Validators.required),
    regionId: this.fb.control<number | null>(null, Validators.required),
    priceCut: this.fb.control<number | null>(null, Validators.required)
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
  }

  readonly search = signal('');

  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  readonly filteredDelayedDays = computed(() =>
    this.delayeddaycut()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  readonly filteredSeason = computed(() =>
    this.seasons()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  filter(value: any) {
    this.search.set(value.target.value);
  }

  async getFormData() {
    const res = await this.delayeddaysCutService.getById(this.uniqueId());
    this.delayedDayCut.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.delayeddaysCutService.create(formValue as DelayedDayCutById)
      this.closed.emit(true)
      if (formValue.id == 0) {
        this.alertService.success(`Hulling Price Cut Created successfully.`)
      }
      else {
        this.alertService.success(`Hulling Price Cut Updated successfully.`)
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

}
