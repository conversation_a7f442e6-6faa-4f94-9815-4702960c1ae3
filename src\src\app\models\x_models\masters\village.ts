export interface Village {
    id: number,
    villageName: string,
    villageRegionalName: string,
    villageLgdCode: string
    regionId?: number,
    unitId?: number,
    talukId?: number,
    blockId: number
}

export interface Villages {
    id: number,
    villageName: string,
    villageRegionalName: string,
    regionName: string,
    unitName: string,
    talukName: string,
    blockName: string,
    villageLgdCode: string
}

export type VillageResponse = Villages[];