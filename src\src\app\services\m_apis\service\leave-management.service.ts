import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environmentLeave } from '../../../../environments/environment';
import { Observable } from 'rxjs';
import { LeaveBalanceDTO } from '../../../enums/m_enums/leave.model';

@Injectable({
  providedIn: 'root'
})
export class LeaveManagementService {
base_url :string;
holiday_base_url:string;
leave_types_base_url:string;
  constructor(private http:HttpClient) {
    this.base_url = environmentLeave.LEAVE_API_URL;
    this.holiday_base_url = environmentLeave.Holiday_API_URL;
    this.leave_types_base_url = environmentLeave.Leave_types_API_URL;
   }

   applyForLeave(formData: FormData): Observable<any> {
    return this.http.post<any>(`${this.base_url}/apply`, formData);
  }
   getLeaveBalance(empId:any):Observable<any>{
    return this.http.get(`${this.base_url}/balance/${empId}`);
   }

   createLeaveBalance(data: { employeeId: string; leaveTypeId: number; year: number; initialBalance: number }): Observable<void> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' });
    const body = new URLSearchParams();
    body.set('employeeId', data.employeeId);
    body.set('leaveTypeId', data.leaveTypeId.toString());
    body.set('year', data.year.toString());
    body.set('initialBalance', data.initialBalance.toString());

    return this.http.post<void>(`${this.base_url}/create-balance`, body.toString(), { headers });
  }

  getLeaveBalances(employeeId: any, year: any): Observable<LeaveBalanceDTO[]> {
    const params = new HttpParams()
      .set('employeeId', employeeId)
      .set('year', year.toString());
    return this.http.post<LeaveBalanceDTO[]>(`${this.base_url}/balance`, {}, { params });
  }

  getLeaveBalanceByType(employeeId: any, leaveTypeId: any, year: any): Observable<LeaveBalanceDTO> {
    const params = new HttpParams()
      .set('employeeId', employeeId)
      .set('leaveTypeId', leaveTypeId.toString())
      .set('year', year.toString());
    return this.http.post<LeaveBalanceDTO>(`${this.base_url}/balance-by-type`, {}, { params });
  }

  getLeaveHistory(employeeId:any):Observable<any>{
    return this.http.get(`${this.base_url}/applications/employee/${employeeId}`);
  }

  getPendingLeavesByEmpId(employeeId:any):Observable<any>{
    return this.http.get(`${this.base_url}/pending/applications/employee/${employeeId}`);
  }

  getPendingLeavesByManagerId(managerId:any):Observable<any>{
    return this.http.get(`${this.base_url}/approvals/pending/${managerId}`);
  }

   loadApprover():Observable<any>{
    return this.http.get(`${this.base_url}/approver`);
   }

   getLeaveApplicationByEmpId(employeeId:any){
    return this.http.get(`${this.base_url}/applications/employee/${employeeId}`);
   }

   getPendingLeaves(managerId:any){
    return this.http.get(`${this.base_url}/applications/pending/${managerId}`);
   }

   //Holidays 
   saveHoliday(holidayData:any):Observable<any>{
    return this.http.post(`${this.holiday_base_url}/save/holiday`,holidayData);
   }

   getHolidays(year: number, month: number): Observable<any[]> {
    return this.http.get<any[]>(`${this.holiday_base_url}/get/holidays/${year}/${month}`);
  }

  getAllLeaveTypes(): Observable<any[]> {
    return this.http.get<any[]>(`${this.leave_types_base_url}/getAllLeaveTypes`);
  }
}
