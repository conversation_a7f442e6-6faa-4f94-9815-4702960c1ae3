export interface SessionData {
  sub: string
  jti: string
  "user.username": string
  "user.fullname": string
  "http://schemas.microsoft.com/ws/2008/06/identity/claims/role": string
  code: string
  "user.timezone": string
  iss: string
  aud: string
  iat: number
  exp: number

  responseData?: string
  responseType?: string
  validationErrors?: string
  accessToken: string,

  userId?: number
  roleId?: number
  blockId:number,
  dpcId:number,
  firstname:string,
  id: number,
  lastname: string,
  regionId: number,
  talukId: number,  
  villageId: number,
  // roleName?: string
  // email?: string
  // mobileNo?: string
}
