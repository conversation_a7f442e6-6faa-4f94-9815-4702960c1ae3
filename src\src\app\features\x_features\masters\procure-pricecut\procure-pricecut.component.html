<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{procurementPriceCutTypeName()}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

  <div class="card component">
  
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="procurementPriceCutType" class="required-label">{{'ProcurementPriceCutType' | translate}} </label>
          <mat-form-field appearance="outline">
            <mat-select id="procurementPriceCutType" formControlName="procurementPriceCutType" (openedChange)="resetSearch('pricetype')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'pricetype')">
              </mat-form-field>
              @for (role of filteredPricetypes(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.procurementPriceCutType.errors?.['required']) {
                <!-- Procurement price cut type is required -->
                {{'ProcurementPriceCutType' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="productId" class="required-label">{{'Product' | translate}} </label>
          <mat-form-field appearance="outline">
            <mat-select id="productId" formControlName="productId" (openedChange)="resetSearch('product')">
             <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'product')">
              </mat-form-field>
              @for (role of filteredProducts(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.productId.errors?.['required']) {
                <!-- Product is required -->
                {{'Product' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="seasonId" class="required-label">{{'Season' | translate}} </label>
          <mat-form-field appearance="outline">
            <mat-select id="seasonId" formControlName="seasonId" (openedChange)="resetSearch('season')">
             <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search3 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'season')">
              </mat-form-field>
              @for (role of filteredSeasons(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.seasonId.errors?.['required']) {
                <!-- Season is required -->
                {{'Season' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="regionId" class="">{{'Region' | translate}} </label>
          <mat-form-field appearance="outline">
            <mat-select id="regionId" formControlName="regionId" (openedChange)="resetSearch('region')">
             <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search4 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'region')">
              </mat-form-field>
              @for (role of filteredRegions(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.regionId.errors?.['required']) {
                <!-- Region is required -->
                {{'Region' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="minRange" class="required-label">{{'MinimumRange' | translate}} </label>
          <mat-form-field>
            <input id="minRange" formControlName="minRange" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.minRange.errors?.['required']) {
                <!-- Minimum range is required -->
                {{'MinimumRange' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="maxRange" class="required-label">{{'MaximumRange' | translate}} </label>
          <mat-form-field>
            <input id="maxRange" formControlName="maxRange" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.maxRange.errors?.['required']) {
                <!-- Maximum range is required -->
                {{'MaximumRange' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="priceCutAmount" class="required-label">{{'PriceCutAmount' | translate}} </label>
          <mat-form-field>
            <input id="priceCutAmount" formControlName="priceCutAmount" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.priceCutAmount.errors?.['required']) {
                <!-- Price Cut Amount is required -->
                {{'PriceCutAmount' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

      

        


      </div>

      <div class="actions">
     
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' | translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0
          ? ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>


    </form>
  </div>
