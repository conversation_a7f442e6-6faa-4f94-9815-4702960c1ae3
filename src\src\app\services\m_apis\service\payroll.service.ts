import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment, environmentStaging } from '../../../../environments/environment';
import { Observable } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class PayrollService {
  getMatrixPayData() {
    throw new Error('Method not implemented.');
  }
private baseUrl: string = 'http://localhost:8080/employee';
private baseUrl1: string = 'http://localhost:8080/download';
private baseMapUrl: string | undefined;
private masterUrl: string;
private downloadUrl: string;
private matrixUrl:String;
private employeeloanUrl:String;
  
  constructor(private http: HttpClient) {

    this.masterUrl=environment.apiBaseUrl;
    this.baseMapUrl = environmentStaging.baseUrl+environmentStaging.baseMap;
    this.downloadUrl = environmentStaging.baseUrl+environmentStaging.download;
    this.matrixUrl=environmentStaging.baseUrl+environmentStaging.matrix;
    this.employeeloanUrl=environmentStaging.baseUrl+environmentStaging.employeeloan;
   }

   getLists(){
    return this.http.get(`${this.baseUrl}/get/particular/employee/pendinglist`);
   }
  

  getList() {
    return this.http.get(`${this.baseUrl}/getall/employee/details`);
  }

  getEmployeeById(empId: string) {
    return this.http.get(`${this.baseUrl}/get/particular/employee/${empId}`);
  }
  submitEmployeeDetails(empObj: any) {
    // empObj.payMonth = "2025-06"
    const dataObj: { empId: string, pfNumber: string, payMonth: string, basicPay: string } = { empId: empObj.empId, pfNumber: empObj.pfNumber, payMonth: empObj.payMonth, basicPay: empObj.basicPay };
    return this.http.post(`${this.baseUrl}/payslip-calculations/save/calculate`, dataObj);
  }
  // getapprovedList() {
  //   return this.http.get(`${this.baseUrl}/getall/employee/approvedlist`);
  // }
  getApprovedPayslips() {
    return this.http.get(`${this.baseMapUrl}/getall/employee/approvedlist`);
  }

  
  getpayslipapproved(empId: string) {
    return this.http.get(`${this.baseMapUrl}/calculate/employee/${empId}`);
  }

  //  downloadPayslip(empId: any) {
  //   // debugger
  //   return this.http.get(`${this.downloadUrl}/generate/${empId}`, {
  //     responseType: 'blob' // Important for binary PDF
  //   });
  // }

  downloadPayslip(empId: string) {
    window.open(`${this.downloadUrl}/generate/${empId}`, "_blank");
  }
 
creatematrix(matrixData: any) {
  return this.http.post(`${this.matrixUrl}/role/save`, matrixData);
}
deleteEmployee(empId: string) {
  return this.http.delete(`${this.baseUrl}/delete/employee/${empId}`);
}
getAllmatrix() {
  return this.http.get(`${this.matrixUrl}/getall/employee/paymatrix/admin`);
}
deleteByRoleAndLevel(employeeRole: string, level: string) {
  return this.http.delete(`${this.matrixUrl}/paymatrix/delete/${employeeRole}/${level}`);
}
updateMatrixByRoleAndLevel(employeeRole: string, level: string, updatedData: any) {
  return this.http.put(`${this.matrixUrl}/paymatrix/update/${employeeRole}/${level}`, updatedData);
}




getAllpendinglist() {
  return this.http.get(`${this.employeeloanUrl}/getall/employee/details/admin`);
}



 
}
