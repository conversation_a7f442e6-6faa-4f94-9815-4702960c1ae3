import { Component, OnInit,inject,input, output, resource } from '@angular/core';
import { PurchaseService } from '../../../../services/x_apis/procurement/purchase.service';
import { TranslateModule } from '@ngx-translate/core';
import { <PERSON><PERSON><PERSON>cyPipe, DatePipe } from '@angular/common';

@Component({
  selector: 'app-procurementdetails',
  imports:[TranslateModule,DatePipe,CurrencyPipe],
  templateUrl: './procurementdetails.component.html',
  styleUrls: ['./procurementdetails.component.scss']
})
export class ProcurementdetailsComponent implements OnInit {

  id=input.required<number>();
  closed=output<boolean>()
  purchaseService=inject(PurchaseService)
  procurementDetails=resource({loader:()=>this.purchaseService.getById(this.id())}).value

  constructor() { }

  ngOnInit() {
  }

  back()
  {
    this.closed.emit(true);
  }
}
