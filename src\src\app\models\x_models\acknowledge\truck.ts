export interface TruckForm {
    id: number
    cropName: string
    fromDate: string
    toDate: string
    seasonId: number | null
}
export interface TruckList {
    id: number
    truckMemoId: string
    truckMemoDate: string
    truckFrom: string
    product: string
    vehicleNo: string
    totalQty: number
    noOfBags : number
    gunnyType : string
    moisture : number
    storageTypeFrom: string
}

export type TruckResponse = TruckList[]