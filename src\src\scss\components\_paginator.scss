@use '@angular/material' as mat;

:root {
  @include mat.paginator-overrides((container-text-size: 14px));
}

body {
  mat-paginator {
    --mat-form-field-container-height: 28px;
    --mat-form-field-container-text-line-height: 28px;

    --mdc-icon-button-state-layer-size: 30px;
    --mdc-icon-button-icon-size: 22px;
    --mat-icon-button-state-layer-color: var(--theme-80);
  }
  --mat-paginator-enabled-icon-color: var(--theme-80);
  --mat-paginator-container-text-line-height: 1em;
  --mat-paginator-container-text-size: 10px;
  --mat-paginator-container-size: 70px;
  --mat-paginator-form-field-container-height: 28px;
  --mat-paginator-form-field-container-vertical-padding: 0px;

  .mat-mdc-paginator-page-size-select {
    width: 60px;
  }

  .mat-mdc-paginator-outer-container {
    container-type: inline-size;

    .mat-mdc-paginator-container {
      @container (max-width: 400px) {
        justify-content: space-between;
        .mat-mdc-paginator-page-size-label {
          display: none;
        }

        .mat-mdc-paginator-range-label {
          margin: 0 10px 0 10px;
        }
      }
    }
  }
}

//.mat-mdc-paginator {
//  border-radius: 0 0 8px 8px;
//  padding: 0 20px 15px 15px;
//}

.mat-mdc-paginator-page-size {
  align-items: center !important;
}
