<div class="card">
  <div class="component1">
    <div class="page-header1">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a (click)="navigateBack()"><h1>{{menuService.activeMenu()?.title}} </h1></a>
          </li>
          <!-- @if(cropId() == 0) {
          <li aria-current="page" class="breadcrumb-item active">{{'New' | translate}}
            {{menuService.activeMenu()?.title}}</li>
          } @else {
          <li aria-current="page" class="breadcrumb-item active"> {{crop()?.cropName}}</li>
          } -->
        </ol>
      </nav>

      @if(cropId() == 0) {
      <h1>{{'New' | translate}} {{menuService.activeMenu()?.title}}</h1>
      } @else {
      <h1>
        <!-- {{crop()?.cropName}} -->

        @if(mode() === "view") {
        <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
          <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
        </button>
        }
      </h1>
      }
    </div>



    <form [formGroup]="form" (ngSubmit)="onSubmit()">

      <h3>GST Details</h3>

      <div class="form">


        <div class="field">
          <label for="PANNo" class="required-label">{{'PANNo' | translate }}</label>
          <mat-form-field>
            <input id="panNo" formControlName="PANNo" matInput (input)="onInput($event,'/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.PANNo.errors?.['required']) {
              {{'PANNo' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.get('PANNo')?.hasError('invalidPan')) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="GSTNumber" class="required-label">{{'GSTNo' | translate }}</label>
          <mat-form-field>
            <input id="GSTNumber" formControlName="GSTNumber" matInput
              (input)="onInput($event,'/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/')" maxlength="15">
            <mat-error>
              @if(form.controls.GSTNumber.errors?.['required']) {
              {{'GSTNo' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.controls.GSTNumber.errors?.['invalidGst']) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="LegalBusinessName">{{'LegalBusinessName' | translate }}</label>
          <mat-form-field>
            <input id="LegalBusinessName" formControlName="LegalBusinessName" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="uin">{{'UIN ' | translate }}</label>
          <mat-form-field>
            <input id="uin" formControlName="uin" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="DateofRegistration">{{'DateofRegistration' | translate }}</label>
          <mat-form-field>
            <input id="DateofRegistration" formControlName="DateofRegistration" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="StatusofGSTIN">{{'StatusofGSTIN' | translate }}</label>
          <mat-form-field>
            <input id="StatusofGSTIN" formControlName="StatusofGSTIN" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="NatureofBusiness">{{'NatureofBusiness' | translate }}</label>
          <mat-form-field>
            <input id="NatureofBusiness" formControlName="NatureofBusiness" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="LandlineNumberofCompany">{{'LandlineNumberofCompany' | translate }}</label>
          <mat-form-field>
            <input id="LandlineNumberofCompany" formControlName="LandlineNumberofCompany" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="ContactPersonName">{{'ContactPersonName' | translate }}</label>
          <mat-form-field>
            <input id="ContactPersonName" formControlName="ContactPersonName" matInput readonly>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Address">{{'Address' | translate }}</label>
          <mat-form-field>
            <input id="Address" formControlName="Address" matInput readonly>
          </mat-form-field>
        </div>
      </div>

      <!-- <h1>Other Details</h1> -->



      <h3>Bank Details</h3>

      <div class="form">
        <div class="field">
          <label for="BankAccountNumber" class="required-label">{{'BankAccountNumber' | translate }}</label>
          <mat-form-field>
            <input id="BankAccountNumber" formControlName="BankAccountNumber" matInput
              (input)="onInput($event,'/^[0-9A-Za-z]*$/')" maxlength="12">
            <mat-error>
              @if(form.controls.BankAccountNumber.errors?.['required']) {
              {{'BankAccountNumber' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

          <div class="field">
            <label for="ConfirmBankAccountNumber" class="required-label">{{'ConfirmBankAccountNumber' | translate }}</label>
            <mat-form-field>
              <input id="ConfirmBankAccountNumber" formControlName="ConfirmBankAccountNumber" matInput
                (input)="onInput($event,'/^[0-9A-Za-z]*$/')" maxlength="12">
              <mat-error>
                @if(form.controls.ConfirmBankAccountNumber.errors?.['required']) {
                {{'ConfirmBankAccountNumber' | translate}} {{'IsRequired' | translate}}
                }
                @else if (form.get('ConfirmBankAccountNumber')?.hasError('noMatch')) {
                  Account numbers do not match.
                  }
              </mat-error>
            </mat-form-field>
          </div>

        <div class="field">
          <label for="IFSCCode" class="required-label">{{'IFSCCode' | translate }}</label>
          <mat-form-field>
            <input id="IFSCCode" formControlName="IFSCCode" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.IFSCCode.errors?.['required']) {
              {{'IFSCCode' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="BankName" class="required-label">{{'BankName' | translate }}</label>
          <mat-form-field>
            <input id="BankName" formControlName="BankName" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.BankName.errors?.['required']) {
              {{'BankName' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Branch" class="required-label">{{'Branch' | translate }}</label>
          <mat-form-field>
            <input id="Branch" formControlName="Branch" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.Branch.errors?.['required']) {
              {{'Branch' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


       
      </div>

      <h3>Contract Details</h3>

      <div class="form">
        <div class="field">
          <label for="ContractID/WorkOrderID" class="required-label">{{'ContractID/WorkOrderID' | translate }}</label>
          <mat-form-field>
            <input id="ContractID/WorkOrderID" formControlName="ContractID" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.ContractID.errors?.['required']) {
              {{'ContractID/WorkOrderID' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="ContractStartDate" class="required-label">{{'ContractStartDate' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker5" formControlName="AgreementUpto">
            <mat-datepicker-toggle matIconSuffix [for]="picker5"></mat-datepicker-toggle>
            <mat-datepicker #picker5></mat-datepicker>
            <mat-error>
              @if(form.controls.ContractStartDate.errors?.['required']) {
              {{'ContractStartDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="ContractEndDate" class="required-label">{{'ContractEndDate' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker6" formControlName="AgreementUpto">
            <mat-datepicker-toggle matIconSuffix [for]="picker6"></mat-datepicker-toggle>
            <mat-datepicker #picker6></mat-datepicker>
            <mat-error>
              @if(form.controls.ContractEndDate.errors?.['required']) {
              {{'ContractEndDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="ContractDoc" class="required-label">{{'Contract/WorkOrderDocument' | translate }}</label>
          <mat-form-field>
            <input id="ContractDoc" formControlName="ContractDoc" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="14">
            <mat-error>
              @if(form.controls.ContractDoc.errors?.['required']) {
              {{'ContractDoc' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Commodity" class="required-label">{{'Commodity' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="Commodity" formControlName="Commodity">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (role of filteredTaluks(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.Commodity.errors?.['required']) {
              {{'Commodity' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

      </div>

      <!-- <h1>Mill Details</h1> -->


      <div class="form">
        <!-- <div class="field">
          <label for="FSSAI" class="required-label">{{'FSSAI' | translate }}</label>
          <mat-form-field>
            <input id="FSSAI" formControlName="FSSAI" matInput (input)="onInput($event,'/^[0-9]*$/')" maxlength="14">
            <mat-error>
              @if(form.controls.FSSAI.errors?.['required']) {
              {{'FSSAI' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div> -->

        <div class="field">
          <label for="MSME" class="required-label">{{'MSME' | translate }}</label>
          <mat-form-field>
            <input id="MSME" formControlName="MSME" matInput (input)="onInput($event,'/^[0-9A-Za-z]*$/')"
              maxlength="16">
            <mat-error>
              @if(form.controls.MSME.errors?.['required']) {
              {{'MSME' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="AgmarkLicense" class="required-label">{{'AgmarkLicense' | translate }}</label>
          <mat-form-field>
            <input id="AgmarkLicense" formControlName="AgmarkLicense" matInput (input)="onInput($event,'/^[0-9]*$/')"
              maxlength="15">
            <mat-error>
              @if(form.controls.AgmarkLicense.errors?.['required']) {
              {{'AgmarkLicense' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="ContactEmail " class="required-label">{{'ContactEmail' | translate }}</label>
          <mat-form-field>
            <input id="ContactEmail" formControlName="ContactEmail" matInput maxlength="50">
            <mat-error>
              @if(form.controls.ContactEmail.errors?.['required']) {
              {{'ContactEmail' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.controls.ContactEmail.errors?.['invalidEmail']) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="OwnerName " class="required-label">{{'OwnerName ' | translate }}</label>
          <mat-form-field>
            <input id="OwnerName" formControlName="OwnerName" matInput (input)="onInput($event,'/^[a-zA-Z\s]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.OwnerName.errors?.['required']) {
              {{'OwnerName' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="MobileNo" class="required-label">{{'MobileNo' | translate }}</label>
          <mat-form-field>
            <input id="MobileNo" formControlName="MobileNo" matInput (input)="onInput($event,'/^[0-9]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.MobileNo.errors?.['required']) {
              {{'MobileNo' | translate}} {{'IsRequired' | translate}}
              } @else if (form.controls.MobileNo.errors?.['pattern']) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="Pincode" class="required-label">{{'Pincode' | translate }}</label>
          <mat-form-field>
            <input id="Pincode" formControlName="Pincode" matInput (input)="onInput($event,'/^[0-9]*$/')" maxlength="6"
            minlength="6">
            <mat-error>
              @if(form.controls.Pincode.errors?.['required']) {
              {{'Pincode' | translate}} {{'IsRequired' | translate}}
              } @else if (form.controls.Pincode.errors?.['minlength']) {
              {{'InvalidFormat' | translate}}
              }
              @else if (form.controls.Pincode.errors?.['maxlength']) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


   
      </div>

      <!-- </div> -->

      <div class="actions">
        <button type="button" mat-flat-button (click)="navigateBack()" class="btn btn-secondary">{{'Reset' |
          translate}}</button>
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
          translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{cropId() == 0 ?
          ('Create' | translate) : ('Create' | translate)}}</button>
        }
      </div>
    </form>
  </div>
</div>