<form>
  <header class="header-component">
    <div class="hederlogo">
      <!-- @if (viewMode() !== 'lg') { -->
      <button (click)="openSideNav()" type="button" class="btn btn-menu" mat-icon-button aria-label="Menu">
        <mat-icon class="material-symbols-rounded">menu</mat-icon>
      </button>
      <!-- } -->
      @if (viewMode() !== 'xs'){
      <img [src]="currentLang === 'en' ? '/images/logo/logo_en.png' : '/images/logo/logo_ta.png'">
      }
      @if (viewMode() == 'xs'){
      <!-- <img src="/images/Tncsclogo.png"> -->
      <img [src]="currentLang === 'en' ? '/images/logo/logo_mob_en.png' : '/images/logo/logo_mob_ta.png'">
      }
    </div>

    <div class="profile">
      <!-- <div class="custom-toggle-group">
        <button type="button" [class.selected]="currentLang === 'en'" (click)="switchLanguage('en')" class="leftbtn">
          @if (viewMode() !== 'xs'){ENG}
          @if (viewMode() == 'xs'){EN}
        </button>

        <button type="button" [class.selected]="currentLang === 'ta'" (click)="switchLanguage('ta')" class="rightbtn">
          @if (viewMode() !== 'xs'){தமிழ்}
          @if (viewMode() == 'xs'){தமி}
        </button>
      </div> -->

      <!-- @let fullname = sessionService.session()?.['user.fullname']; -->
      @let firstname = sessionService.session()?.firstname ;
      @let lastname = sessionService.session()?.lastname;
      <!-- @if (viewMode() !== 'xs'){
      <span class="profileName"> {{firstname+" "+lastname}}</span>
      } -->


      @if(firstname && lastname) {
        <button type="button" class="btn btn-avatar" [matMenuTriggerFor]="menu" aria-label="User Menu">
          {{firstname[0]+lastname[0]}}</button>
      } @else if(firstname){
        <button type="button" class="btn btn-avatar" [matMenuTriggerFor]="menu" aria-label="User Menu">
          {{firstname[0]+firstname[1]}}</button>
      }
      
      <!-- <button type="button" class="btn btn-avatar" [matMenuTriggerFor]="menu" aria-label="User Menu">
        {{firstname[0]+lastname[0]}}</button> -->

      <mat-menu #menu="matMenu">
        <button mat-menu-item>
          <div class="name">{{firstname}}</div>
        </button>
        <button mat-menu-item>
          <mat-icon class="material-symbols-outlined">account_circle</mat-icon>
          <span>My profile</span>
        </button>
        <button (click)="onSignOut()" mat-menu-item>
          <mat-icon class="material-symbols-outlined">logout</mat-icon>
          <span>Logout</span>
        </button>
      </mat-menu>
    </div>
  </header>
</form>