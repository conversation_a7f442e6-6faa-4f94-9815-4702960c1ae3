html {
    --sys-primary: var(--blue-80);
    --sys-on-primary: rgb(255 255 255);

    --sys-secondary: #000000;
    --sys-on-secondary: rgb(255 255 255);

    --sys-tertiary: var(--orange-80);
    --sys-on-tertiary: rgb(255 255 255);

    --sys-error: var(--red-80);
    --sys-on-error: rgb(255 255 255);

    // --sys-primary-container: var(--blue-30);
    // --sys-on-primary-container: #000000;

    // --sys-secondary-container: rgb(216 227 248);
    // --sys-on-secondary-container: rgb(17 28 43);

    // --sys-tertiary-container: #FBDFB5;
    // --sys-on-tertiary-container: #000000;

    --success-container: var(--green-40);
    --on-success-container: #000000;

    --sys-error-container: var(--red-30);
    --sys-on-error-container: #000000;

    --warn-container: var(--orange-40);
    --on-warn-container: #000000;

    --sys-background: rgb(255, 255, 255);
    --sys-on-background: rgb(0, 0, 0);

    --sys-surface: rgb(255, 255, 255);
    --sys-on-surface: var(--grey-100);

    --sys-outline: var(--grey-30);
    --sys-outline-variant: #E9E9E9;

    --clr-disabled: var(--grey-10);
    --clr-on-disabled: var(--grey-100);

    --clr-disabled-icon: var(--grey-30);
    --clr-placeholder: #40454E;
}

.th-default {
  --theme-05: var(--blue-05);
  --theme-10: var(--blue-10);
  --theme-20: var(--blue-20);
  --theme-30: var(--blue-30);
  --theme-40: var(--blue-40);
  --theme-50: var(--blue-50);
  --theme-60: var(--blue-60);
  --theme-70: var(--blue-70);
  --theme-80: var(--blue-80);
  --theme-90: var(--blue-90);
  --theme-99:var(--grey-10)
  --headertheme-10: var(--darkblue-10);
  --headertheme-20: var(--darkblue-20);

  --sidebar-bg: var(--sidebar-bg-default);
  --sidebar-fg: #11074e;
  --logo-bg: var(--sidebar-bg-default);
  --active-menu-bg: var(--blue-90);
  --active-menu-fg:white;
  --active-submenu-bg: var(--blue-10);
}
