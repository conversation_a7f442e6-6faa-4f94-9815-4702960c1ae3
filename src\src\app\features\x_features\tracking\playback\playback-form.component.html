@if (!showForm()) {
<div class=" card">
    <div class="component">


        <div class="page-header">
            <h1>{{'PlayBack' | translate }}</h1>
        </div>

        <div class="content1">

            <form [formGroup]="form" (ngSubmit)="onSubmit()">
                <div class="form">

                    <div class="field">
                        <mat-radio-group aria-labelledby="example-radio-group-label" class="example-radio-group"
                            formControlName="range">
                            <mat-radio-button class="example-radio-button" [value]="1" checked>By
                                Date</mat-radio-button>
                            <mat-radio-button class="example-radio-button" [value]="2">By Range</mat-radio-button>
                        </mat-radio-group>
                    </div>

                    <div class="field">
                        <label for="vehicleId" class="">{{'VehicleSearch' | translate}}</label>
                        <mat-form-field appearance="outline">
                            <mat-select id="vehicleId" formControlName="vehicleId">
                                <mat-form-field class="select-search hide-subscript" appearance="outline">
                                    <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                    <input #search autocomplete="off" matInput placeholder="Search"
                                        (input)="filterVehicles($event)">
                                </mat-form-field>
                                @for (role of filteredvehicle(); track role) {
                                <mat-option [value]="role.key">{{role.value}}</mat-option>
                                }
                            </mat-select>
                            <!-- <mat-error>
                                @if(form.controls.vehicleId.errors?.['required']) {
                                {{'Region' | translate}} {{'IsRequired' | translate}}
                                }
                            </mat-error> -->
                        </mat-form-field>
                    </div>

                    @if (form.value.range ==1){
                    <div class="field">
                        <label for="Date" class="required-label">{{'Date' | translate
                            }}</label>
                        <mat-form-field>
                            <input matInput appDateInput [matDatepicker]="picker3" formControlName="Date">
                            <mat-datepicker-toggle matIconSuffix [for]="picker3"></mat-datepicker-toggle>
                            <mat-datepicker #picker3></mat-datepicker>
                            <mat-error>
                                @if(form.controls.Date.errors?.['required']) {
                                <!-- Date of appointment is required -->
                                {{'Date' | translate}} {{'IsRequired' | translate}}
                                }
                            </mat-error>
                        </mat-form-field>
                    </div>
                    }

                    

                    <div class="field">
                        <label for="regionId" class="required-label">{{'Region' | translate }}</label>
                        <mat-form-field appearance="outline">
                            <mat-select id="regionId" formControlName="regionId">
                                <!-- <mat-option value="null" disabled selected>Select Region</mat-option> -->
                                @for (role of regions(); track role) {
                                <mat-option [value]="role.key">{{role.value}}</mat-option>
                                }
                            </mat-select>
                            <mat-error>
                                @if(form.controls.regionId.errors?.['required']) {
                                <!-- Region is required -->
                                {{'Region' | translate}} {{'IsRequired' | translate}}
                                }
                            </mat-error>
                        </mat-form-field>
                    </div>

                    <!-- <div class="field">
                        <label for="vehicleId" class="required-label">{{'Vehicle Search' | translate }}</label>
                        <mat-form-field appearance="outline">
                            <mat-select id="vehicleId" formControlName="vehicleId">
                                @for (role of vehicle(); track role) {
                                <mat-option [value]="role.key">{{role.value}}</mat-option>
                                }
                            </mat-select>
                            <mat-error>
                                @if(form.controls.vehicleId.errors?.['required']) {
                                {{'Vehicle' | translate}} {{'IsRequired' | translate}}
                                }
                            </mat-error>
                        </mat-form-field>
                    </div> -->

                    


                    <div class="field">
                        <label for="vehicleIdList" class="required-label">{{'Vehicle' | translate }}</label>

                        <mat-form-field class="no-icon-prefix" formControlName="vehicleIdList">
                            <mat-select id="multi-select" multiple>
                                @for (role of vehicle(); track role) {
                                <mat-option [value]="role.key">{{role.value}}</mat-option>
                                }
                            </mat-select>
                        </mat-form-field>
                    </div>


                    @if (form.value.range ==2){
                    <div class="field">
                        <label for="fromDate" class="required-label">{{'FromDate' | translate
                            }}</label>
                        <mat-form-field>
                            <input matInput appDateInput [matDatepicker]="picker3" formControlName="fromDate">
                            <mat-datepicker-toggle matIconSuffix [for]="picker3"></mat-datepicker-toggle>
                            <mat-datepicker #picker3></mat-datepicker>
                            <mat-error>
                                @if(form.controls.fromDate.errors?.['required']) {
                                <!-- Date of appointment is required -->
                                {{'FromDate' | translate}} {{'IsRequired' | translate}}
                                }
                            </mat-error>
                        </mat-form-field>
                    </div>

                    <div class="field">
                        <label for="toDate" class="required-label">{{'ToDate' | translate }}</label>
                        <mat-form-field>
                            <input matInput appDateInput [matDatepicker]="picker4" formControlName="toDate"
                                [min]="form.value.fromDate">
                            <mat-datepicker-toggle matIconSuffix [for]="picker4"></mat-datepicker-toggle>
                            <mat-datepicker #picker4></mat-datepicker>
                            <mat-error>
                                @if(form.controls.toDate.errors?.['required']) {
                                <!-- To date is required -->
                                {{'ToDate' | translate}} {{'IsRequired' | translate}}
                                }
                            </mat-error>
                        </mat-form-field>
                    </div>
                    }



                </div>

                <div class="actions">
                    <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
                        translate}}</button>
                    <button mat-flat-button class="btn btn-theme" type="submit">{{'Submit' | translate}}</button>
                </div>

            </form>



        </div>
    </div>
</div>


}
@else {
<app-playback [vehicleIdList]="this.form.value.vehicleIdList" (closed)="onClose()"></app-playback>
}