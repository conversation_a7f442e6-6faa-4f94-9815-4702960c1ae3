import { Component, computed, signal } from '@angular/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import {provideNativeDateAdapter} from '@angular/material/core';
import {DateInputDirective} from '../../../directives/date-input.directive';
import {CurrencyFormatterDirective} from '../../../directives/currency-formatter.directive';
import {OnlyNumbersDirective} from '../../../directives/only-numbers.directive';
import {PhoneInputDirective} from '../../../directives/phone-input.directive';

@Component({
  selector: 'app-form-components',
  imports: [MatFormFieldModule, MatInputModule, MatSelectModule, MatCheckboxModule, MatRadioModule, MatSlideToggleModule, MatIconModule, CurrencyFormatterDirective, ReactiveFormsModule, MatDatepickerModule, DateInputDirective, OnlyNumbersDirective, PhoneInputDirective],
  templateUrl: './form-components.component.html',
  providers: [provideNativeDateAdapter()],
  styleUrl: './form-components.component.scss'
})
export class FormComponentsComponent {
  task = signal<any>({
    name: 'Parent task',
    completed: false,
    subtasks: [
      {name: 'Child task 1', completed: false},
      {name: 'Child task 2', completed: false},
      {name: 'Child task 3', completed: false},
    ],
  });
  seasons: string[] = ['Winter', 'Spring', 'Summer', 'Autumn'];
  amount = new FormControl("")
  number = new FormControl("")
  mobile = new FormControl("")

  readonly partiallyComplete = computed(() => {
    const task = this.task();
    if (!task.subtasks) {
      return false;
    }
    return task.subtasks.some((t: any) => t.completed) && !task.subtasks.every((t: any) => t.completed);
  });

  update(completed: boolean, index?: number) {
    this.task.update(task => {
      if (index === undefined) {
        task.completed = completed;
        task.subtasks?.forEach((t: any) => (t.completed = completed));
      } else {
        task.subtasks![index].completed = completed;
        task.completed = task.subtasks?.every((t: any) => t.completed) ?? true;
      }
      return {...task};
    });
  }
}
