// Variables
$primary-color: #203664;
$accent-color: #203664;
$success-color: #4caf50;
$error-color: #f44336;
$warning-color: #ff9800;
$text-primary: rgba(0, 0, 0, 0.87);
$text-secondary: rgba(0, 0, 0, 0.6);
$background-color: #f5f5f5;
$card-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
$section-spacing: 2rem;
$border-radius: 8px;

// Container and Card Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #f5f5f5 0%, #fbf9fc 100%);
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

.form-card {
  max-width: 900px;
  margin: 0 auto;
  box-shadow: $card-shadow;
  border-radius: $border-radius;
  background: white;
  overflow: hidden;
  
  .card-header {
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
    color: white;
    padding: 2rem;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $accent-color, $primary-color);
    }
    
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 0.5rem;
      
      .header-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
      }
      
      h1 {
        margin: 0;
        font-size: 2rem;
        font-weight: 300;
        letter-spacing: 0.5px;
      }
    }
    
    mat-card-subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 1rem;
      margin-left: 3rem;
    }
  }
  
  .progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
  }
  
  .card-content {
    padding: 2rem;
    
    @media (max-width: 768px) {
      padding: 1rem;
    }
  }
}

// Form Styles
.form-container {
  .section {
    margin-bottom: $section-spacing;
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
      color: $text-primary;
      font-size: 1.25rem;
      font-weight: 500;
      
      mat-icon {
        color: $primary-color;
      }
    }
    
    mat-divider {
      margin-bottom: 1.5rem;
      background: linear-gradient(90deg, $primary-color, transparent);
      height: 2px;
    }
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .full-width {
      grid-column: 1 / -1;
    }
  }
  
  // Material Form Field Customization
  mat-form-field {
    width: 100%;
    
    &.mat-form-field-appearance-outline {
      .mat-form-field-outline-thick {
        color: $primary-color;
      }
      
      &.mat-focused .mat-form-field-outline-thick {
        color: $primary-color;
      }
      
      .mat-form-field-label {
        color: $text-secondary;
      }
      
      &.mat-focused .mat-form-field-label {
        color: $primary-color;
      }
    }
    
    mat-icon[matSuffix] {
      color: $text-secondary;
      transition: color 0.3s ease;
    }
    
    &.mat-focused mat-icon[matSuffix] {
      color: $primary-color;
    }
    
    mat-error {
      font-size: 0.875rem;
      color: $error-color;
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }
  }
  
  // Input Styles
  input[matInput] {
    font-size: 1rem;
    transition: all 0.3s ease;
    
    &:focus {
      outline: none;
    }
  }
  
  // Select Styles
  mat-select {
    font-size: 1rem;
    
    .mat-select-trigger {
      display: flex;
      align-items: center;
    }
  }
}

// File Upload Section
.file-upload-section {
  .file-upload-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    
    .upload-button {
      align-self: flex-start;
      padding: 0.75rem 1.5rem;
      border: 2px dashed $primary-color;
      border-radius: $border-radius;
      background: rgba($primary-color, 0.05);
      color: $primary-color;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba($primary-color, 0.1);
        border-color: darken($primary-color, 10%);
        transform: translateY(-2px);
      }
      
      mat-icon {
        margin-right: 0.5rem;
      }
    }
    
    .file-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem;
      background: rgba($success-color, 0.1);
      border: 1px solid rgba($success-color, 0.3);
      border-radius: $border-radius;
      
      .file-icon {
        color: $success-color;
      }
      
      .file-name {
        flex: 1;
        font-size: 0.875rem;
        color: $text-primary;
        font-weight: 500;
      }
      
      .remove-file-btn {
        color: $error-color;
        transition: transform 0.2s ease;
        
        &:hover {
          transform: scale(1.1);
          background: rgba($error-color, 0.1);
        }
      }
    }
  }
  
  .file-requirements {
    .requirement-text {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: $text-secondary;
      font-size: 0.75rem;
      
      .info-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
        color: $primary-color;
      }
    }
  }
}

// Button Section
.button-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  
  @media (max-width: 768px) {
    flex-direction: column;
    
    .reset-button,
    .submit-button {
      width: 100%;
    }
  }
  
  .reset-button {
    padding: 0.75rem 2rem;
    border: 1px solid $text-secondary;
    color: $text-secondary;
    border-radius: $border-radius;
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) {
      border-color: $primary-color;
      color: $primary-color;
      background: rgba($primary-color, 0.05);
      transform: translateY(-2px);
    }
    
    mat-icon {
      margin-right: 0.5rem;
    }
  }
  
  .submit-button {
    padding: 0.75rem 2.5rem;
    border-radius: $border-radius;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba($primary-color, 0.3);
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba($primary-color, 0.4);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    mat-icon {
      margin-right: 0.5rem;
      
      &.loading-icon {
        animation: spin 1s linear infinite;
      }
    }
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Form Animation
.form-container {
  animation: fadeIn 0.6s ease-out;
}

.section {
  animation: fadeIn 0.8s ease-out;
  
  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
  &:nth-child(5) { animation-delay: 0.4s; }
  &:nth-child(6) { animation-delay: 0.5s; }
}

// Snackbar Customization
::ng-deep {
  .success-snackbar {
    background: $success-color !important;
    color: white !important;
    
    .mat-simple-snackbar-action {
      color: white !important;
    }
  }
  
  .error-snackbar {
    background: $error-color !important;
    color: white !important;
    
    .mat-simple-snackbar-action {
      color: white !important;
    }
  }
  
  // Custom Mat-Select Panel
  .mat-select-panel {
    border-radius: $border-radius;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    
    .mat-option {
      padding: 1rem 1.5rem;
      transition: background-color 0.2s ease;
      
      &:hover {
        background: rgba($primary-color, 0.08);
      }
      
      &.mat-selected {
        background: rgba($primary-color, 0.12);
        color: $primary-color;
      }
    }
  }
  
  // Custom Datepicker
  .mat-datepicker-popup {
    .mat-calendar {
      border-radius: $border-radius;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    
    .mat-calendar-body-selected {
      background-color: $primary-color;
    }
    
    .mat-calendar-body-today:not(.mat-calendar-body-selected) {
      border-color: $primary-color;
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .container {
    padding: 1.5rem 1rem;
  }
  
  .form-card .card-header {
    padding: 1.5rem;
    
    mat-card-title h1 {
      font-size: 1.75rem;
    }
  }
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr !important;
  }
  
  .form-card .card-header {
    mat-card-title {
      flex-direction: column;
      text-align: center;
      gap: 0.5rem;
      
      h1 {
        font-size: 1.5rem;
      }
    }
    
    mat-card-subtitle {
      margin-left: 0;
      text-align: center;
    }
  }
  
  .section .section-title {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem 0.5rem;
  }
  
  .form-card .card-content {
    padding: 1rem;
  }
  
  .button-section {
    gap: 0.75rem;
  }
  
  .reset-button,
  .submit-button {
    padding: 0.625rem 1.5rem;
    font-size: 0.875rem;
  }
}

// Focus and Accessibility
mat-form-field.mat-focused {
  .mat-form-field-outline-thick {
    color: $primary-color;
  }
}

button:focus-visible {
  outline: 2px solid $primary-color;
  outline-offset: 2px;
}

input:focus-visible,
mat-select:focus-visible {
  outline: none;
}

// Print Styles
@media print {
  .container {
    background: white;
    box-shadow: none;
  }
  
  .form-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .button-section {
    display: none;
  }
  
  .file-upload-section {
    display: none;
  }
}