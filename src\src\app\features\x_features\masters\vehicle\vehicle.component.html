<mat-toolbar color="primary" class="navbar">
    <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
      <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
      <mat-icon>chevron_right</mat-icon>
      <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
      <mat-icon>chevron_right</mat-icon>
      <span aria-current="page" class="nav-active">
        @if(uinqueId() == 0) {
        {{'New' | translate}} {{menuService.activeMenu()?.title}}
        } @else {
            {{vehicle()?.regNo}}
        }
      </span>
      @if(mode() === "view") {
      <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
        <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
      </button>
      }
    </nav>
    <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
      <mat-icon>arrow_back_ios</mat-icon>
      Back
    </button>
  </mat-toolbar>

    <div class="card component">
     
        <form [formGroup]="form" (ngSubmit)="onSubmit()">
            <div class="form">

                <div class="field">
                    <label for="regNo" class="required-label">{{'Reg. No.' | translate}}</label>
                    <mat-form-field>
                        <input id="regNo" formControlName="regNo" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
                            maxlength="100">
                        <mat-error>
                            @if(form.controls.regNo.errors?.['required']) {
                            {{'Reg. No.' | translate}} {{"IsRequired" | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="ownerName" class="required-label">{{'Owner Name' | translate}}</label>
                    <mat-form-field>
                        <input id="ownerName" formControlName="ownerName" matInput
                            (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
                        <mat-error>
                            @if(form.controls.ownerName.errors?.['required']) {
                            {{'Owner Name' | translate}} {{"IsRequired" | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="ownerMobileNo" class="required-label">{{'Owner Mobile No.' | translate}}</label>
                    <mat-form-field>
                        <input id="ownerMobileNo" formControlName="ownerMobileNo" matInput
                            (input)="onInput($event,'/^[0-9.]*$/')" maxlength="10">
                        <mat-error>
                            @if(form.controls.ownerMobileNo.errors?.['required']) {
                            {{'Owner Mobile No.' | translate}} {{"IsRequired" | translate}}
                            }
                            @else if(form.controls.ownerMobileNo.errors?.['pattern']){
                            {{'InvalidFormat' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="vendorId" class="required-label">{{"Vendor" | translate}}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="vendorId" formControlName="vendorId">
                            <!-- <mat-option value="null" disabled selected>Select Delta Type</mat-option> -->
                            @for (role of vendor(); track role) {
                            <mat-option [value]="role.key">{{role.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.vendorId.errors?.['required']) {
                            {{"Vendor" | translate}} {{"IsRequired" | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>


                <div class="field">
                    <label for="contractorId" class="required-label">{{"Contractor" | translate}}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="contractorId" formControlName="contractorId">
                            <!-- <mat-option value="null" disabled selected>Select Delta Type</mat-option> -->
                            @for (role of vendor(); track role) {
                            <mat-option [value]="role.key">{{role.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.contractorId.errors?.['required']) {
                            {{"Contractor" | translate}} {{"IsRequired" | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

            </div>

            <div class="actions">
               
                @if(editable() === true) {
                @if(mode() === 'add') {
                <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
                    translate}}</button>
                }
                <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme"
                    type="submit">{{uinqueId() == 0
                    ? ('Create' | translate) : ('Update' | translate)}}</button>
                }
            </div>


        </form>
    </div>
