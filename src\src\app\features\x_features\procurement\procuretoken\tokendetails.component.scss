.grid-container {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1rem;
  }

  .gauge-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  ngx-gauge {
    width: 300px;
    height: 200px;
    margin: 20px;
  }

  .row-12 {
    grid-column: span 12;
  }

  .row-8 {
    grid-column: span 8;
  }

  .row-4 {
    grid-column: span 4;
  }

  .row-6 {
    grid-column: span 6;
  }

  .row-3 {
    grid-column: span 3;
  }

  @media (max-width: 768px) {
    .grid-container {
      grid-template-columns: repeat(12, 1fr);
    }

    .row-8 {
      grid-column: span 12;
      text-align: left;
    }

    .row-4 {
      grid-column: span 12;
    }
    .row-3 {
        grid-column: span 12;
      }

    .row-6 {
      grid-column: span 12;
    }

    .actions {
      justify-content: flex-start;
    }

    .card-container {
      grid-template-columns: 1fr;
    }
  }

  .value {
    padding-left: 20px;
    color: #123483;
    font-size: 16px;
    padding-bottom: 20px;
  }

  .label {
    color: black;
    font-size: 14px;
    padding-bottom: 20px;
  }

  .card1 {
    background-color: #fff;
    box-shadow: 0 0 10px 0 rgb(36 109 173 / 20%);
    border-radius: var(--4px);
    padding: 20px;
    margin-top: 20px;
  }

  .land {
    color: #292b83;
    /* padding-left: 20px; */
    /* color: #2e66e9; */
    font-size: 20px;
    padding-bottom: 10px;
  }