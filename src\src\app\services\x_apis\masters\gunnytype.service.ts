import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Gunnies, GunnyById } from '../../../models/x_models/masters/gunnytype';

@Injectable({
  providedIn: 'root'
})
export class GunnytypeService {

  dataService = inject(DataService)

  create(data:any) {
      return this.dataService.post<Response>("/gunnytypedetail", data)
  }

  get() {
      return this.dataService.get<Gunnies>("/gunnytypedetail")
  }

  getById(id: number) {
      return this.dataService.get<GunnyById>(`/gunnytypedetail/${id}`)
  }

  delete(id: number) {
      return this.dataService.delete<Response>(`/gunnytypedetail/${id}`)
  }

}
