<div class="card">
  <div class="field">
    <label for="textInput">Text Input</label>
    <mat-form-field>
      <input id="textInput" matInput placeholder="Placeholder">
    </mat-form-field>
  </div>

  <div class="field">
    <label for="select">Select</label>
    <mat-form-field class="no-icon-prefix">
      <mat-select id="select">
        <mat-option value="" disabled>Select an item</mat-option>
        <mat-option value="1">Option 1</mat-option>
        <mat-option value="2">Option 2</mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div class="field">
    <label for="multi-select">Multi Select</label>
    <mat-form-field class="no-icon-prefix">
      <mat-select id="multi-select" multiple>
        <mat-option value="" disabled>Select an item</mat-option>
        <mat-option value="1">Option 1</mat-option>
        <mat-option value="2">Option 2</mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div class="field">
    <label for="select-search">Select with search</label>
    <mat-form-field class="no-icon-prefix">
      <mat-select id="select-search">
        <mat-form-field class="select-search hide-subscript" appearance="outline">
          <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
          <input #search autocomplete="off" matInput placeholder="Search">
        </mat-form-field>
        <mat-option value="" disabled>Select an item</mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <!-- checkboxes Start-->
   <div class="field">
    <mat-checkbox class="example-margin">Check me!</mat-checkbox>
    <mat-checkbox class="example-margin" [disabled]="true">Disabled</mat-checkbox>
   
    <section class="example-section">
      <span class="example-list-section">
        <mat-checkbox
          class="example-margin"
          [checked]="task().completed"
          [indeterminate]="partiallyComplete()"
          (change)="update($event.checked)"
        >
          {{task().name}}
        </mat-checkbox>
      </span>
      <span class="example-list-section">
        <ul>
          @for (subtask of task().subtasks; track subtask; let i = $index) {
            <mat-checkbox [checked]="subtask.completed" (change)="update($event.checked, i)">
              {{subtask.name}}
            </mat-checkbox>
          }
        </ul>
      </span>
    </section>
   </div>
  <!-- checkboxes End -->

  <div class="field">
    <label for="number">Number</label>
    <mat-form-field appearance="outline">
      <input appCurrencyFormatter [formControl]="number" id="number" autocomplete="off" matInput placeholder="">
    </mat-form-field>
  </div>

  <div class="field">
    <label for="mobile">Mobile</label>
    <mat-form-field appearance="outline">
      <input appPhoneInput [formControl]="mobile" id="mobile" autocomplete="off" matInput placeholder="">
    </mat-form-field>
  </div>

  <div class="field">
    <label for="amount">Amount</label>
    <mat-form-field appearance="outline">
      <input appCurrencyFormatter [formControl]="amount" id="amount" autocomplete="off" matInput placeholder="0.00">
      <span matTextPrefix>₹</span>
    </mat-form-field>
  </div>

  <div class="field">
    <label id="example-radio-group-label">Pick your favorite season</label>
    <mat-radio-group
      aria-labelledby="example-radio-group-label"
      class="example-radio-group"
      >
      @for (season of seasons; track season) {
        <mat-radio-button class="example-radio-button" [value]="season">{{season}}</mat-radio-button>
      }
    </mat-radio-group>
  </div>

  <mat-form-field>
    <input matInput appDateInput [matDatepicker]="picker1" placeholder="Date">
    <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
    <mat-datepicker #picker1></mat-datepicker>
  </mat-form-field>

  <mat-form-field>
    <mat-date-range-input [rangePicker]="picker2">
      <input appDateInput matStartDate placeholder="Start date">
      <input appDateInput matEndDate placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
    <mat-date-range-picker #picker2></mat-date-range-picker>
  </mat-form-field>

  <mat-slide-toggle>Receive card statement</mat-slide-toggle>
</div>