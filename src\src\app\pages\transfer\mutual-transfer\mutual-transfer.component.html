<div class="container">
  <mat-card class="form-card">
    <mat-card-header class="card-header">
      <mat-card-title>
        <mat-icon class="header-icon">swap_horizontal_circle</mat-icon>
        <h1>Mutual Transfer Request</h1>
      </mat-card-title>
      <mat-card-subtitle>
        Request a mutual transfer between two employees in different locations
      </mat-card-subtitle>
    </mat-card-header>

    <mat-progress-bar
      *ngIf="isLoading"
      mode="indeterminate"
      class="progress-bar">
    </mat-progress-bar>

    <mat-card-content class="card-content">
      <form [formGroup]="transferForm" (ngSubmit)="onSubmit()" class="form-container transfer-form">

        <!-- Current Employee Information Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>person</mat-icon>
            Your Information (Employee A)
          </h3>
          <mat-divider></mat-divider>

          <div class="form-grid">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Your Name</mat-label>
              <input matInput formControlName="employeeAName" readonly>
              <mat-icon matSuffix>person</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Your Employee Code</mat-label>
              <input matInput formControlName="employeeACode" readonly>
              <mat-icon matSuffix>badge</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Your Current Region</mat-label>
              <input matInput formControlName="employeeARegion" readonly>
              <mat-icon matSuffix>location_on</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Your Current Department</mat-label>
              <input matInput formControlName="employeeADepartment" readonly>
              <mat-icon matSuffix>business</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Your Current Position</mat-label>
              <input matInput formControlName="employeeAPosition" readonly>
              <mat-icon matSuffix>work</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Your Current Grade</mat-label>
              <input matInput formControlName="employeeAGrade" readonly>
              <mat-icon matSuffix>grade</mat-icon>
            </mat-form-field>
          </div>
        </div>

        <!-- Mutual Employee Selection Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>people</mat-icon>
            Select Mutual Transfer Partner (Employee B)
          </h3>
          <mat-divider></mat-divider>

          <div class="search-section">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Search Employee</mat-label>
              <input matInput
                     formControlName="employeeSearch"
                     placeholder="Search by name, employee code, or region"
                     (input)="onEmployeeSearch($event)">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
          </div>

          <div class="form-grid">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Filter by Region</mat-label>
              <mat-select formControlName="filterRegion" (selectionChange)="onRegionFilter($event)">
                <mat-option value="">All Regions</mat-option>
                <mat-option *ngFor="let region of regions" [value]="region.id">
                  {{ region.name }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>filter_list</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Filter by Department</mat-label>
              <mat-select formControlName="filterDepartment" (selectionChange)="onDepartmentFilter($event)">
                <mat-option value="">All Departments</mat-option>
                <mat-option *ngFor="let dept of departments" [value]="dept.id">
                  {{ dept.name }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>filter_list</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Select Employee B <span class="required-asterisk">*</span></mat-label>
              <mat-select formControlName="employeeB" (selectionChange)="onEmployeeBSelect($event)">
                <mat-option *ngFor="let emp of filteredEmployees" [value]="emp.id">
                  <div class="employee-option">
                    <div class="employee-main">
                      <strong>{{ emp.name }}</strong> ({{ emp.employeeCode }})
                    </div>
                    <div class="employee-details">
                      {{ emp.position }} | {{ emp.department }} | {{ getRegionName(emp.region) }}
                    </div>
                  </div>
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>person_add</mat-icon>
              <mat-error *ngIf="transferForm.get('employeeB')?.invalid && transferForm.get('employeeB')?.touched">
                Please select an employee for mutual transfer
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Selected Employee B Information -->
        <div class="section" *ngIf="selectedEmployeeB">
          <h3 class="section-title">
            <mat-icon>person_outline</mat-icon>
            Selected Partner Information (Employee B)
          </h3>
          <mat-divider></mat-divider>

          <div class="employee-info-card">
            <div class="form-grid">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Partner Name</mat-label>
                <input matInput [value]="selectedEmployeeB.name" readonly>
                <mat-icon matSuffix>person</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Partner Employee Code</mat-label>
                <input matInput [value]="selectedEmployeeB.employeeCode" readonly>
                <mat-icon matSuffix>badge</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Partner Current Region</mat-label>
                <input matInput [value]="getRegionName(selectedEmployeeB.region)" readonly>
                <mat-icon matSuffix>location_on</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Partner Current Department</mat-label>
                <input matInput [value]="getDepartmentName(selectedEmployeeB.department)" readonly>
                <mat-icon matSuffix>business</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Partner Current Position</mat-label>
                <input matInput [value]="selectedEmployeeB.position" readonly>
                <mat-icon matSuffix>work</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Partner Current Grade</mat-label>
                <input matInput [value]="selectedEmployeeB.currentGrade" readonly>
                <mat-icon matSuffix>grade</mat-icon>
              </mat-form-field>
            </div>
          </div>
        </div>

        <!-- Transfer Details Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>assignment</mat-icon>
            Transfer Request Details
          </h3>
          <mat-divider></mat-divider>

          <div class="form-grid">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Priority Level <span class="required-asterisk">*</span></mat-label>
              <mat-select formControlName="priority">
                <mat-option value="LOW">
                  <span class="priority-option low">Low</span>
                </mat-option>
                <mat-option value="MEDIUM">
                  <span class="priority-option medium">Medium</span>
                </mat-option>
                <mat-option value="HIGH">
                  <span class="priority-option high">High</span>
                </mat-option>
                <mat-option value="URGENT">
                  <span class="priority-option urgent">Urgent</span>
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>priority_high</mat-icon>
              <mat-error *ngIf="transferForm.get('priority')?.invalid && transferForm.get('priority')?.touched">
                Please select priority level
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Preferred Joining Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="preferredJoiningDate" [min]="minDate">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
        </div>

        <!-- Reason and Agreement Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>description</mat-icon>
            Reason and Agreement
          </h3>
          <mat-divider></mat-divider>

          <div class="form-grid">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Reason for Mutual Transfer <span class="required-asterisk">*</span></mat-label>
              <textarea
                matInput
                formControlName="reason"
                rows="4">
              </textarea>
              <mat-icon matSuffix>description</mat-icon>
              <mat-hint>Minimum 10 characters required</mat-hint>
              <mat-error *ngIf="transferForm.get('reason')?.invalid && transferForm.get('reason')?.touched">
                {{getErrorMessage('reason')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Additional Justification</mat-label>
              <textarea
                matInput
                formControlName="justification"
                rows="3"
                placeholder="Any additional information that supports your mutual transfer request">
              </textarea>
              <mat-icon matSuffix>note_add</mat-icon>
            </mat-form-field>
          </div>

          <!-- Agreement Section -->
          <div class="agreement-section">
            <h4 class="agreement-title">
              <mat-icon>verified_user</mat-icon>
              Agreement Confirmation
            </h4>

            <div class="agreement-checkboxes">
              <mat-checkbox formControlName="agreementConfirmed" class="agreement-checkbox">
                <span class="checkbox-text">
                  I confirm that both employees have agreed to this mutual transfer and understand the terms and conditions
                </span>
              </mat-checkbox>

              <mat-checkbox formControlName="termsAccepted" class="agreement-checkbox">
                <span class="checkbox-text">
                  I accept the terms and conditions for mutual transfer as per company policy
                </span>
              </mat-checkbox>

              <mat-checkbox formControlName="documentationComplete" class="agreement-checkbox">
                <span class="checkbox-text">
                  I confirm that all required documentation will be provided upon approval
                </span>
              </mat-checkbox>
            </div>

            <div class="agreement-note">
              <mat-icon>info</mat-icon>
              <p>
                <strong>Note:</strong> This mutual transfer request requires approval from both employees' supervisors
                and HR departments. The selected partner employee will be notified and must confirm their agreement
                before the request can proceed.
              </p>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            mat-raised-button
            color="primary"
            type="submit"
            [disabled]="transferForm.invalid || isLoading || !allAgreementsConfirmed()"
            class="submit-btn">
            <mat-icon *ngIf="!isLoading">send</mat-icon>
            <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
            {{ isLoading ? 'Submitting...' : 'Submit Mutual Transfer Request' }}
          </button>

          <button
            mat-stroked-button
            color="accent"
            type="button"
            (click)="saveDraft()"
            [disabled]="isLoading"
            class="draft-btn">
            <mat-icon>save</mat-icon>
            Save as Draft
          </button>

          <button
            mat-button
            color="warn"
            type="button"
            (click)="resetForm()"
            [disabled]="isLoading"
            class="reset-btn">
            <mat-icon>refresh</mat-icon>
            Reset Form
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
