<h2>Mutual Transfer</h2>
<form [formGroup]="transferForm" (ngSubmit)="onSubmit()">
  <mat-form-field appearance="outline">
    <mat-label>Employee A Name & Position</mat-label>
    <mat-select formControlName="employeeA">
      <mat-option *ngFor="let emp of employees" [value]="emp.id">{{ emp.name }} - {{ emp.position }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Employee B Name & Position</mat-label>
    <mat-select formControlName="employeeB">
      <mat-option *ngFor="let emp of employees" [value]="emp.id">{{ emp.name }} - {{ emp.position }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-checkbox formControlName="agreementConfirmed">
    Both Employees Agree to Swap
  </mat-checkbox>

  <button mat-raised-button color="accent" type="submit">Submit</button>
</form>
