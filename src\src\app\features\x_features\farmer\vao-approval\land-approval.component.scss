.head{
    background-color: #094B8D;
    border-top-right-radius: 20px;
    border-top-left-radius: 20px;
    padding: 17px;
    color: white;
    font-size: larger;
    // font-weight: 600;
}

.container-land{
    display: grid;
    grid-template-columns: auto auto auto;
    padding:20px 20px 0px 20px;
    gap: 20px;
   @media (max-width: 768px) {
    grid-template-columns: auto;
   } 
}
.reject
{
    background-color: red !important;
    border: 1px solid red !important;
    color: white;
    padding: 5px 17px;
    border-radius: 3px;
    font-weight: 500;
}

.approve
{
    background-color: #094B8D !important;
    border: 1px solid #094B8D !important;
    color: white;
    padding: 5px 17px;
    border-radius: 3px;
    font-weight: 500;
}

.item-container{
    display: grid;
    grid-template-columns: auto auto;
}


.back{
background-color: #A68B63;
    border: 1px solid #A68B63;
    color: white;
    padding: 5px 17px;
    border-radius: 3px;
    font-weight: 500;
}

.sendback
{
    padding: 5px 10px 5px 10px;
    border-radius: 14px;
    background: #eda5a56b;
}

.approved{
    padding: 5px 10px 5px 10px;
    border-radius: 14px;
    background: #b0edb0ab;
}
.pending
{
    padding: 5px 10px 5px 10px;
    border-radius: 14px;
    background: #eed5a8a3;
}

.flex
{
    display: flex;
}
.width-50
{
    width: 50%;
}
.label
{
    color:dimgrey
}
.btn-icon1{
    color: black
}