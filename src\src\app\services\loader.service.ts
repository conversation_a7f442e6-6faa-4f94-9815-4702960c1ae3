import { computed, Injectable, signal } from '@angular/core';
import { BehaviorSubject, Observable, Subject, delay, map, of, shareReplay } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class LoaderService {

  #loaderSignal = signal<boolean>(false)
  loader = this.#loaderSignal.asReadonly();
  loadCount = signal<number>(0);

  constructor() { }

  start(source = '') {
    this.loadCount.set(this.loadCount() + 1)
    // console.log('stop---', this.loadCount, source);
    this.#loaderSignal.set(true)
  }

  startWithoutCount(source = '') {
    // console.log('stop---', this.loadCount, source);
    this.#loaderSignal.set(true)
  }
  
  stop(source = '') {
    this.loadCount.set(this.loadCount() - 1)
    // console.log('stop---', this.loadCount, source);
    if (this.loadCount() <= 0) {
      this.loadCount.set(0)
      this.#loaderSignal.set(false)
    }
  }
}
