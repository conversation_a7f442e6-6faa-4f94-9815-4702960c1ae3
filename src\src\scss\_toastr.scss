// @use 'ngx-toastr/toastr';

// .toast-container .ngx-toastr {
//   // background-position: 15px 14px;
//   // font-size: 14px;
//   // border-radius: 12px;
//   // box-shadow: 0px 6px 14px -6px rgba(19, 25, 39, 0.12), 0px 10px 32px -4px rgba(19, 25, 39, 0.10);
//   // background-image: none;
//   // height: fit-content;
//   // // padding: 15px 40px 15px 30px;
//   // margin-bottom: 50px;
//   // box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
//   // max-width: 450px;
//   // width: 100% !important;
// }

// .toast-container .ngx-toastr {
//   animation: fadeInUp 0.4s ease-out;
//   transition: all 0.3s ease-in-out;
//   box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
//   border-radius:10px;
// }

// .toast-warning {
//   background-color: #ffc107 !important;
//   color: white !important;
//   .toast-close-button span {
//     color: var(--orange-80);
//     text-shadow: 0 0px 0 var(--orange-80);
//   }
// }

// .toast-info {
//   background-color: #17a2b8 !important;
//   color: white !important;

//   .toast-close-button span {
//     color: var(--blue-80);
//     text-shadow: 0 0px 0 var(--orange-80);
//   }
// }

// .toast-error {
//   background-color: #dc3545 !important;
//   color: white !important;
//   .toast-close-button span {
//     color: var(--red-80);
//     text-shadow: 0 0px 0 var(--red-80);
//   }
// }

// .toast-success {
//   background-color: #28a745 !important;
//   color: white !important;
//   .toast-close-button span {
//     color: var(--theme-80);
//     text-shadow: 0 0px 0 var(--theme-80);
//   }
// }

// // .toast-close-button {
// //   top: 8px;
// //   right: 12px;
// //   opacity: 0.8;

// //   &:hover {
// //     opacity: 1;
// //     transform: scale(1.2);
// //     transition: transform 0.2s ease;
// //   }

// //   span {
// //     font-size: 18px !important;
// //     color: #fff !important;
// //   }
// // }


// .toast-title {
//   font-size: 16px;
//   font-weight: 600;
//   margin-bottom: 4px;
// }

// .toast-message {
//   font-size: 14px;
//   line-height: 1.4;
// }

// .toast-close-button {
//   position: absolute;
//   top: 0px;
//   // right: 8px;
//   font-weight: 100;
//   color: #fff;
//   height: 100%;
//   display: flex;
//   align-items: center;
//   padding-bottom: 4px !important;
//   right: 15px;

//   span {
//     scale: 1.5;
//     font-weight: 200;
//     font-size: 16px !important;
//   }
// }


// @media (prefers-color-scheme: dark) {
//   .toast-success,
//   .toast-error,
//   .toast-info,
//   .toast-warning {
//     background-color: rgba(255, 255, 255, 0.1) !important;
//     color: #fff !important;
//     border-left: 4px solid rgba(255, 255, 255, 0.3);
//   }
// }





// Old Css
@use 'ngx-toastr/toastr';

.toast-container .ngx-toastr {
  background-position: 15px 14px;
  font-size: 14px;
  border-radius: 12px;
  box-shadow: 0px 6px 14px -6px rgba(19, 25, 39, 0.12), 0px 10px 32px -4px rgba(19, 25, 39, 0.10);
  background-image: none;
  height: fit-content;
  padding: 15px 40px 15px 30px;
  margin-bottom: 50px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  max-width: 450px;
  width: 100% !important;
}

.toast-warning {
  // background-image: url("../../assets/zeus/toastr/circle-exclamation-solid.svg");
  background-color: var(--orange-30);
  color: black !important;
  // border-left: 6px solid #e9c141;

  .toast-close-button span {
    color: var(--orange-80);
    text-shadow: 0 0px 0 var(--orange-80);
  }
}

.toast-info {
  // background-image: url("../../assets/zeus/toastr/circle-info-solid.svg");
  background-color: #ecf0fc;
  color: black !important;

  // border-left: 6px solid #3664dd;
  .toast-close-button span {
    color: var(--blue-80);
    text-shadow: 0 0px 0 var(--orange-80);
  }
}

.toast-error {
  background-image: url("/assets/toastr/circle-xmark-solid.svg");
  background-color: rgb(179, 58, 58);
  color: rgb(244, 252, 244) !important;
  .toast-close-button span {
    color: white;
    // text-shadow: 0 0px 0 var(--theme-80);
  }
}

.toast-success {
  background-image: url("/assets/toastr/circle-check-solid.svg");
  background-color: rgb(63, 121, 63) !important;
  color: rgb(244, 252, 244) !important;
  .toast-close-button span {
    color: white;
    // text-shadow: 0 0px 0 var(--theme-80);
  }
}

.toast-title {
  font-weight: 600 !important;
}

.toast-close-button {
  position: absolute;
  top: 0px;
  // right: 8px;
  font-weight: 100;
  color: #fff;
  height: 100%;
  display: flex;
  align-items: center;
  padding-bottom: 4px !important;
  right: 15px;

  span {
    scale: 1.5;
    font-weight: 200;
    font-size: 16px !important;
  }
}