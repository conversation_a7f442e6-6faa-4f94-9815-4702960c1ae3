import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { DelayedDayCutById, DelayedDayCuts } from '../../../models/x_models/masters/delayeddaycut';

@Injectable({
  providedIn: 'root'
})
export class DelayeddaycutService {

  dataService = inject(DataService)

  create(data:any) {
      return this.dataService.post<Response>("/delayeddayscut", data)
  }

  get() {
      return this.dataService.get<DelayedDayCuts>("/delayeddayscut")
  }

  getById(id: number) {
      return this.dataService.get<DelayedDayCutById>(`/delayeddayscut/${id}`)
  }

  delete(id: number) {
      return this.dataService.delete<Response>(`/delayeddayscut/${id}`)
  }


}
