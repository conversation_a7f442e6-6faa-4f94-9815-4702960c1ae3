{"version": 3, "sources": ["../../../../../../node_modules/@ngx-translate/http-loader/fesm2022/ngx-translate-http-loader.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Inject } from '@angular/core';\nimport * as i1 from '@angular/common/http';\nclass TranslateHttpLoader {\n  http;\n  prefix;\n  suffix;\n  constructor(http, prefix = \"/assets/i18n/\", suffix = \".json\") {\n    this.http = http;\n    this.prefix = prefix;\n    this.suffix = suffix;\n  }\n  /**\n   * Gets the translations from the server\n   */\n  getTranslation(lang) {\n    return this.http.get(`${this.prefix}${lang}${this.suffix}`);\n  }\n  static ɵfac = function TranslateHttpLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslateHttpLoader)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(String), i0.ɵɵinject(String));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslateHttpLoader,\n    factory: TranslateHttpLoader.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateHttpLoader, [{\n    type: Injectable\n  }], () => [{\n    type: i1.HttpClient\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [String]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [String]\n    }]\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TranslateHttpLoader };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAGA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,MAAM,SAAS,iBAAiB,SAAS,SAAS;AAC5D,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,MAAM;AACnB,WAAO,KAAK,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,IAAI,GAAG,KAAK,MAAM,EAAE;AAAA,EAC5D;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAwB,SAAY,UAAU,GAAM,SAAS,MAAM,GAAM,SAAS,MAAM,CAAC;AAAA,EAC5H;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;", "names": []}