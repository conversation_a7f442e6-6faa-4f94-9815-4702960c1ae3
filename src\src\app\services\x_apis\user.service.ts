import {inject, Injectable} from '@angular/core';
import { DataService } from '../data.service';
import { Response } from '../../models/x_models/api-response';
import { CreateUserPayload, DeleteUserPayload, UserByIdResponse, UserResponse } from '../../models/x_models/user';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  dataService = inject(DataService)

  create(data: CreateUserPayload) {
    return this.dataService.post<Response>("/user", data)
  }

  get() {
    return this.dataService.get<UserResponse>("/user")
  }

  getById(id: number) {
    return this.dataService.get<UserByIdResponse>(`/user/${id}`)
  }

  update(data: any) {
    return this.dataService.put<any>("/user", data)
  }

  delete(data: DeleteUserPayload) {
    return this.dataService.delete<Response>("/user", data)
  }
}
