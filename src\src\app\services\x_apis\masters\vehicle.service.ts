import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Vehicle, VehicleResponse } from '../../../models/x_models/masters/vehicle';
import { LookUpResponse } from '../../../models/x_models/lookup';

@Injectable({
  providedIn: 'root'
})
export class VehicleService {

  dataService = inject(DataService)

  create(data: Vehicle) {
    return this.dataService.post<Response>("/vehicle", data)
  }

  get() {
    return this.dataService.get<VehicleResponse>("/vehicle")
  }

  getById(id: number) {
    return this.dataService.get<Vehicle>(`/vehicle/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/vehicle/${id}`)
  }

  lookup(regionId : number ){
    return  this.dataService.get<LookUpResponse[]>(`/vehicle/lookup/${regionId}`);
  }

}
