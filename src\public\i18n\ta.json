{"Region": "மாவட்டம்", "Unit": "அலகு அலுவலகம்", "Taluk": "தாலுகா", "Block": "தொகுதி", "Village": "கிராமம்", "Season": "பருவம்", "Crop": "பயிர்", "FromDate": "தேதியிலிருந்து", "ToDate": "இன்றுவரை", "Back": "மீண்டும்", "Reset": "மீட்டமை", "Create": "உருவாக்கு", "Update": "மேம்படுத்தல்", "Actions": "செயல்கள்", "Search": "தேடல்", "Master": "முதன்மைப் பக்கம்", "View": "பார்வை", "Edit": "திருத்து", "Delete": "நீக்கவும்", "New": "புதியது", "InTamil": "தமிழில்", "LGDCode": "உள்ளூர் அரசாங்க அடைவு குறியீடு", "DeltaType": "டெல்டா வகை", "Hulling": "அரவை", "Code": "குறியீடு", "Agency": "நிறுவனம்", "HullingCode": "அரவை குறியீடு", "MillName": "ஆலை பெயர்", "MillAddress": "ஆலை முகவரி", "MillPincode": "மில் பின்கோடு", "MillLatitude": "ஆலையின் அட்சரேகை", "MillLongitude": "மில்லின் தீர்க்கரேகை", "MillLandmark": "ஆலையின் அடையாளச் சின்னம்", "MillerName": "ஆலை உரிமையாளர் பெயர்", "MillerPresentAddress": "ஆலையின் தற்போதைய முகவரி", "MillType": "ஆலை வகை", "FactoryLicenseNo": "தொழிற்சாலை உரிம எண்", "FactoryLicenseUpto": "தொழிற்சாலை உரிமம் வரை", "BoilerLicenseNo": "பாய்லர் உரிம எண்", "BoilerLicenseUpto": "பாய்லர் உரிமம் வரை", "DateofAppointment": "நியமன தேதி", "AgreementValidFrom": "ஒப்பந்தம் செல்லுபடியாகும் தேதியிலிருந்து", "AgreementValidTo": "ஒப்பந்தம் செல்லுபடியாகும்", "MRMType": "எம்ஆர்எம் வகை", "GSTNo": "ஜிஎஸ்டி எண்.", "PANNo": "பான் எண்", "PaddyType": "நெல் வகை", "FortifiedType": "வலுவூட்டப்பட்ட வகை", "IsGeneratorAvailable?": "ஜெனரேட்டர் இருக்கிறதா இல்லையா?", "GeneratorCapacity": "ஜெனரேட்டர் கொள்ளளவு", "GeneratorModelNo": "ஜெனரேட்டர் மாதிரி எண்", "IsSeparateEBMeter?": "தனியாக மின்சார மீட்டர் இருக்கிறதா இல்லையா?", "DPCName": "டிபிசி  பெயர்", "PermanentAddress": "நிரந்தர முகவரி", "PresentAddress": "தற்போதைய முகவரி", "Pincode": "பின்கோடு", "DPCType": "டிபிசி வகை", "POSType": "இயந்திர வகை", "Latitude": "அட்சரேகை", "Longitude": "தீர்க்கரேகை", "AuthBypassFlag": "அங்கீகார பைபாஸ் கொடி", "StorageLocation": "சேமிப்பு இடம்", "StorageType": "சேமிப்பக வகை", "Capacity": "கொள்ளளவு", "CarpetArea": "தரைவிரிப்பு பகுதி", "NewProvider": "புதிய வழங்குநர்", "UpdateProvider": "புதுப்பிக்கப்பட்ட வழங்குநர்கள்", "PrimarySIM": "முதன்மை சிம்", "PrimaryMobileNo": "முதன்மை கைப்பேசி எண்", "PrimaryProvider": "முதன்மை வழங்குநர்", "PrimaryPlan": "முதன்மைத் திட்டம்", "SecondarySIM": "இரண்டாம் நிலை சிம் கார்டு", "SecondaryMobileNo": "இரண்டாம் நிலை அலைபேசி எண்.", "SecondaryProvider": "இரண்டாம் நிலை திட்டம்", "SecondaryPlan": "இரண்டாம் நிலை திட்டம்", "NewScheme": "புதிய திட்டம்", "Scheme": "திட்டம்", "SchemeCode": "திட்டக் குறியீடு", "Description": "விளக்கம்", "NewGunny": "புதிய சாக்கு", "GunnyName": "சாக்கு பெயர்", "GunnyCode": "சாக்கு குறியீடு", "GunnyShortCode": "சாக்கு குறுகிய குறியீடு", "NewDevice": "புதிய சாதனம்", "IMEINumber": "ஐஎம்இஐ எண்", "DeviceManufacturer": "சாதன உற்பத்தியாளர்", "FirmwareVersion": "நிலைபொருள் பதிப்பு", "Protocol": "நெறிமுறை", "Device": "சாதனம்", "Provider": "வழங்குநர்", "NewProcurementTiming": "புதிய கொள்முதல் நேரம்", "NoOfBags": "பைகளின் எண்ணிக்கை", "NewProcurementRate": "புதிய கொள்முதல் விலை", "Product": "தயாரிப்பு", "Variety": "வகை", "Price": "விலை", "BonusPrice": "போனஸ் விலை", "GradeCut": "தர வெட்டு", "NewProcurementPriceCut": "புதிய கொள்முதல் விலை குறைப்பு", "ProcurementPriceCutType": "கொள்முதல் விலை குறைப்பு வகை", "MinimumRange": "குறைந்தபட்ச வரம்பு", "MaximumRange": "அதிகபட்ச வரம்பு", "PriceCutAmount": "விலை குறைப்பு தொகை", "NewHullingPriceCut": "புதிய அரைக்கும் விலை குறைப்பு", "HullingPriceCutType": "அரைக்கும் விலை குறைப்பு வகை", "DelayedDayCutType": "தாமதமான நாள் வெட்டு வகை", "NewDelayedDayCut": "புதிய தாமதமான நாள் வெட்டு", "DPC": "டிபிசி", "Gunny": "சாக்கு", "ProcurementTiming": "கொள்முதல் நேரம்", "Procurement Rate": "கொள்முதல் விலை", "PriceCut": "அரைக்கும் விலை குறைப்பு", "HullingPriceCut": "ஹல்லிங் விலை குறைப்பு", "DelayedDaysCut": "தாமதமான நாட்கள் குறைப்பு", "Vehicle": "வாகனம்", "IsRequired": "நிரப்ப வேண்டும்", "ProcurementPriceCut": "கொள்முதல் விலை குறைப்பு", "DriverName": "ஓட்டுனர் பெயர்", "DriverMobileNo": "ஓட்டுனர் கைபேசி எண்", "LicenseNo": "ஓட்டுநர் உரிமம் எண்", "DateOfBirth": "பிறந்த தேதி", "Driver": "ஓட்டுனர்", "InvalidFormat": "தவறான வடிவம்", "VehicleTracking": "வாகன கண்காணிப்பு", "VehicleStatus": "வாகன நிலை", "LastUpdated": "கடைசியாக புதுப்பிக்கப்பட்டது", "PowerStatus": "மின் நிலை", "Refresh": "புதுப்பிக்க", "MapView": "வா.அ. அலுவலரின் ஒப்புதல்", "GridView": "கட்டக் காட்சிக்கு மாற்றவும்", "RegisterNo": "பதிவு எண்", "VAOApproval": "வா.அ. அலுவலரின் ஒப்புதல்", "FarmerId": "விவசாயி அடையாள எண்", "FarmerName": "விவசாயி பெயர்", "MobileNo": "கைபேசி எண்", "ApprovedLand": "ஒப்புதலளிக்கப்பட்ட நிலம் (ஏக்கரில்)", "SkipPayment": "Skip Payment", "Reason": "Reason", "Enterreason": "Enter reason", "Optional": "Optional", "CompanyName": "Company Name", "AuthorizedPersonName": "Authorized Person Name", "EmailId": "Email ID", "WhatsappNo": "Whatsapp No", "VendorType": "Vendor Type", "GSTNumber": "GST Number", "LegalBusinessName": "Legal Business Name", "UIN": "UIN", "DateofRegistration": "Date of Registration", "StatusofGSTIN": "Status of GSTIN", "NatureofBusiness": "Nature of Business", "LandlineNumberofCompany": "Landline Number of Company", "ContactPersonName": "Contact Person Name", "Address": "Address", "FSSAI": "FSSAI", "MSME": "MSME", "AgmarkLicense": "Agmark License", "ContactEmail": "Contact Email", "OwnerName": "Owner Name", "BankAccountNumber": "Bank Account Number", "IFSCCode": "IFSC Code", "BankName": "Bank Name", "Branch": "Branch", "Commodity": "Commodity", "ContractID/WorkOrderID": "ContractID / WorkOrderID", "ContractStartDate": "Contract Start Date", "ContractEndDate": "Contract End Date", "Contract/WorkOrderDocument": "Contract / Work Order Document", "MillOwnerName": "Mill Owner Name", "MillCode": "Mill Code", "AdhaarNo": "<PERSON><PERSON>ar No", "HullingMonthlyCapacityinMTs": "Hulling Monthly Capacity in MTs", "IsGeneratorAvailable": "Is Generator Available", "IsSeparateEBAvailable": "Is Separate EB Available", "EBConsumptionNoBoiler": "EB Consumption No Boiler", "TypeofEBServiceOffice": "Type of EB Service Office", "AgreementUpto": "Agreement Upto", "SecurityDepositValue": "Security Deposit Value", "BankGuaranteeValue": "Bank Guarantee Value", "BGNumber": "BG Number", "BGValidUpTo": "BG Valid UpTo", "GenModel": "Gen Model", "EBConsumptionNoMill": "EB Consumption No Mill", "TypeofEBServiceBoiler": "Type of EB Service Boiler", "DateOfAppointment": "Date Of Appointment", "Capacity(Kwh.)": "Capacity (Kwh.)", "TypeofEBServiceMill": "Type of EB Service Mill", "EBConsumptionNoOffice": "EB Consumption No Office", "ApprovedStatus": "Approved Status", "TokenId": "Token Id", "ProductType": "Product Type", "NoofBags": "Total No of Bags", "TokenCreatedDate": "Token Created Date", "TokenNo": "Token No", "RequestedPerson": "Requested Person", "ProductName": "Product Name", "VarietyName": "Variety Name", "SeasonName": "Season Name", "SurveyNo": "Survey No", "LandType": "Land Type", "SubDivsion": "Sub Divsion", "ExpectedYield": "Expected <PERSON>eld", "BillNo": "Bill No", "Bags": "Bags", "PurchaseQuality": "Purchase Quality", "Incentive": "Incentive", "Amount": "Amount", "MoistureCut": "Moisture Cut", "DDSCut": "DDS Cut", "ISSCut": "ISS Cut", "PaymentAmount": "Payment Amount", "TripSheet": "Trip Sheet", "PlayBack": "Play Back", "ConfirmBankAccountNumber": "Confirm Bank Account Number", "QrCode": "QR Code", "AddQR": "Add QR", "ScanQR": "Scan QR", "StorageLocationFrom": "Storage Location From", "StorageLocationTo": "Storage Location To", "TotalGunny": "Total Gunny", "GunnyType": "Gunny <PERSON>", "QRCode": "QR Code", "GodownLocation": "Godown Location", "TruckMemoId": "Truck Memo Id", "TruckMemoDate": "Truck Memo Date", "TruckFrom": "Truck From", "VehicleNo": "Vehicle No", "TotalQty": "Total Qty (in Kgs.)", "Moisture": "Moisture", "StorageTypeFrom": "Storage Type From"}