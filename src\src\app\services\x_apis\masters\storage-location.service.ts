import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { StorageLocation, StorageLocationResponse } from '../../../models/x_models/masters/storage-location';

@Injectable({
  providedIn: 'root'
})
export class StorageLocationService {

  dataService = inject(DataService)

  create(data: StorageLocation) {
    return this.dataService.post<Response>("/storagelocation", data)
  }

  get() {
    return this.dataService.get<StorageLocationResponse>("/storagelocation")
  }

  getById(id: number) {
    return this.dataService.get<StorageLocation>(`/storagelocation/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/storagelocation/${id}`)
  }

}
