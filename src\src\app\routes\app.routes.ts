import { Routes } from '@angular/router';
import { LayoutComponent } from '../layout/layout.component';
import { NotFoundComponent } from '../pages/not-found/not-found.component';
import { AppRoutes } from '../enums/app-routes';
import { LoginComponent } from '../features/x_features/auth/login/login.component';
import { isUserAuthenticated, isUserLoggedOut } from '../guards/auth.guard';
import { PersonalDetailsComponent } from '../pages/msp/personal-details/personal-details.component';

export const routes: Routes = [
  {
    path: "",
    pathMatch: 'full',
    redirectTo: "login"
  },
  {
    path: 'login',
    component: LoginComponent,
    canActivate: [isUserLoggedOut]
  },
  {
    path: '',
    component: LayoutComponent,
    canActivate: [isUserAuthenticated],
    children: [
      {
        path: 'x',
        loadChildren: () => import("./x_routes").then(m => m.X_ROUTES)
      },
      {
        path: 'm',
        loadChildren: () => import("./m_routes").then(m => m.M_ROUTES)
      }
    ],
  },

  {
    path: 'error',
    component: NotFoundComponent,
  },
  {
    path: '**',
    pathMatch: 'full',
    redirectTo: AppRoutes.NotFound,
  },
];
