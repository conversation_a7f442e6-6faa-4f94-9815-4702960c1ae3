import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { LeaveManagementService } from '../../../../services/m_apis/service/leave-management.service';
import { LeaveApplicationRequest } from '../../../../enums/m_enums/leave.model';
interface Approver {
  id: string;
  name: string;
}
@Component({
  selector: 'app-leave-apply-new',
  imports: [  CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatDatepickerModule],
  templateUrl: './leave-apply-new.component.html',
  styleUrl: './leave-apply-new.component.scss'
})
export class LeaveApplyNewComponent implements OnInit {
 
  leaveForm!: FormGroup;
  selectedFiles: File[] = [];
  employeeId: string | null = '';
  approvers: Approver[] = [
    { id: '1', name: 'RM' },
  ];
  

  sessions = [
    { value: 'full', label: 'Full Day' },
    { value: 'morning', label: 'Morning Session' },
    { value: 'afternoon', label: 'Afternoon Session' }
  ];

  successMessage: any;
  errorMessage: string='';
  calculatedDays: number | null = null;
  selectedFileName: string | null = null;
  selectedFile: File | null = null;
  leaveTypes: any[] = [];

  constructor(private formBuilder: FormBuilder, private snackBar: MatSnackBar,private leaveService: LeaveManagementService){
   
  }

  ngOnInit(): void {
    this.employeeId = 'EMP040';
    this.leaveForm = this.formBuilder.group({
      leaveType: ['', Validators.required],
      fromDate: ['', Validators.required],
      toDate: ['', Validators.required],
      fromSession: [''],
      toSession: [''],
      approver: ['', Validators.required],
      reason: ['', [Validators.required, Validators.minLength(10)]],
      attachments: ['']
    });

    this.getLeaveTypes();
  }

  getLeaveTypes(): void {
    this.leaveService.getAllLeaveTypes().subscribe({
      next: (response) => {
        this.leaveTypes = response;
      },
      error: (error) => {
        console.error('Error fetching leave types:', error);
      }
    });
  }

  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Add new files to existing ones instead of replacing
      const newFiles = Array.from(files) as File[];
      this.selectedFiles = [...this.selectedFiles, ...newFiles];
      const fileNames = this.selectedFiles.map(file => file.name).join(', ');
      this.leaveForm.patchValue({ attachments: fileNames });
      
      // Clear the input to allow selecting the same file again if needed
      event.target.value = '';
    }
  }
  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
    const fileNames = this.selectedFiles.map(file => file.name).join(', ');
    this.leaveForm.patchValue({ attachments: fileNames });
    
    // Clear the file input to allow re-selection
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  getApprovername(id: string): string {
    const approver = this.approvers.find(approver => approver.id === id);
    return approver ? approver.name : '';
  }

  onReset(): void {
    this.leaveForm.reset();
    this.selectedFiles = [];
    
    // Clear the file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
    
    this.snackBar.open('Form reset successfully', 'Close', {
      duration: 3000
    });
  }

   onSubmit():void{
      if(this.leaveForm.valid){
        const formData = new FormData();
        const request: LeaveApplicationRequest = {
          employeeId: this.employeeId,
          leaveTypeId: this.leaveForm.get('leaveType')?.value,
          fromDate: this.leaveForm.get('fromDate')?.value,
          toDate: this.leaveForm.get('toDate')?.value,
          fromSession: this.leaveForm.get('fromSession')?.value,
          toSession: this.leaveForm.get('toSession')?.value,
          approverId: this.leaveForm.get('approver')?.value,
          reason: this.leaveForm.get('reason')?.value
        };
        console.log('FormData request:', request); // Debug payload
        formData.append('request', new Blob([JSON.stringify(request)], { type: 'application/json' }));
        if (this.selectedFiles.length > 0) {
          formData.append('document', this.selectedFiles[0]);
          console.log('FormData file:', this.selectedFiles[0].name); // Debug file
        }
        for (const pair of (formData as any).entries()) {
          console.log(`FormData entry: ${pair[0]}, ${pair[1]}`);
        }
        this.leaveService.applyForLeave(formData).subscribe({
          next: (response) => {
            this.successMessage = response.message;
            this.errorMessage = '';
            this.showSuccess('Leave application submitted successfully');
  
            this.leaveForm.reset();
            this.selectedFiles = [];
            this.calculatedDays = null;
            this.selectedFileName = null;
            this.selectedFile = null;
            // this.loadLeaveBalances();
          },
          error: (error) => {
            this.showError(error.error?.message || 'Failed to submit leave application');
            console.error('Error submitting leave:', error);
          }
        });
      }
    }

    calculateLeaveDays(): number {
      const fromDate = this.leaveForm.get('fromDate')?.value;
      const toDate = this.leaveForm.get('toDate')?.value;
      
      if (fromDate && toDate) {
        const start = new Date(fromDate);
        const end = new Date(toDate);
        const diffTime = Math.abs(end.getTime() - start.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
        return diffDays;
      }
      return 0;
    }

    updateDaysCalculation(): void {
      if (this.leaveForm.valid) {
        const { fromDate, toDate, fromSession, toSession } = this.leaveForm.value;
    
        if (fromDate && toDate) {
          const start = new Date(fromDate);
          const end = new Date(toDate);
    
          // Calculate the difference in days
          let days = (end.getTime() - start.getTime()) / (1000 * 3600 * 24) + 1;
    
          // Adjust for half-day selections
          if (start.getTime() === end.getTime()) {
            // If both sessions are selected, count as a full day
            if ((fromSession === 'FIRST_HALF' && toSession === 'SECOND_HALF') ||
                (fromSession === 'SECOND_HALF' && toSession === 'FIRST_HALF')) {
              days = 1;
            } else {
              // Otherwise, count as half a day
              days = 0.5;
            }
          } else {
            // Adjust for half-day selections on different days
            if (fromSession === 'SECOND_HALF') days -= 0.5;
            if (toSession === 'FIRST_HALF') days -= 0.5;
          }
    
          this.calculatedDays = days >= 0 ? days : null;
        } else {
          this.calculatedDays = null;
        }
      }
    }
    
  showSuccess(arg0: string) {
    this.snackBar.open(arg0, 'Close', {
      duration: 3000,
      verticalPosition: 'top',
      horizontalPosition: 'center',
      panelClass: ['success-snackbar']
    });
  }
  showError(arg0: any) {
    this.snackBar.open(arg0, 'Close', {
      duration: 3000,
      verticalPosition: 'top',
      horizontalPosition: 'center',
      panelClass: ['error-snackbar']
    });
  }
}
