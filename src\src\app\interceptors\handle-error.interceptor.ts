import { HttpErrorResponse, HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError, throwError } from 'rxjs';
import { AlertService } from '../services/alert.service';
import { AppRoutes } from '../enums/app-routes';
import { Router } from '@angular/router';
import { SessionService } from '../services/session.service';
import swal from "sweetalert2";

export const handleErrorInterceptor: HttpInterceptorFn = (req, next) => {
  const sessionService = inject(SessionService)
  const router = inject(Router)
  const alert = inject(AlertService);

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {

      alertError(error, alert)

      if (error.status === 401 || error.status === 403 || error.status === 423) {
        sessionService.clear();
        // router.navigate([AppRoutes.Login])
      }
      if (error.status === 0) {
        router.navigateByUrl(AppRoutes.NotFound)
      }

      return throwError(() => error);
    })
  );
};

const alertError = (error: HttpErrorResponse, alert: AlertService) => {
  let failures = error.error?.failures
  console.log(error)
  if (failures?.length) {
    failures.forEach((failure: string) => {
      alert.error(failure)
    })
  } else {
    const msg = fallBackErrorMessage(error);
    console.log(msg);
    alert.error(msg);
  }
}

const fallBackErrorMessage = (rejection: HttpErrorResponse) => {
  switch (rejection.status) {
    case 0:
      return 'Unable to connect to the server, please try again in a couple of seconds.'
    case 429:
      return 'You’ve made too many requests. Please wait a moment before trying again.'
    case 400:
      return 'The data you entered seems incorrect. Please check and try again.'
    case 404:
      return "Oops! We couldn't find the page/resource you're looking for."
    case 500:
      return 'Something went wrong on our end. Please try again later.'
    case 409:
      return 'There was a conflict with your request. Please check and try again.'
    case 401:
      return 'You need to log in to access this page.'
    case 403:
      return 'You don’t have permission to access this content.'
    case 423:
      return 'Session expired'
    default:
      return "Unknown Error";
  }
};


