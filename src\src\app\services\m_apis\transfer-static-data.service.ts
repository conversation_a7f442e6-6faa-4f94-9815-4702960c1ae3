
import { Injectable } from '@angular/core';
import { Region, Department, Employee } from '../../enums/m_enums/transfer.model';


@Injectable({
  providedIn: 'root'
})
export class TransferStaticDataService {

  getRegions(): Region[] {
    return [
      { id: 'R1', name: 'Chennai' },
      { id: 'R2', name: 'Coimbatore' },
      { id: 'R3', name: 'Madurai' }
    ];
  }

  getDepartments(): Department[] {
    return [
      { id: 'HR', name: 'Human Resources' },
      { id: 'FIN', name: 'Finance' },
      { id: 'OPS', name: 'Operations' }
    ];
  }

  getEmployees(): Employee[] {
    return [
      { id: 'E1001', name: '<PERSON><PERSON>', position: 'Assistant Manager', region: 'R1' },
      { id: 'E1002', name: '<PERSON>riya <PERSON>', position: 'Junior Assistant', region: 'R2' },
      { id: 'E1003', name: '<PERSON><PERSON>', position: 'Store Keeper', region: 'R3' }
    ];
  }
}