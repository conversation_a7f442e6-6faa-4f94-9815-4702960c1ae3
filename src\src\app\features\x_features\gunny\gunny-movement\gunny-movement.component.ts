import { Component, computed, effect, inject, input, linkedSignal, OnInit, output, resource, signal, viewChild } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { <PERSON><PERSON><PERSON>on, MatButtonModule } from '@angular/material/button';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError, MatPrefix } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { LookupService } from '../../../../services/x_apis/lookup.service';;
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AlertService } from '../../../../services/alert.service';
import { DatePipe, NgTemplateOutlet } from '@angular/common';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { GunnyMovementService } from '../../../../services/x_apis/gunny/gunny-movement.service';
import { GunnyMovement } from '../../../../models/x_models/gunny/gunny-movement';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { GunnyList } from '../../../../models/x_models/masters/gunny';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { GunnyService } from '../../../../services/x_apis/masters/gunny.service';
import { MatToolbarModule } from '@angular/material/toolbar';
import { QrCode } from '../../../../models/x_models/gunny/gunny-allocation';

@Component({
  selector: 'app-gunny-movement',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatFormFieldModule, TranslateModule,
    MatMenuModule,
    MatPaginatorModule,
    MatIcon,
    MatPrefix,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatSortModule,
    MatToolbarModule
  ],
  templateUrl: './gunny-movement.component.html',
  styleUrls: ['./gunny-movement.component.scss'],
  providers: [provideNativeDateAdapter(), DatePipe],
})
export class GunnyMovementComponent implements OnInit {
  menuService = inject(MenuService);
  gunnyMovementService = inject(GunnyMovementService)
  gunnyService = inject(GunnyService)
  alert = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  lookupService = inject(LookupService);
  storageType = resource({ loader: () => this.lookupService.getStoragelocation(1) }).value;
  gunnyType = resource({ loader: () => this.lookupService.getGunnytype() }).value;

  mode = input.required<string>();
  uniqueId = input.required<any>()
  closed = output<boolean>();
 
  router = inject(Router);
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);

  formDisable = signal<boolean>(false);
  qrAbstract = signal<any[]>([]);


  gunnyMovement = signal<GunnyMovement | null>(null);
  minToDate = signal<Date>(new Date());

  form = this.fb.group({
    id: [0],
    fromStorageLocationId: this.fb.control<number | null>(null, Validators.required),
    toStorageLocationId: this.fb.control<number | null>(null, Validators.required),
    gunnyMovementId: this.fb.control<number>(0, null),
    qrCode: this.fb.control<string>('', Validators.required),
    noOfBags: this.fb.control<number>(0, null)
  });

  // for grid
  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  dataSource = signal(new MatTableDataSource<GunnyList>([]));




  // listResource = resource({ loader: () => this.gunnyService.get() });
  // dataSource = linkedSignal(() => new MatTableDataSource(this.listResource.value()));
  gridmode = signal<'view' | 'edit' | 'add'>('add');
  showGrid = signal(false);

  listData = signal<GunnyList>({
    id: 0,
    gunnyQrCode: '',
    gunnyName: '',
    gunnyShortName: '',
    storageLocationName: ''
  });

  displayedColumns: string[] = [
    'gunnyName',
    'gunnyQrCode',
    'actions',
  ];

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    })
  }

  ngOnInit() {
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getLists(this.uniqueId());
    }
  }

  readonly search = signal('');
  readonly filteredTaluks = computed(() =>
    this.storageType()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  filter(value: any) {
    this.search.set(value.target.value);
  }

  async getCrop() {
    const res = await this.gunnyMovementService.getById(this.uniqueId());
    this.gunnyMovement.set(res);
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
    try {
      this.form.controls['noOfBags'].setValue(Number(this.form.value.noOfBags) + 1);
      const formValue = { ...this.form.value };
      const res : any = await this.gunnyMovementService.qrCreate(formValue as GunnyMovement);
      if (res.isSuccess) {
       
          this.showGrid.set(true);
        
        if (formValue.id == 0) {
          this.formDisable.set(true)
          this.form.controls['qrCode'].setValue("");
          this.alert.success(`Gunny Movement ${formValue.fromStorageLocationId} Created Successfully.`)
          this.getLists(res.value);
        }
        else {
          this.form.controls['qrCode'].setValue("");
          this.getLists(res.value);
          this.alert.success(`Gunny Movement ${formValue.fromStorageLocationId} Updated Successfully.`)
        }
      } else {
        console.log('No Data Found in Qr getById');
      }
    } catch (error) {
      this.form.controls['noOfBags'].setValue(Number(this.form.value.noOfBags) - 1);
      console.error('Error fetching data:', error);
    }
  } else{
    this.form.markAllAsTouched();
  }
  }

  async getLists(value : number) {
    const res: any = await this.gunnyMovementService.getByQrId(value);
    if(res) {
      this.formDisable.set(true)

      this.form.patchValue({ ...res });
      this.form.controls['id'].setValue(res.id);
      this.form.controls['noOfBags'].setValue(res.noOfBags);
      
      this.form.controls['gunnyMovementId'].setValue(value);
      this.listData.set(res.scannedGunnyInfoViewModel);
      
      this.onAbstract(res.scannedGunnyInfoViewModel)




      const data = this.listData();
      const dataArray: GunnyList[] = data
        ? (Array.isArray(data) ? data : [data])
        : [];
      this.dataSource.set(new MatTableDataSource(dataArray))
      setTimeout(() => {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }, 100)
    } else{
      console.log('No Data Found in Qr getById')
    }
    
  }


   onAbstract(updatedData: any[]) {
    debugger
    updatedData.map(item => {
      const matchingGunny = this.gunnyType()?.find(g => g.key === item.gunnyName);
      return matchingGunny ? { ...item, gunny: matchingGunny.value } : item;
    });
   
      const result = updatedData.reduce((acc, curr) => {
        const existing = acc.find((item: QrCode) => item.gunnyName === curr.gunnyName);
        if (existing) {
          existing.count++;
          existing.gunnyQrCode = curr.gunnyQrCode;
        } else {
          acc.push({ ...curr, count: 1 });
        }
        return acc;
      }, []);

     

      this.qrAbstract.set(result);
      console.log('this.qrAbstract',this.qrAbstract)
    }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

  onQrEnter() {
    this.onSubmit();
  }

  //for grid ----------------------------->>>

  onAdd() {
    this.listData.set({
      id: 0,
      gunnyQrCode: '',
      gunnyName: '',
      gunnyShortName: '',
      storageLocationName: ''
    });
    this.gridmode.set("add")
    this.showGrid.set(true)
  }

  onView(data: GunnyList) {
    debugger
    this.listData.set(data);
    this.gridmode.set('view');
    this.showGrid.set(true)
    this.getLists(this.uniqueId());
  }

  onEditGrid(data: GunnyList) {
    this.listData.set(data);
    this.gridmode.set('edit');
    this.showGrid.set(true);
    this.getLists(this.uniqueId());
  }

  onClose() {
    this.showGrid.set(false)
    this.getLists(this.uniqueId());
    // this.search.setValue("");
  }

  onRefresh() {
    this.getLists(this.uniqueId());
    // this.search.setValue("");
  }

  async onDelete(data: any) {
    await confirmAndDelete(data, data.gunnyName, 'Gunny Movement', this.gunnyMovementService, () => this.getLists(this.uniqueId()));
  }

  onSearch() {
    // const filterValue = this.search.value?.trim().toLowerCase() || '';
    // this.dataSource().filter = filterValue;
  }



  addQR(id: number, qrcode: any) {

  }

  moveFocus(nextInput: any) {
    nextInput.setFocus();
  }
}
