import { Component, computed, ElementRef, inject, input, OnInit, output, resource, signal, viewChild } from '@angular/core';
import { DeviceService } from '../../../../services/x_apis/masters/device.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Device } from '../../../../models/x_models/masters/device';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { MatButton } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { <PERSON><PERSON><PERSON><PERSON>ield, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { LookUpResponse } from '../../../../models/x_models/lookup';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-device',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatFormFieldModule,
    TranslateModule,
    MatToolbarModule
  ],
  templateUrl: './device.component.html',
  styleUrls: ['./device.component.scss']
})

export class DeviceComponent implements OnInit {
  menuService = inject(MenuService);
  deviceService = inject(DeviceService)
  alert = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  lookupService = inject(LookupService);
  // seasons = resource({ loader: () => this.lookupService.getSeason() }).value;
  protocols = resource({ loader: () => this.lookupService.getProtocol() }).value;
  providers = resource({ loader: () => this.lookupService.getUnmappedProvider() }).value;
  devicemanufacturers = resource({ loader: () => this.lookupService.getDeviceManifacturer() }).value;
  mode = input.required<string>();
  uniqueId = input.required<any>()
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  device = signal<Device | null>(null);
  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')
  search3 = viewChild.required<ElementRef<HTMLInputElement>>('search3')

  form = this.fb.group({
    id: this.fb.control<number>(0),
    imeiNo: this.fb.control<string>('', Validators.required),
    deviceManufacturerType: this.fb.control<number | null>(null, Validators.required),
    firmwareVersion: this.fb.control<string>('', Validators.required),
    providerId: this.fb.control<number | null>(null, Validators.required),
    protocolId: this.fb.control<number | null>(null, Validators.required)
  });


  ngOnInit() {
    // this.form.get("FromDate")?.valueChanges.subscribe(res=>{
    //   this.form.patchValue({
    //     ToDate: ''
    //   });
    // });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getCrop()
    }
  }

  readonly deviceSearch = signal('');
  readonly providerSearch = signal('');
  readonly protocolSearch = signal('');

  readonly filteredProtocols = computed(() =>
    this.protocols()?.filter(x =>
      x.value.toLowerCase().includes(this.protocolSearch().toLowerCase())
    )
  );

  readonly filteredProviders = computed(() =>
    this.providers()?.filter(x =>
      x.value.toLowerCase().includes(this.providerSearch().toLowerCase())
    )
  );

  readonly filteredManufacturers = computed(() =>
    this.devicemanufacturers()?.filter(x =>
      x.value.toLowerCase().includes(this.deviceSearch().toLowerCase())
    )
  );

  filter(value: any,type:string) {
   if (type === 'device') {
      this.deviceSearch.set(value.target.value);
    } else if (type === 'provider') {
      this.providerSearch.set(value.target.value);
    } else  {
      this.protocolSearch.set(value.target.value);
    }
  }

   resetSearch(type: any) {
    if (type === 'device') {
      this.search1().nativeElement.value = '';
      this.deviceSearch.set('');
    } else if (type === 'provider') {
      this.providerSearch.set('');
      this.search2().nativeElement.value = '';
    } else if (type === 'taluk') {
      this.protocolSearch.set('');
      this.search3().nativeElement.value = '';
    }
  }


  async getCrop() {
    const res = await this.deviceService.getById(this.uniqueId());
    this.form.patchValue(res)
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {

    if (this.form.valid) {
      const payload1 = { ...this.form.value }
      let payload = {
        id: payload1.id ?? 0,
        imeiNo: payload1.imeiNo!,
        deviceManufacturerType: payload1.deviceManufacturerType!,
        firmwareVersion: payload1.firmwareVersion!,
        providerId: payload1.providerId!,
        protocolId: payload1.protocolId!
      }
      const res = await this.deviceService.create(payload)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (payload1.id == 0) {
          this.alert.success(`Device Created Successfully.`)
        }
        else {
          this.alert.success(`Device Updated Successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }
}
