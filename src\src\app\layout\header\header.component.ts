import { ChangeDetectorRef, Component, effect, inject, OnInit, output, signal, WritableSignal } from '@angular/core';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatBadgeModule } from '@angular/material/badge';
import { ViewModeService } from '../../services/view-mode.service';
import { SessionService } from '../../services/session.service';
import { MatMenuModule } from "@angular/material/menu";
import { Router } from '@angular/router';
import { AppRoutes } from '../../enums/app-routes';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import Swal from 'sweetalert2';
import { MenuService } from '../../services/x_apis/menu.service';


@Component({
  selector: 'app-header',
  imports: [MatFormFieldModule, MatSelectModule, MatInputModule, ReactiveFormsModule, MatIconModule, MatButtonModule,
    MatBadgeModule, MatMenuModule, TranslateModule, MatButtonToggleModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent implements OnInit {
  viewModeService = inject(ViewModeService)
  sessionService = inject(SessionService)
  router = inject(Router)
  viewMode = this.viewModeService.viewMode
  sideNavToggled = output<boolean>();

  translate = inject(TranslateService);
  currentLang = this.translate.currentLang || 'en';
  language: WritableSignal<string> = signal('');

  menuService=inject(MenuService)
 

  constructor(private cdr: ChangeDetectorRef) {
  }

  ngOnInit(): void {
    console.log('session',this.sessionService.session());
    
    localStorage.setItem('language','en');
  //   this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
  //     localStorage.setItem('language', event.lang);
  //     const lang = localStorage.getItem('language') ?? 'en';
  //     this.currentLang  = lang;
  // });
}

  switchLanguage(lang: string) {
    this.translate.use(lang).subscribe(() => {
      localStorage.setItem('language', lang);
      this.currentLang = lang;
      this.cdr.detectChanges();
    });
  }

  openSideNav() {
    this.viewModeService.openSideNav()
    this.sideNavToggled.emit(true);

  }

  async onSignOut() {
    this.logoutConfirmation();
    // const res = await this.authService.logout()

  }

  async logoutConfirmation() {
    const title = 'Logout Confirmation';
    const txt = `Are you sure you want to Logout ? `;
    const Yes = 'Yes';
    const No = 'No';

    const swalRes = await Swal.fire({
      title,
      text: txt,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: Yes,
      cancelButtonText: No,
      customClass: {
        confirmButton: 'btn-primary',
        cancelButton: 'btn-secondary',
      },
    });

    if (swalRes.isConfirmed) {
      localStorage.setItem('language', 'en');
      this.translate.use('en');
      this.sessionService.clear()
      void this.router.navigate([AppRoutes.Login]);
      // let payload: UpdateUserStatusPayload = {
      //   id: user.id,
      //   statusType: StatusType.Inactive,
      // };

      // const res = await this.userService.update(payload);
      // if (res.isSuccess) {
      //   this.getUsers();
      // }
    }
  }
}
