import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { TruckForm, TruckResponse } from '../../../models/x_models/acknowledge/truck';

@Injectable({
    providedIn: 'root'
})
export class TruckService {
    dataService = inject(DataService)

    create(data: TruckForm) {
        return this.dataService.post<Response>("/truck", data)
    }

    get() {
        return this.dataService.get<TruckResponse>("/crop")
    }

    getById(id: number) {
        return this.dataService.get<TruckForm>(`/truck/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/truck/${id}`)
    }
}
