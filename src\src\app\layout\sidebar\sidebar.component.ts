import { ChangeDetectorRef, Component, effect, inject, input, output, signal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { filter, first } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { ViewModeService } from '../../services/view-mode.service';
import { Menu } from '../../models/x_models/menu';
import { SessionService } from '../../services/session.service';
import { MenuService } from '../../services/x_apis/menu.service';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-sidebar',
  imports: [MatIconModule, RouterModule, MatButtonModule, CommonModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
})
export class SidebarComponent {
  router = inject(Router);
  menuService = inject(MenuService)
  viewModeService = inject(ViewModeService);
  menu = signal<Menu[]>([]);
  activeMenu = signal<any>({});
  closingIndex = signal<number | null>(null);
  activeUrl = signal('');
  viewMode = this.viewModeService.viewMode;
  menuSelected = output<Menu>();
  submenuSelected = output<boolean>();
  sideNavCollapsed = input<boolean | null>(false);
  sideNavToggled = output<boolean>()
  collapsed = input<boolean | null>()
  focused = input<boolean>(false)
  maxSubItems = signal<number>(1);
  translate = inject(TranslateService);
  isLeaveMenuActive = signal(false);

  constructor(private cdr: ChangeDetectorRef) {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event) => {
        if (event.urlAfterRedirects != '/') {
          this.activeUrl.set(event.urlAfterRedirects);
          this.setActiveMenu();
        }
      });


    const currentUrl = this.router.url;
    if (currentUrl !== '/') {
      this.activeUrl.set(currentUrl);
      this.setActiveMenu();
    }
  }

  ngOnInit(): void {
    this.getMenu();
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      localStorage.setItem('language', event.lang);
      this.getMenu();
    });
  }
  

  async getMenu() {
    const res = await this.menuService.get()
    this.menu.set(res);
    this.setActiveMenu();

  }

  hasItems(item: any) {
    return item.submenu !== undefined ? item.submenu.length > 0 : false;
  }

  onMenuItemClick(activeIndex: any) {
    debugger
    this.menuSelected.emit(this.menu()[activeIndex]);
    if (!this.menu()[activeIndex]?.submenu?.length && this.viewModeService.viewMode() !== 'lg') {
      this.viewModeService.closeSideNav();
    }
    this.menu.update((menu) => {
      return menu.map((item, index) => {
        if (item.active) {
          this.closingIndex.set(index);
        }
        if (index === activeIndex && !item.active ) {
          item.active = true;
          this.menuService.setActiveMenu(item)

          item.submenu.forEach((subitem) => {
            subitem.active = subitem.path === this.activeUrl();
            if (subitem.path === this.activeUrl()) this.menuService.setActiveMenu(subitem)
          });
        } else {
          if (item.path !== this.activeUrl()) {
            item.active = false;
          }
          item.submenu.forEach((subitem) => {
            subitem.active = false;
          });
        }
        console.log(item,this.closingIndex(),index);
        
        return item;
      });
    });
  }

  onSubmenuItemClick(menuItemIndex: number, submenuItemIndex: number) {
    debugger
    this.submenuSelected.emit(true);
    if (this.viewModeService.viewMode() !== 'lg') {
      this.viewModeService.closeSideNav();
    }
    this.menu.update((menu) => {
      menu[menuItemIndex].active = true;
      menu[menuItemIndex].submenu.forEach((subItem, index) => {
        subItem.active = index === submenuItemIndex;
        if (index === submenuItemIndex) this.menuService.setActiveMenu(subItem)
      });
      return menu;
    });
  }

  onListItemAnimationEnd(event: any) {
    debugger
    if (event.animationName.includes('collapse') && this.closingIndex()) {
      this.closingIndex.set(null);
    }
  }

  setActiveMenu() {
    this.menu.update((menu) => {
      return menu.map((item) => {
        if (!item.submenu?.length) {
          item.active = item.path === this.activeUrl();
          if (item.path === this.activeUrl()) this.menuService.setActiveMenu(item)
        } else {
          item.submenu.forEach((subitem: any, index: any) => {
            // item.active = true;
            if (subitem.path === this.activeUrl()) {
              subitem.active = true;
              item.active = true;
              this.menuService.setActiveMenu(subitem)
            } else {
              subitem.active = false;
            }
            if (index > this.maxSubItems()) {
              this.maxSubItems.set(index + 1)
            }
          });
        }
        return item;
      });
    });
  }

  onToggleSidebar() {
    this.sideNavToggled.emit(true)
  }

  onNavItemClick(navitem: any) {
    // debugger;
    this.menu()?.forEach(item => {
      if (item !== navitem) {
        item.show = false;
      } else {
        if (item.submenu) {
          item.show = !item.show;
        } else {
          item.show = true;
        }

      }
    })
  }

}

// const menu = [
//   {
//     id: 1,
//     parentId: null,
//     path: '/x/dashboard',
//     title: 'Dashboard',
//     icon: 'speed',
//     active: false,
//     class: '',
//     badge: '',
//     badgeClass: '',
//     isExternalLink: false,
//     sequenceNo: 1,
//     isVisible: true,
//     controlAccess: {
//       canAdd: true,
//       canEdit: true,
//       canView: true,
//       canDelete: true,
//       canDeActivate: true,
//       canActivate: true,
//       canReset: true,
//       canHistory: true,
//       canDownload: true,
//     },
//     submenu: [],
//   },
//   // {
//   //   id: 7,
//   //   parentId: null,
//   //   path: '/x/users',
//   //   title: 'Users',
//   //   icon: 'table',
//   //   active: false,
//   //   class: '',
//   //   badge: '',
//   //   badgeClass: '',
//   //   isExternalLink: false,
//   //   sequenceNo: 4,
//   //   isVisible: true,
//   //   controlAccess: {
//   //     canAdd: true,
//   //     canEdit: true,
//   //     canView: true,
//   //     canDelete: true,
//   //     canDeActivate: true,
//   //     canActivate: true,
//   //     canReset: true,
//   //     canHistory: true,
//   //     canDownload: true,
//   //   },
//   //   submenu: [],
//   // },
//   // {
//   //   id: 3,
//   //   parentId: null,
//   //   path: '/x/form',
//   //   title: 'Form components',
//   //   icon: 'article',
//   //   active: false,
//   //   class: '',
//   //   badge: '',
//   //   badgeClass: '',
//   //   isExternalLink: false,
//   //   sequenceNo: 3,
//   //   isVisible: true,
//   //   controlAccess: {
//   //     canAdd: true,
//   //     canEdit: true,
//   //     canView: true,
//   //     canDelete: true,
//   //     canDeActivate: true,
//   //     canActivate: true,
//   //     canReset: true,
//   //     canHistory: true,
//   //     canDownload: true,
//   //   },
//   //   submenu: [],
//   // },
//   // {
//   //   id: 5,
//   //   parentId: null,
//   //   path: '',
//   //   title: 'Reports',
//   //   icon: 'report',
//   //   active: false,
//   //   class: '',
//   //   badge: '',
//   //   badgeClass: '',
//   //   isExternalLink: false,
//   //   sequenceNo: 5,
//   //   isVisible: true,
//   //   controlAccess: {
//   //     canAdd: true,
//   //     canEdit: true,
//   //     canView: true,
//   //     canDelete: true,
//   //     canDeActivate: true,
//   //     canActivate: true,
//   //     canReset: true,
//   //     canHistory: true,
//   //     canDownload: true,
//   //   },
//   //   submenu: [
//   //     {
//   //       id: 51,
//   //       parentId: 5,
//   //       path: '/x/report',
//   //       title: 'Report',
//   //       active: false,
//   //       icon: '',
//   //       class: '',
//   //       badge: '',
//   //       badgeClass: '',
//   //       isExternalLink: false,
//   //       sequenceNo: 1,
//   //       isVisible: true,
//   //       controlAccess: {
//   //         canAdd: true,
//   //         canEdit: true,
//   //         canView: true,
//   //         canDelete: true,
//   //         canDeActivate: true,
//   //         canActivate: true,
//   //         canReset: true,
//   //         canHistory: true,
//   //         canDownload: true,
//   //       },
//   //       submenu: [],
//   //     },
//   //   ],
//   // },
//   {
//     id: 7,
//     parentId: null,
//     path: '',
//     title: 'Master',
//     icon: 'table',
//     active: false,
//     class: '',
//     badge: '',
//     badgeClass: '',
//     isExternalLink: false,
//     sequenceNo: 6,
//     isVisible: true,
//     controlAccess: {
//       canAdd: true,
//       canEdit: true,
//       canView: true,
//       canDelete: true,
//       canDeActivate: true,
//       canActivate: true,
//       canReset: true,
//       canHistory: true,
//       canDownload: true,
//     },
//     submenu: [
//       {
//         id: 52,
//         parentId: 5,
//         path: '/x/season',
//         title: 'Season',
//         active: false,
//         icon: '',
//         class: '',
//         badge: '',
//         badgeClass: '',
//         isExternalLink: false,
//         sequenceNo: 1,
//         isVisible: true,
//         controlAccess: {
//           canAdd: true,
//           canEdit: true,
//           canView: true,
//           canDelete: true,
//           canDeActivate: true,
//           canActivate: true,
//           canReset: true,
//           canHistory: true,
//           canDownload: true,
//         },
//         submenu: [],
//       },
//       {
//         id: 53,
//         parentId: 5,
//         path: '/x/crops',
//         title: 'Crop',
//         active: false,
//         icon: '',
//         class: '',
//         badge: '',
//         badgeClass: '',
//         isExternalLink: false,
//         sequenceNo: 2,
//         isVisible: true,
//         controlAccess: {
//           canAdd: true,
//           canEdit: true,
//           canView: true,
//           canDelete: true,
//           canDeActivate: true,
//           canActivate: true,
//           canReset: true,
//           canHistory: true,
//           canDownload: true,
//         },
//         submenu: [],
//       },
//       {
//         id: 54,
//         parentId: 5,
//         path: '/x/regions',
//         title: 'Region',
//         active: false,
//         icon: '',
//         class: '',
//         badge: '',
//         badgeClass: '',
//         isExternalLink: false,
//         sequenceNo: 7,
//         isVisible: true,
//         controlAccess: {
//           canAdd: true,
//           canEdit: true,
//           canView: true,
//           canDelete: true,
//           canDeActivate: true,
//           canActivate: true,
//           canReset: true,
//           canHistory: true,
//           canDownload: true,
//         },
//         submenu: [],
//       },
//       {
//         id: 55,
//         parentId: 5,
//         path: '/x/units',
//         title: 'Unit Office' ,
//         active: false,
//         icon: '',
//         class: '',
//         badge: '',
//         badgeClass: '',
//         isExternalLink: false,
//         sequenceNo: 7,
//         isVisible: true,
//         controlAccess: {
//           canAdd: true,
//           canEdit: true,
//           canView: true,
//           canDelete: true,
//           canDeActivate: true,
//           canActivate: true,
//           canReset: true,
//           canHistory: true,
//           canDownload: true,
//         },
//         submenu: [],
//       },

//       {
//         id: 56,
//         parentId: 5,
//         path: '/x/taluks',
//         title: 'Taluk',
//         active: false,
//         icon: '',
//         class: '',
//         badge: '',
//         badgeClass: '',
//         isExternalLink: false,
//         sequenceNo: 8,
//         isVisible: true,
//         controlAccess: {
//           canAdd: true,
//           canEdit: true,
//           canView: true,
//           canDelete: true,
//           canDeActivate: true,
//           canActivate: true,
//           canReset: true,
//           canHistory: true,
//           canDownload: true,
//         },
//         submenu: [],
//       },
//       {
//         id: 57,
//         parentId: 5,
//         path: '/x/blocks',
//         title: 'Block',
//         active: false,
//         icon: '',
//         class: '',
//         badge: '',
//         badgeClass: '',
//         isExternalLink: false,
//         sequenceNo: 9,
//         isVisible: true,
//         controlAccess: {
//           canAdd: true,
//           canEdit: true,
//           canView: true,
//           canDelete: true,
//           canDeActivate: true,
//           canActivate: true,
//           canReset: true,
//           canHistory: true,
//           canDownload: true,
//         },
//         submenu: [],
//       },
//       {
//         id: 58,
//         parentId: 5,
//         path: '/x/villages',
//         title: 'Village',
//         active: false,
//         icon: '',
//         class: '',
//         badge: '',
//         badgeClass: '',
//         isExternalLink: false,
//         sequenceNo: 10,
//         isVisible: true,
//         controlAccess: {
//           canAdd: true,
//           canEdit: true,
//           canView: true,
//           canDelete: true,
//           canDeActivate: true,
//           canActivate: true,
//           canReset: true,
//           canHistory: true,
//           canDownload: true,
//         },
//         submenu: [],
//       },
//     ],
//   }
// ];
