<div class="history-of-leaves-container">
<mat-card class="header-card">
    <mat-card-header class="form-header">
      <div class="header-content">
        <div class="logo-section">
          <mat-icon class="tncsc-logo">history</mat-icon>
        </div>
        <div class="title-section">
          <h2 class="form-subtitle">My History Leaves</h2>
          <p class="subtitle">View your history leave applications</p>
        </div>
      </div>
    </mat-card-header>
  </mat-card>
  <mat-card class="table-card">
    <mat-card-content>
      <!-- Search Filter -->
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search leaves</mat-label>
        <input matInput (keyup)="applyFilter($event)" 
               placeholder="Search by leave type, reason, status..." />
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Loading history leaves...</p>
      </div>

      <!-- Data Table -->
      <div *ngIf="!isLoading" class="table-container">
        <table mat-table [dataSource]="dataSource" class="leave-table mat-elevation-2">
          
          <!-- Leave Type Column -->
          <ng-container matColumnDef="leaveType">
            <th mat-header-cell *matHeaderCellDef>Leave Type</th>
            <td mat-cell *matCellDef="let leave">
              <mat-chip class="leave-type-chip">
                {{ leave.leaveTypeName }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- From Date Column -->
          <ng-container matColumnDef="fromDate">
            <th mat-header-cell *matHeaderCellDef>From Date</th>
            <td mat-cell *matCellDef="let leave">
              <div class="date-cell">
                <mat-icon class="date-icon">event</mat-icon>
                {{ leave.fromDate | date:'dd MMM yyyy' }}
                <span class="session-info" *ngIf="leave.fromSession">
                  <br><small>({{ leave.fromSession | titlecase }})</small>
                </span>
              </div>
            </td>
          </ng-container>

          <!-- To Date Column -->
          <ng-container matColumnDef="toDate">
            <th mat-header-cell *matHeaderCellDef>To Date</th>
            <td mat-cell *matCellDef="let leave">
              <div class="date-cell">
                <mat-icon class="date-icon">event</mat-icon>
                {{ leave.toDate | date:'dd MMM yyyy' }}
                <span class="session-info" *ngIf="leave.toSession">
                  <br><small>({{ leave.toSession | titlecase }})</small>
                </span>
              </div>
            </td>
          </ng-container>

          <!-- Reason Column -->
          <ng-container matColumnDef="reason">
            <th mat-header-cell *matHeaderCellDef>Reason</th>
            <td mat-cell *matCellDef="let leave">
              <span class="reason-text" [title]="leave.reason">
                {{ leave.reason.length > 30 ? (leave.reason | slice:0:30) + '...' : leave.reason }}
              </span>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let leave">
                <!-- <mat-icon *ngIf="leave.status === 'PENDING'"></mat-icon>
                <mat-icon *ngIf="leave.status === 'APPROVED'"></mat-icon>
                <mat-icon *ngIf="leave.status === 'REJECTED'"></mat-icon> -->
                {{ leave.status }}
            </td>
          </ng-container>

          <!-- Actions Column (Optional) -->
          <ng-container matColumnDef="actions" *ngIf="displayedColumns.includes('actions')">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let leave">
              <div class="action-buttons">
                <!-- <button mat-icon-button 
                        color="primary" 
                        (click)="viewDetails(leave)"
                        matTooltip="View Details">
                  <mat-icon>visibility</mat-icon>
                </button> -->
                
                <!-- <button mat-icon-button 
                        color="warn" 
                        (click)="cancelLeave(leave)"
                        matTooltip="Cancel Leave"
                        *ngIf="leave.status === 'PENDING'">
                  <mat-icon>cancel</mat-icon>
                </button> -->
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
              [ngClass]="'row-' + row.status.toLowerCase()"></tr>
        </table>

        <!-- No Data Message -->
        <div *ngIf="dataSource.data.length === 0" class="no-data">
          <mat-icon>inbox</mat-icon>
          <h3>No Pending Leaves</h3>
          <p>You don't have any pending leave applications.</p>
        </div>
      </div>

      <!-- Summary Card -->
      <mat-card class="summary-card" *ngIf="!isLoading && dataSource.data.length > 0">
        <mat-card-content>
          <div class="summary-content">
            <div class="summary-item">
              <mat-icon color="primary">pending_actions</mat-icon>
              <div class="summary-text">
                <h4>{{ dataSource.data.length }}</h4>
                <p>Pending Applications</p>
              </div>
            </div>
          
          </div>
        </mat-card-content>
      </mat-card>
    </mat-card-content>
  </mat-card>
  </div>