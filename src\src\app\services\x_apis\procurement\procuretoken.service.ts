import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Procuretoken, ProcuretokenResponse } from '../../../models/x_models/procurement/procuetoken';

@Injectable({
    providedIn: 'root'
})
export class ProcuretokenService {
    dataService = inject(DataService)

    create(data: Procuretoken) {
        return this.dataService.post<Response>("/procurement", data)
    }

    get() {
        return this.dataService.get<ProcuretokenResponse>("/procurement")
    }

    getById(id: number) {
        return this.dataService.get<Procuretoken>(`/procurement/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/procurement/${id}`)
    }
}
