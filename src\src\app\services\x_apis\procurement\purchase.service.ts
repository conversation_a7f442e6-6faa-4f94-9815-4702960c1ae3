import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { PurchaseList, PurchaseResponse } from '../../../models/x_models/procurement/purchase';
import { Response } from '../../../models/x_models/api-response';

@Injectable({
    providedIn: 'root'
})
export class PurchaseService {
    dataService = inject(DataService)

    create(data: PurchaseResponse) {
        return this.dataService.post<Response>("/purchase", data)
    }

    get() {
        return this.dataService.get<PurchaseList>("/purchase")
    }

    getById(id: number) {
        return this.dataService.get<PurchaseResponse>(`/purchase/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/purchase/${id}`)
    }
}
