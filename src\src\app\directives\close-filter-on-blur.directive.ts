import {Directive, ElementRef, HostListener, inject, input, model} from '@angular/core';
import {filter} from 'rxjs';

@Directive({
  selector: '[appCloseFilterOnBlur]'
})
export class CloseFilterOnBlurDirective {
  showFilters = model.required<boolean>()

  el = inject(ElementRef)

  @HostListener('window:click', ['$event'])
  onWindowClick(event: MouseEvent): void {
    this.onCardStatementHostClick(event)
  }

  constructor() {

  }

  onCardStatementHostClick(event: any) {
    if (this.el) {
      const clickedOutside = !this.el?.nativeElement.contains(event.target)
      const notOverlay = !event.target.classList.contains("cdk-overlay-backdrop")
      if (clickedOutside && notOverlay) {
        this.showFilters.set(false)
      }
    }
  }

}
