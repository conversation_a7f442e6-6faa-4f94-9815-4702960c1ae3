<div class="mapcard">
  <div class="content">

    <div class="grid-container">


      <!-- @if(mapIds() === 2) { -->
      <div class="row-8 map-grid">

        <div class="col-12" id="mapId" #mapId style="display:block;position: relative; height: 30rem;"></div>
        <!-- <div id="mapId" #mapId>
          <div class="map_icons_sec sidebtns pointer">
            <span class="resender_sec" matTooltip="test" matTooltipClass="tooltip-edit" matTooltipPosition="right">
              <a>
              </a>
            </span>
          </div>
        </div> -->
      </div>

     
      <div class="row-4 map-grid " style="padding: 20px;">
        <!-- <div class="vehicle-grid"> -->

          <div class="grid-container">
            <div class="row-4">
       
              <img [src]="getVehicleStatusImage(1)" [alt]="getVehicleStatusAltText(1)" class="status-icon" />
            </div>
            <div class="row-4" style="margin:auto">{{vehicleDetails().regNo}}</div>
            <div class="row-4" style="margin-left:auto;">
              <img [src]="getVehicleStatusImage(1)" [alt]="getVehicleStatusAltText(3)" class="status-icon" />
            </div>
          </div>
    
    
          <div class="grid-container" style="margin-top: 40px;">
            <div class="row-6 vehicle-lbl">Region</div>
            <div class="row-6 vehicle-txt" >{{vehicleDetails().regionName}}</div>
    
            <div class="row-6 vehicle-lbl">Vehicle Status</div>
            <div class="row-6 vehicle-txt">{{vehicleDetails().vehicleStatusTypeName}}</div>
    
            <div class="row-6 vehicle-lbl">Last Updated</div>
            <div class="row-6 vehicle-txt">{{vehicleDetails().gpsDeviceTs | date:'dd-MM-yyyy hh:mm:ss a' }}
            </div>
          </div>
    
          <div class="grid-container">
          <div class="gauge-wrapper ">
            <ngx-gauge [value]="gaugeValue" [label]="gaugeLabel" [append]="gaugeAppendText" [thresholds]="thresholdConfig"
              [thick]="10" [duration]="1000" [backgroundColor]="bgColor" [min]="0" [max]="150"
              style="display: flex;"></ngx-gauge>
          </div>
          </div>
    
      <!-- </div> -->
      </div>
    <!-- } -->

    </div>

   

</div>
</div>