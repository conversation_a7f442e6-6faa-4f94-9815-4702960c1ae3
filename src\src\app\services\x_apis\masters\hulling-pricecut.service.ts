import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { HullingPricecut, HullingPricecutResponse } from '../../../models/x_models/masters/hulling-pricecut';

@Injectable({
  providedIn: 'root'
})
export class HullingPricecutService {

  dataService = inject(DataService)

  create(data: HullingPricecut) {
    return this.dataService.post<Response>("/hullingpricecut", data)
  }

  get() {
    return this.dataService.get<HullingPricecutResponse>("/hullingpricecut")
  }

  getById(id: number) {
    return this.dataService.get<HullingPricecut>(`/hullingpricecut/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/hullingpricecut/${id}`)
  }

}
