<h2>Transfer Within Region</h2>
<form [formGroup]="transferForm" (ngSubmit)="onSubmit()">
  <mat-form-field appearance="outline">
    <mat-label>Current Position</mat-label>
    <input matInput formControlName="currentPosition" readonly>
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Proposed Department</mat-label>
    <mat-select formControlName="proposedDepartment">
      <mat-option *ngFor="let dept of departments" [value]="dept.id">{{ dept.name }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Reason for Transfer</mat-label>
    <textarea matInput formControlName="reason"></textarea>
  </mat-form-field>

  <button mat-raised-button color="accent" type="submit">Submit</button>
</form>
