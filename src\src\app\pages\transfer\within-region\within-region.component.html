<div class="container">
  <mat-card class="form-card">
    <mat-card-header class="card-header">
      <mat-card-title>
        <mat-icon class="header-icon">swap_horiz</mat-icon>
        <h1>Transfer Within Region</h1>
      </mat-card-title>
      <mat-card-subtitle>
        Request transfer to a different department within your current region
      </mat-card-subtitle>
    </mat-card-header>

    <mat-progress-bar
      *ngIf="isLoading"
      mode="indeterminate"
      class="progress-bar">
    </mat-progress-bar>

    <mat-card-content class="card-content">
      <form [formGroup]="transferForm" (ngSubmit)="onSubmit()" class="form-container">

        <!-- Current Information Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>info</mat-icon>
            Current Information
          </h3>
          <mat-divider></mat-divider>

          <div class="form-grid">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Employee Name</mat-label>
              <input matInput formControlName="employeeName" readonly>
              <mat-icon matSuffix>person</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Employee Code</mat-label>
              <input matInput formControlName="employeeCode" readonly>
              <mat-icon matSuffix>badge</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Current Region</mat-label>
              <input matInput formControlName="currentRegion" readonly>
              <mat-icon matSuffix>location_on</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Current Department</mat-label>
              <input matInput formControlName="currentDepartment" readonly>
              <mat-icon matSuffix>business</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Current Position</mat-label>
              <input matInput formControlName="currentPosition" readonly>
              <mat-icon matSuffix>work</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Current Grade</mat-label>
              <input matInput formControlName="currentGrade" readonly>
              <mat-icon matSuffix>grade</mat-icon>
            </mat-form-field>
          </div>
        </div>

        <!-- Transfer Request Details Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>assignment</mat-icon>
            Transfer Request Details
          </h3>
          <mat-divider></mat-divider>

          <div class="form-grid">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Proposed Department <span class="required-asterisk">*</span></mat-label>
              <mat-select formControlName="proposedDepartment" (selectionChange)="onDepartmentChange($event)">
                <mat-option *ngFor="let dept of departments" [value]="dept.id">
                  {{ dept.name }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>business</mat-icon>
              <mat-error *ngIf="transferForm.get('proposedDepartment')?.invalid && transferForm.get('proposedDepartment')?.touched">
                Please select a department
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Proposed Position</mat-label>
              <mat-select formControlName="proposedPosition">
                <mat-option *ngFor="let pos of availablePositions" [value]="pos.id">
                  {{ pos.title }} ({{ pos.grade }})
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>work</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Priority Level <span class="required-asterisk">*</span></mat-label>
              <mat-select formControlName="priority">
                <mat-option value="LOW">
                  <span class="priority-option low">Low</span>
                </mat-option>
                <mat-option value="MEDIUM">
                  <span class="priority-option medium">Medium</span>
                </mat-option>
                <mat-option value="HIGH">
                  <span class="priority-option high">High</span>
                </mat-option>
                <mat-option value="URGENT">
                  <span class="priority-option urgent">Urgent</span>
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>priority_high</mat-icon>
              <mat-error *ngIf="transferForm.get('priority')?.invalid && transferForm.get('priority')?.touched">
                Please select priority level
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Preferred Joining Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="preferredJoiningDate" [min]="minDate">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
        </div>

        <!-- Reason and Justification Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>description</mat-icon>
            Reason and Justification
          </h3>
          <mat-divider></mat-divider>

          <div class="form-grid">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Reason for Transfer <span class="required-asterisk">*</span></mat-label>
              <textarea
                matInput
                formControlName="reason"
                rows="4"
                placeholder="Please provide detailed reason for the transfer request">
              </textarea>
              <mat-icon matSuffix>description</mat-icon>
              <mat-hint>Minimum 10 characters required</mat-hint>
              <mat-error *ngIf="transferForm.get('reason')?.invalid && transferForm.get('reason')?.touched">
                {{getErrorMessage('reason')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Additional Justification</mat-label>
              <textarea
                matInput
                formControlName="justification"
                rows="3"
                placeholder="Any additional information that supports your transfer request">
              </textarea>
              <mat-icon matSuffix>note_add</mat-icon>
            </mat-form-field>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            mat-raised-button
            color="primary"
            type="submit"
            [disabled]="transferForm.invalid || isLoading"
            class="submit-btn">
            <mat-icon *ngIf="!isLoading">send</mat-icon>
            <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
            {{ isLoading ? 'Submitting...' : 'Submit Transfer Request' }}
          </button>

          <button
            mat-stroked-button
            color="accent"
            type="button"
            (click)="saveDraft()"
            [disabled]="isLoading"
            class="draft-btn">
            <mat-icon>save</mat-icon>
            Save as Draft
          </button>

          <button
            mat-button
            color="warn"
            type="button"
            (click)="resetForm()"
            [disabled]="isLoading"
            class="reset-btn">
            <mat-icon>refresh</mat-icon>
            Reset Form
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
