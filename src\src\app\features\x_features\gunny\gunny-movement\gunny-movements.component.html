@if (!showGrid()) {
  <mat-toolbar color="primary" class="navbar">
    <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
      <a routerLink="/" class="crumb">Gunny Management</a>
      <mat-icon>chevron_right</mat-icon>
      <span aria-current="page" class="nav-active">{{menuService.activeMenu()?.title}}</span>
    </nav>
  </mat-toolbar>

<div class="component card">
  <!-- <div class="page-header">
    <h1>{{menuService.activeMenu()?.title}}</h1>
  </div> -->



  <form [formGroup]="form">
    <div class="form">

      <div class="field">
        <label for="fromDate" class="required-label">{{'FromDate' | translate }}</label>
        <mat-form-field>
          <input matInput id="fromDate" formControlName="fromDate" [matDatepicker]="picker1"
            (dateChange)="onDateChange($event)">
          <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
          <mat-datepicker #picker1></mat-datepicker>
          <mat-error>
            <!-- @if(form.controls.fromDate.errors?.['required']) {
            {{'Date' | translate}} {{'IsRequired' | translate}}
            } -->
          </mat-error>
        </mat-form-field>
      </div>

      <div class="field">
        <label for="toDate" class="required-label">{{'ToDate' | translate }}</label>
        <mat-form-field>
          <input matInput id="toDate" formControlName="toDate" [matDatepicker]="picker2"
            (dateChange)="onDateChange($event)">
          <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
          <mat-datepicker #picker2></mat-datepicker>
          <mat-error>
            <!-- @if(form.controls.scannedDate.errors?.['required']) {
            {{'Date' | translate}} {{'IsRequired' | translate}}
            } -->
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </form>



  <div class="header">
    <div class="filters-wrapper">
      <mat-form-field class="search hide-subscript" appearance="outline">
        <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
        <input id="search" [formControl]="search" (input)="onSearch()" autocomplete="off" matInput
          placeholder="{{ 'Search' | translate }}">
      </mat-form-field>
      <div class="filters-more">
        @if(access()?.canAdd) {
        <button (click)="onAdd()" class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix>add</mat-icon>
        </button>
        }
        <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
        </button>
      </div>
    </div>
  </div>
  <div class="content">
    <div class="table-wrapper">
      <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">

        <ng-container matColumnDef="fromStorageLocationName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'From' | translate}} </th>
          <td mat-cell *matCellDef="let row"> {{row.fromStorageLocationName}} </td>
        </ng-container>

        <ng-container matColumnDef="toStorageLocationName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'To' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.toStorageLocationName}} </td>
        </ng-container>

        <ng-container matColumnDef="noOfBags">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'NoofBags' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.noOfBags }} </td>
        </ng-container>

        <ng-container matColumnDef="acknowledgementStatusTypeName" style="text-align: center;">
          <th mat-header-cell *matHeaderCellDef mat-sort-header style="text-align: center;">  {{'Status' | translate}} </th>
          <td mat-cell *matCellDef="let row"> <span class="status" style="text-align: center;"
          
            >{{row.acknowledgementStatusTypeName }}</span></td>
          <!-- <td mat-cell *matCellDef="let row"> <span class="status" style="text-align: center;"
            [ngStyle]="{
              'background': getStatusColor(row.acknowledgementStatusType)
            }" 
            >{{row.acknowledgementStatusTypeName }}</span></td> -->
        </ng-container>

        <!-- <ng-container matColumnDef="acknowledgementStatusTypeName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Status' | translate}} </th>
          <td mat-cell *matCellDef="let row"> {{row.acknowledgementStatusTypeName }} </td>
        </ng-container>

        <ng-container matColumnDef="toDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'ToDate' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.toDate | date :'dd-MM-yyyy' }} </td>
        </ng-container> -->

    
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef class="action"> {{ 'Actions' | translate }} </th>
          <td mat-cell *matCellDef="let row">

           
            <div class="table-controls">
              @if(row.acknowledgementStatusType == 1) {
              @if (access()?.canView) {
              <button title="{{ 'View' | translate }}" (click)="onView(row)" class="btn-icon btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
              </button>
              }

              @if(access()?.canEdit) {
              <button title="{{ 'Edit' | translate }}" (click)="onEdit(row)" class="btn-icon btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">edit</mat-icon>
              </button>
              }

              @if(access()?.canDelete) {
              <button title="{{ 'Delete' | translate }}" (click)="onDelete(row)"
                class="btn-icon btn-delete btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
              </button>
              }
            

              @if(access()?.canApprove) {
              <div class="dpc" (click)="sentDpc(row)">Send to DPC</div>
              }

            } 
            <!-- @else{
              {{row.acknowledgementStatusTypeName }}
            } -->

           
              <!-- @if(access()?.canDelete) {
              <button title="{{ 'Add' | translate }}" (click)="onScanAdd(row)" class="btn-icon btn-delete btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">add</mat-icon>
              </button>
              } -->
            </div>
          

          </td>
        </ng-container>
        

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" [attr.colspan]="displayedColumns.length" style="text-align: center;padding:10px">
            No data found
          </td>
        </tr>
        <!-- <tr *matNoDataRow>
          <ng-container *ngTemplateOutlet="shimmer"></ng-container>
        </tr> -->
      </table>

    </div>
    <!-- @if (dataSource().filteredData.length==0) {
      <div style="text-align: center;">No Data</div>

      } -->
    <mat-paginator class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
      aria-label="Select page of users"></mat-paginator>

    <div class="mobile-wrapper">
      <div class="un-cards">
        @for (item of dataSource().data; track item) {
        <div class="un-card">
          <div class="desc">
            <div class="quote-no">{{item.fromStorageLocationName}}</div>
            <div style="line-height: 1em;"><span class="tracking-no">{{item.toStorageLocationName}}</span> </div>
            <div class="quote-no">{{item.noOfBags}}</div>
            <!-- <div class="quote-no">{{item.toDate | date }}</div> -->
          </div>
          <div class="actions">
            <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu" aria-label="Card actions">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">

              @if (access()?.canView) {
              <button (click)="onView(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                <span>{{ 'View' | translate }}</span>
              </button>
              }

              @if(access()?.canEdit) {
              <button (click)="onEdit(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">edit</mat-icon>
                <span>{{ 'Edit' | translate }}</span>
              </button>
              }

              @if(access()?.canDelete) {
              <button (click)="onScanAdd(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">add</mat-icon>
                <span>{{ 'Add' | translate }}</span>
              </button>
              }

              @if(access()?.canDelete) {
              <button (click)="onScanAdd(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">add</mat-icon>
                <span>{{ 'Add' | translate }}</span>
              </button>
              }
              <!-- @if(access()?.canDelete) {
              <button (click)="onDelete(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
                <span>{{ 'Delete' | translate }}</span>
              </button>
              } -->
            </mat-menu>
          </div>
        </div>
        }
      </div>
    </div>
  </div>
</div>


}
<!-- @else if(scanner == 2)  {
<app-gunny-movement [cropId]="gunnyMovement().id" [mode]="mode()" (closed)="onClose()"></app-gunny-movement>

} -->

@else {
<app-gunny-movement [uniqueId]="gunnyMovement().id" [mode]="mode()" (closed)="onClose()"></app-gunny-movement>

}