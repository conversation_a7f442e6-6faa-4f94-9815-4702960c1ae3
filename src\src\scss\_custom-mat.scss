@use '@angular/material' as mat;

@include mat.app-background();
// Define the theme object.
$default-theme: mat.define-theme((
            color: (
                use-system-variables: true,
                system-variables-prefix: sys,
            ),
            // typography: (
            //     plain-family: Inter
            // ),
            density: (scale: 0,
            )));


:root {
    // --sys-background: #ffffff !important;
    // --sys-surface: #ffffff !important;
    @include mat.all-component-themes($default-theme);
}

mat-card.card {
  background-color: #fff !important;
  box-shadow: 0 0 10px 0 rgba(183, 192, 206, .2);
  border-radius: var(--4px);
  // border-width: 0;
  padding: 20px;
}

.mat-mdc-card {
  background-color:white !important ;
  // box-shadow: 0 5px 0 0 #0f8217, 0 0px 27px 0 rgba(0, 0, 0, 0.08) !important;
};

