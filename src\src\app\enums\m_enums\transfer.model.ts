export interface Region {
  id: string;
  name: string;
  code?: string;
  description?: string;
}

export interface Department {
  id: string;
  name: string;
  code?: string;
  regionId?: string;
  description?: string;
}

export interface Employee {
  id: string;
  name: string;
  position: string;
  region?: string;
  department?: string;
  employeeCode?: string;
  email?: string;
  mobileNumber?: string;
  joiningDate?: Date;
  currentGrade?: string;
  currentSalary?: number;
}

export interface Position {
  id: string;
  title: string;
  grade: string;
  department: string;
  region: string;
  description?: string;
}

export enum TransferType {
  WITHIN_REGION = 'WITHIN_REGION',
  INTER_REGION = 'INTER_REGION',
  MUTUAL = 'MUTUAL'
}

export enum TransferStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  APPROVED_BY_SUPERVISOR = 'APPROVED_BY_SUPERVISOR',
  APPROVED_BY_HR = 'APPROVED_BY_HR',
  APPROVED_BY_MANAGEMENT = 'APPROVED_BY_MANAGEMENT',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export interface TransferRequest {
  id: string;
  requestNumber: string;
  employeeId: string;
  employeeName: string;
  transferType: TransferType;
  status: TransferStatus;
  priority: Priority;

  // Current Details
  currentRegionId: string;
  currentRegionName: string;
  currentDepartmentId: string;
  currentDepartmentName: string;
  currentPosition: string;
  currentGrade: string;

  // Proposed Details
  proposedRegionId?: string;
  proposedRegionName?: string;
  proposedDepartmentId?: string;
  proposedDepartmentName?: string;
  proposedPosition?: string;
  proposedGrade?: string;

  // Transfer Details
  reason: string;
  justification?: string;
  preferredJoiningDate?: Date;
  relocationRequired?: boolean;
  relocationSupport?: boolean;

  // Mutual Transfer Specific
  mutualEmployeeId?: string;
  mutualEmployeeName?: string;
  mutualAgreementConfirmed?: boolean;

  // Workflow
  submittedDate?: Date;
  submittedBy: string;
  approvalWorkflow?: ApprovalStep[];

  // Additional Information
  attachments?: TransferAttachment[];
  comments?: TransferComment[];

  // Audit Fields
  createdDate: Date;
  createdBy: string;
  lastModifiedDate?: Date;
  lastModifiedBy?: string;
}

export interface ApprovalStep {
  id: string;
  stepNumber: number;
  approverRole: string;
  approverName?: string;
  approverId?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  comments?: string;
  actionDate?: Date;
  isRequired: boolean;
}

export interface TransferAttachment {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadDate: Date;
  uploadedBy: string;
  description?: string;
}

export interface TransferComment {
  id: string;
  comment: string;
  commentBy: string;
  commentDate: Date;
  isInternal: boolean;
}

export interface WithinRegionTransferRequest extends Omit<TransferRequest, 'transferType'> {
  transferType: TransferType.WITHIN_REGION;
  proposedRegionId: string; // Same as current region
  proposedRegionName: string;
}

export interface InterRegionTransferRequest extends Omit<TransferRequest, 'transferType'> {
  transferType: TransferType.INTER_REGION;
  proposedRegionId: string;
  proposedRegionName: string;
  relocationRequired: boolean;
  familyDetails?: FamilyMember[];
}

export interface MutualTransferRequest extends Omit<TransferRequest, 'transferType'> {
  transferType: TransferType.MUTUAL;
  mutualEmployeeId: string;
  mutualEmployeeName: string;
  mutualAgreementConfirmed: boolean;
  mutualEmployeeApproval?: boolean;
}

export interface FamilyMember {
  name: string;
  relationship: string;
  age: number;
  occupation?: string;
  schoolingRequired?: boolean;
}