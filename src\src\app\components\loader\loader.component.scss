.loader-bg {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #ffffffeb;
    z-index: 99999;
    opacity: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    flex-direction: column;
    gap: 10px;
}



.site-preloader__fade {
    transition: opacity .3s;
    opacity: 0;
}

.static-message {
    margin-bottom: 0px;

    margin-top: 5px;
    font-weight: 400;
    color: #46394b;
    font-size: 18px;
    text-align: center;
    text-wrap: balance;
}

.dynamic-message {
    position: relative;
    max-width: 700px;
    width: 100%;
    color: #46394b;
    font-weight: 500 !important;
    top: -5px;
    font-size: 20px;
}

.message {
    opacity: 0;
    animation-name: messageFadeInOut;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
    position: absolute;
    width: 100%;
    text-align: center;
    animation-duration: 48s;
}

.message1 {
    animation-name: message1FadeInOut;
}

.message2 {
    animation-name: message2FadeInOut;
}
.message3 {
    animation-name: message3FadeInOut;
}
.message4 {
    animation-name: message4FadeInOut;
}
.message5 {
    animation-name: message5FadeInOut;
}
.message6 {
    animation-name: message6FadeInOut;
}

@keyframes messageFadeInOut {
    0%, 16.7%, 33.3%, 50%, 66.6%, 83.3%, 95% { 
        opacity: 0; 
    }
    4%, 14%, 20.7%, 31%, 37.33%, 48%, 54%, 64%, 70.6%, 81%, 87.3%{
        opacity: 1;
    }
}

@keyframes message1FadeInOut {
    0%, 16.7% {
        opacity: 0;
    }
    4%, 14% {
        opacity: 1;
    }
}
@keyframes message2FadeInOut {
    0%, 16.7%, 33.3% {
        opacity: 0
    }
    20.7%, 31% {
        opacity: 1;
    }
}
@keyframes message3FadeInOut {
    0%, 33.3%, 50%, 95% { 
        opacity: 0; 
    }
    37.33%, 48% {
        opacity: 1
    }
}
@keyframes message4FadeInOut {
    0%, 50%, 66.6%, 95% { 
        opacity: 0; 
    }
    54%, 64% {
        opacity: 1
    }
}
@keyframes message5FadeInOut {
    0%, 66.6%, 83.3%, 95% { 
        opacity: 0; 
    }
    70.6%, 81% {
        opacity: 1
    }
}
@keyframes message6FadeInOut {
    0%, 83.3%  { 
        opacity: 0; 
    }
    87.3%, 95% {
        opacity: 1
    }
}


/* HTML: <div class="loader"></div> */
.loader {
    width: 50px;
    aspect-ratio: 1;
    border-radius: 50%;
    border: 5px solid #514b82;
    // border: 5px solid var(--green-80);
    animation:
      l20-1 0.8s infinite linear alternate,
      l20-2 1.6s infinite linear;
  }
  @keyframes l20-1{
     0%    {clip-path: polygon(50% 50%,0       0,  50%   0%,  50%    0%, 50%    0%, 50%    0%, 50%    0% )}
     12.5% {clip-path: polygon(50% 50%,0       0,  50%   0%,  100%   0%, 100%   0%, 100%   0%, 100%   0% )}
     25%   {clip-path: polygon(50% 50%,0       0,  50%   0%,  100%   0%, 100% 100%, 100% 100%, 100% 100% )}
     50%   {clip-path: polygon(50% 50%,0       0,  50%   0%,  100%   0%, 100% 100%, 50%  100%, 0%   100% )}
     62.5% {clip-path: polygon(50% 50%,100%    0, 100%   0%,  100%   0%, 100% 100%, 50%  100%, 0%   100% )}
     75%   {clip-path: polygon(50% 50%,100% 100%, 100% 100%,  100% 100%, 100% 100%, 50%  100%, 0%   100% )}
     100%  {clip-path: polygon(50% 50%,50%  100%,  50% 100%,   50% 100%,  50% 100%, 50%  100%, 0%   100% )}
  }
  @keyframes l20-2{ 
    0%    {transform:scaleY(1)  rotate(0deg)}
    49.99%{transform:scaleY(1)  rotate(135deg)}
    50%   {transform:scaleY(-1) rotate(0deg)}
    100%  {transform:scaleY(-1) rotate(-135deg)}
  }


