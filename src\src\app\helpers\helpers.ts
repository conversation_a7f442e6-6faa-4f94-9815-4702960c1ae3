import {FormControl, FormGroup, Validators} from '@angular/forms';
import {MatCheckboxChange} from '@angular/material/checkbox';
import {WritableSignal} from '@angular/core';
import {MatTableDataSource} from '@angular/material/table';

export function toTitleCase(sentence: string): string {
  if (!sentence) {
    return ""; // Handle empty or null input
  }

  return sentence
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export function formatCurrencyValue(amount: string) {
  const parts = amount.split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ','); // Apply comma formatting to the integer part.
  return parts.join('.');
}

export function markRequired(controls: FormControl): void;
export function markRequired(controls: FormControl[]): void;
export function markRequired(controls: string[], form: FormGroup): void;
export function markRequired(controls: FormControl | FormControl[] | string[], form?: FormGroup): void {
  if (Array.isArray(controls) && controls[0] instanceof FormControl) {
    (<FormControl[]>controls).forEach(control => {
      control.addValidators(Validators.required);
      control.updateValueAndValidity();
    });
  } else if (Array.isArray(controls) && typeof controls[0] === 'string' && form) {
    (<string[]>controls).forEach(controlName => {
      const control = form.get(controlName);
      if (control) {
        control.addValidators(Validators.required);
      }
    });
    form.updateValueAndValidity();
  } else if (controls instanceof FormControl) {
    controls.addValidators(Validators.required);
    controls.updateValueAndValidity();
  } else {
    console.error('Invalid arguments provided to the markRequired function.');
  }
}

export function markOptional(controls: FormControl): void;
export function markOptional(controls: FormControl[]): void;
export function markOptional(controls: string[], form: FormGroup): void;
export function markOptional(controls: FormControl | FormControl[] | string[], form?: FormGroup): void {
  if (Array.isArray(controls) && controls[0] instanceof FormControl) {
    (<FormControl[]>controls).forEach(control => {
      control.removeValidators(Validators.required);
      control.updateValueAndValidity();
    });
  } else if (Array.isArray(controls) && typeof controls[0] === 'string' && form) {
    (<string[]>controls).forEach(controlName => {
      const control = form.get(controlName);
      if (control) {
        control.removeValidators(Validators.required);
      }
    });
    form.updateValueAndValidity();
  } else if (controls instanceof FormControl) {
    controls.removeValidators(Validators.required);
    controls.updateValueAndValidity();
  } else {
    console.error('Invalid arguments provided to the markOptional function.');
  }
}

export function disable(controls: FormControl): void;
export function disable(controls: FormControl[]): void;
export function disable(controls: string[], form: FormGroup): void;
export function disable(controls: FormControl | FormControl[] | string[], form?: FormGroup): void {
  if (Array.isArray(controls) && controls[0] instanceof FormControl) {
    (<FormControl[]>controls).forEach(control => {
      control.disable();
      control.updateValueAndValidity();
    });
  } else if (Array.isArray(controls) && typeof controls[0] === 'string' && form) {
    (<string[]>controls).forEach(controlName => {
      const control = form.get(controlName);
      if (control) {
        control.disable();
      }
    });
    form.updateValueAndValidity();
  } else if (controls instanceof FormControl) {
    controls.disable();
    controls.updateValueAndValidity();
  } else {
    console.error('Invalid arguments provided to the markOptional function.');
  }
}

export function enable(controls: FormControl): void;
export function enable(controls: FormControl[]): void;
export function enable(controls: string[], form: FormGroup): void;
export function enable(controls: FormControl | FormControl[] | string[], form?: FormGroup): void {
  if (Array.isArray(controls) && controls[0] instanceof FormControl) {
    (<FormControl[]>controls).forEach(control => {
      control.enable();
      control.updateValueAndValidity();
    });
  } else if (Array.isArray(controls) && typeof controls[0] === 'string' && form) {
    (<string[]>controls).forEach(controlName => {
      const control = form.get(controlName);
      if (control) {
        control.enable();
      }
    });
    form.updateValueAndValidity();
  } else if (controls instanceof FormControl) {
    controls.enable();
    controls.updateValueAndValidity();
  } else {
    console.error('Invalid arguments provided to the markOptional function.');
  }
}

// ==========|| list select helper  ||=============
type ObjectWithSelected = { selected?: boolean; };

export function handleSelectAll<T extends ObjectWithSelected>(event: MatCheckboxChange, dataSource:  WritableSignal<MatTableDataSource<T>>) {
  dataSource().data.forEach((item: any) => {
    item.selected = event.checked;
  })
  return dataSource().data.filter(item => item.selected)
}

export function handleSelect<T extends ObjectWithSelected>(event: MatCheckboxChange, selected: T, dataSource:  WritableSignal<MatTableDataSource<T>>) {
  dataSource().data.forEach((item: any) => {
    if (selected === item) {
      item.selected = event.checked;
    }
  })
  return dataSource().data.filter(item => item.selected)
}
