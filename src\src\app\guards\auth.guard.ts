import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { SessionService } from '../services/session.service';
import {X_AppRoutes} from '../enums/x_enums/x_app-routes';
import {AppRoutes} from '../enums/app-routes';

export const isUserAuthenticated: CanActivateFn = (route, state) => {
  const sessionService = inject(SessionService)
  const router = inject(Router)

  if (sessionService.isLoggedIn() && !sessionService.isTokenExpired()) {
    return true
  } else {
    return router.parseUrl(AppRoutes.Login)
  }
};

export const isUserLoggedOut: CanActivateFn = (route, state) => {
  const sessionService = inject(SessionService)
  const router = inject(Router)

  if (sessionService.isLoggedIn() && !sessionService.isTokenExpired()) {
    return router.parseUrl(X_AppRoutes.Dashboard)
  } else {
    return true
  }
}
