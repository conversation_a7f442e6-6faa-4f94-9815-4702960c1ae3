import { Component, computed, inject, input, output, resource, signal } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { UnitService } from '../../../../services/x_apis/masters/unit.sevice';
import { Router } from '@angular/router';
import { Unit } from '../../../../models/x_models/masters/unit';
import { MatIconModule } from '@angular/material/icon';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-unit',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatFormFieldModule,
    TranslateModule,
    MatIconModule,
    MatToolbarModule
  ],
  templateUrl: './unit.component.html',
  styleUrl: './unit.component.scss'
})
export class UnitComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  unitService = inject(UnitService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);

  translate = inject(TranslateService);

  regions = resource({ loader: () => this.lookupService.getRegion() }).value;

  uniqueId = input.required<number>();
  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);

  router = inject(Router);
  unit = signal<Unit | null>(null);

  form = this.fb.group({
    id: this.fb.control<number>(0),
    unitName: this.fb.control<string>('', Validators.required),
    unitRegionalName: this.fb.control<string>('', Validators.required),
    regionId: this.fb.control<number | null>(null, Validators.required)
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });

    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData();
    }
  }

   readonly search = signal('');


  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );


  filter(value: any) {
    this.search.set(value.target.value);
  }

  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
  }

  async getFormData() {
    const res = await this.unitService.getById(this.uniqueId());
    this.unit.set(res);
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false);
      this.form.disable();
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true);
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.unitService.create(formValue as Unit)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Unit ${formValue.unitName} Created successfully.`)
        }
        else {
          this.alertService.success(`Unit ${formValue.unitName} Updated successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }
}
