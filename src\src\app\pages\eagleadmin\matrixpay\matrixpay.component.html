<div class="container">
    <h1 class="page-title">Employee Matrix Pay Management</h1>

    <!-- Form Section -->
    <mat-card class="form-section">
        <mat-card-header>
            <mat-card-title>

                Add New Employee
            </mat-card-title>
        </mat-card-header>

        <mat-card-content>
            <form [formGroup]="CreateForm" (ngSubmit)="submit()" class="employee-form">
                <div class="form-row">
                    <mat-form-field appearance="outline" class="form-field">
                        <mat-label>Employee Role</mat-label>
                        <input matInput formControlName="employeeRole" placeholder="e.g., Software Developer"
                            autocomplete="off">

                        <mat-error
                            *ngIf="CreateForm.controls['employeeRole'].touched && CreateForm.controls['employeeRole'].errors">
                            {{ getErrorMessage('Employee Role') }}
                        </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="form-field">
                        <mat-label>Matric ID</mat-label>
                        <input matInput formControlName="matrixId" placeholder="e.g., EMP001" autocomplete="off">

                        <mat-error
                            *ngIf="CreateForm.controls['matrixId'].touched && CreateForm.controls['matrixId'].errors">
                            {{ getErrorMessage('matrix ID') }}
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="form-row">
                    <mat-form-field appearance="outline" class="form-field">
                        <mat-label>Level</mat-label>
                        <input matInput formControlName="level" placeholder="e.g., 1" autocomplete="off">


                        <mat-error *ngIf="CreateForm.controls['level'].touched && CreateForm.controls['level'].errors">
                            {{ getErrorMessage('Level') }}
                        </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="form-field">
                        <mat-label>Basic Pay</mat-label>
                        <input matInput type="number" formControlName="basicPay" placeholder="0.00">
                        <span matPrefix>₹&nbsp;</span>

                        <mat-error
                            *ngIf="CreateForm.controls['basicPay'].touched && CreateForm.controls['basicPay'].errors">
                            {{ getErrorMessage('Basic Pay') }}
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="form-actions">
                    <button mat-raised-button color="primary" type="submit" [disabled]="isSubmitting">
                        <mat-icon *ngIf="!isSubmitting">save</mat-icon>
                        <mat-spinner *ngIf="isSubmitting" diameter="20"></mat-spinner>
                        {{ isSubmitting ? 'Submitting...' : 'Add MAtrix' }}
                    </button>

                    <button mat-stroked-button type="button" (click)="CreateForm.reset()" [disabled]="isSubmitting">
                        <mat-icon>clear</mat-icon>
                        Reset
                    </button>
                </div>
            </form>
        </mat-card-content>
    </mat-card>

    <!-- Table Section -->

</div>
<div class="container">
    <mat-card class="table-section">
        <mat-card-header>
            <mat-card-title>
                <mat-icon>people</mat-icon>
                Employee List
            </mat-card-title>
            <div class="header-actions">
                <button mat-icon-button (click)="refreshData()" [disabled]="isLoading" matTooltip="Refresh Data">
                    <mat-icon>refresh</mat-icon>
                </button>
            </div>
        </mat-card-header>

        <mat-card-content>
            <div *ngIf="isLoading" class="loading-container">
                <mat-spinner></mat-spinner>
                <p>Loading employees...</p>
            </div>
            <div *ngIf="!isLoading && dataSource.length > 0" class="table-container">
                <table mat-table [dataSource]="dataSource" class="mat-elevation-2 full-width-table" matSort>
                    <!-- S.No Column -->
                    <ng-container matColumnDef="position">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>S.No</th>
                        <td mat-cell *matCellDef="let element; let i = index">{{ i + 1 }}</td>
                    </ng-container>
                    <!-- Matrix ID Column -->
                    <ng-container matColumnDef="matrixId">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Matrix ID</th>
                        <td mat-cell *matCellDef="let element">{{ element.matrixId }}</td>
                    </ng-container>
                    <ng-container matColumnDef="employeeRole">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Employee Role</th>
                        <td mat-cell *matCellDef="let element">{{ element.employeeRole }}</td>
                    </ng-container>
                    <ng-container matColumnDef="level">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Level</th>
                        <td mat-cell *matCellDef="let element">{{ element.level }}</td>
                    </ng-container>
                    <ng-container matColumnDef="bascipay">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Basic Pay</th>
                        <td mat-cell *matCellDef="let element">{{ element.bascipay }}</td>
                    </ng-container>

                    <ng-container matColumnDef="Action">
                        <th mat-header-cell *matHeaderCellDef>Action</th>
                        <td mat-cell *matCellDef="let element">
                            <button mat-icon-button color="warn"
                                (click)="deleteMatrix(element.employeeRole, element.level)" matTooltip="Delete">
                                <mat-icon>delete</mat-icon>
                            </button>
                            <button mat-icon-button color="warn">
                               <!-- (click)="updateEmployee(element.employeeRole, element.level)" -->
                                <mat-icon>edit</mat-icon>
                            </button>
                        </td>
                    </ng-container>
                    <tr mat-header-row
                        *matHeaderRowDef="['position', 'matrixId','employeeRole','level','bascipay','Action']"></tr>
                    <tr mat-row
                        *matRowDef="let row; columns: ['position', 'matrixId','employeeRole','level','bascipay','Action'];">
                    </tr>
                </table>
            </div>



        </mat-card-content>
    </mat-card>
</div>