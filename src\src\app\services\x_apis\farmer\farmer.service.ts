import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { FarmerResponse, Farmers, VaoApprovalList } from '../../../models/x_models/farmer/farmer';

@Injectable({
    providedIn: 'root'
})
export class FarmerService {
    dataService = inject(DataService)

    create(data: Farmers) {
        return this.dataService.post<Response>("/farmer", data)
    }

    get() {
        return this.dataService.get<FarmerResponse>("/farmer")
    }

    getById(id: number) {
        return this.dataService.get<Farmers>(`/farmer/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/farmer/${id}`)
    }

     getVoaApprovalList(id:any) {
        return this.dataService.get<VaoApprovalList>(`/farmer/vaoapproval/${id}`)
    }
}
