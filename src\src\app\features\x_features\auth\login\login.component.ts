import { Component, inject, resource, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { Router, RouterModule } from '@angular/router';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { AlertService } from '../../../../services/alert.service';
import { SessionService } from '../../../../services/session.service';
import { DataService } from '../../../../services/data.service';
import Swal from 'sweetalert2';
import { MatDialog } from '@angular/material/dialog';
import { UserService } from '../../../../services/x_apis/user.service';
import { AuthService } from '../../../../services/x_apis/auth.service';
import { LoginPayload } from '../../../../models/x_models/auth';
import { X_AppRoutes } from '../../../../enums/x_enums/x_app-routes';
import { MenuService } from '../../../../services/x_apis/menu.service';

@Component({
  selector: 'app-login',
  imports: [ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  router = inject(Router);
  sessionService = inject(SessionService);
  authService = inject(AuthService)
  alert = inject(AlertService);
  fb = inject(FormBuilder);
  hide = signal(true);
  dialog = inject(MatDialog);
  menuService = inject(MenuService);

  menu = signal<any[]>([])
  // menu = resource({ loader: () => this.menuService.get() }).value

  loginForm = this.fb.group({
    username: ['', [Validators.required]],
    password: ['', [Validators.required]],
  });

  show: boolean = true;

  clickEvent(event: MouseEvent) {
    this.hide.set(!this.hide());
    event.stopPropagation();
  }

  async onForgotPassword() {
    // const dialog = await openForgotPasswordDialog(this.dialog);
  }

  async getMenu() {
    const res = await this.menuService.get()
    this.menu.set(res);
      const menu: any = this.menu()

     if (menu[0]?.submenu.length == 0) {
          this.router.navigate([menu[0]?.path]);
        }
        else {
          this.router.navigate([menu[0]?.submenu[0].path])
        }

  }

  async onLogin() {
    if (this.loginForm.valid) {
      const { username, password } = this.loginForm.value;
      const payload: LoginPayload = {
        timeZone: new Intl.DateTimeFormat().resolvedOptions().timeZone,
        username: username!,
        password: password!,
        isForceLogout: false
      }
      try {
        const res = await this.authService.login(payload);
        this.getMenu();
        debugger
     const res1=await this.authService.userDetails();
     this.sessionService.updateSessionDataWithUserDetails(res1);
       
        // void this.router.navigate([X_AppRoutes.Dashboard]);
      } catch (err: any) {
        if (err?.status == 409) {
          const result = await Swal.fire({
            title: 'Confirmation',
            text: 'Another user has logged into your account, Do you want to force login',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes',
            cancelButtonText: 'No',
            customClass: {
              confirmButton: 'btn-primary',
              cancelButton: 'btn-secondary',
            },
          });

          if (result.value) {
            void this.forceLogin();
          }
        } else {
          console.log(err);
        }
      }
    }
  }

  async forceLogin() {
    const { username, password } = this.loginForm.value;
    const payload: LoginPayload = {
      timeZone: new Intl.DateTimeFormat().resolvedOptions().timeZone,
      username: username!,
      password: password!,
      isForceLogout: true
    }
    const res = await this.authService.login(payload);
     this.getMenu();
     debugger
     const res1=await this.authService.userDetails();
     this.sessionService.updateSessionDataWithUserDetails(res1);

    // void this.router.navigate([X_AppRoutes.Dashboard]);
  }
}
