@if (!showGrid()) {
<div class="component card">
  <div class="page-header">
    <h1>{{menuService.activeMenu()?.title}}</h1>
  </div>
  <div class="header">
    <div class="filters-wrapper">
      <mat-form-field class="search hide-subscript" appearance="outline">
        <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
        <input id="search" [formControl]="search" (input)="onSearch()" autocomplete="off" matInput placeholder="{{ 'Search' | translate }}">
      </mat-form-field>
      <div class="filters-more">
        @if(access()?.canAdd) {
        <button (click)="onAdd()" class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix>add</mat-icon>
        </button>
        }
        <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
        </button>
      </div>
    </div>
  </div>
  <div class="content">
    <div class="table-wrapper">
      <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">

        <ng-container matColumnDef="truckMemoId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'TruckMemoId' | translate}} </th>
          <td mat-cell *matCellDef="let row"> {{row.truckMemoId}} </td>
        </ng-container>

        <ng-container matColumnDef="truckMemoDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'TruckMemoDate' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.truckMemoDate }} </td>
        </ng-container>

        <ng-container matColumnDef="truckFrom">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'TruckFrom' | translate}} </th>
          <td mat-cell *matCellDef="let row"> {{row.truckFrom | date :'dd-MM-yyyy' }} </td>
        </ng-container>

        <ng-container matColumnDef="product">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'Product' | translate}} </th>
          <td mat-cell *matCellDef="let row"> {{row.product}} </td>
        </ng-container>

        <ng-container matColumnDef="vehicleNo">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'VehicleNo' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.vehicleNo }} </td>
        </ng-container>

        <ng-container matColumnDef="totalQty">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'TotalQty' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.totalQty }} </td>
        </ng-container>

        <ng-container matColumnDef="noOfBags">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'NoOfBags' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.noOfBags }} </td>
        </ng-container>

        <ng-container matColumnDef="gunnyType">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'GunnyType' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.gunnyType }} </td>
        </ng-container>

        <ng-container matColumnDef="moisture">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Moisture' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.moisture }} </td>
        </ng-container>

        <ng-container matColumnDef="storageTypeFrom">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'StorageTypeFrom' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.storageTypeFrom }} </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef class="action"> {{ 'Actions' | translate }} </th>
          <td mat-cell *matCellDef="let row">

            <div class="table-controls">
              @if (access()?.canView) {
              <button title="{{ 'View' | translate }}" (click)="onView(row)" class="btn-icon btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
              </button>
              }

              @if(access()?.canEdit) {
              <button title="{{ 'Edit' | translate }}" (click)="onEdit(row)" class="btn-icon btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">edit</mat-icon>
              </button>
              }

              @if(access()?.canDelete) {
              <button title="{{ 'Delete' | translate }}" (click)="onDelete(row)" class="btn-icon btn-delete btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
              </button>
              }
            </div>

          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" [attr.colspan]="displayedColumns.length" style="text-align: center;padding:10px">
            No data found
          </td>
        </tr>
        <!-- <tr *matNoDataRow>
          <ng-container *ngTemplateOutlet="shimmer"></ng-container>
        </tr> -->
      </table>
      
    </div>
    <!-- @if (dataSource().filteredData.length==0) {
      <div style="text-align: center;">No Data</div>

      } -->
      <mat-paginator class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
        aria-label="Select page of users"></mat-paginator>
        
    <div class="mobile-wrapper">
      <div class="un-cards">
        @for (item of dataSource().data; track item) {
        <div class="un-card">
          <div class="desc">
            <div class="quote-no">{{item.truckMemoId}}</div>
            <div style="line-height: 1em;"><span class="tracking-no">{{item.truckMemoDate | date}}</span> </div>
            <div class="quote-no">{{item.product}}</div>
            <div class="quote-no">{{item.noOfBags }}</div>
          </div>
          <div class="actions">
            <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu" aria-label="Card actions">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">

              @if (access()?.canView) {
              <button (click)="onView(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                <span>{{ 'View' | translate }}</span>
              </button>
              }

              @if(access()?.canEdit) {
              <button (click)="onEdit(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">edit</mat-icon>
                <span>{{ 'Edit' | translate }}</span>
              </button>
              }

              @if(access()?.canDelete) {
              <button (click)="onDelete(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
                <span>{{ 'Delete' | translate }}</span>
              </button>
              }
            </mat-menu>
          </div>
        </div>
        }
      </div>
    </div>
  </div>
</div>


} @else {
<app-truckform [uniqueId]="list().id" [mode]="mode()" (closed)="onClose()"></app-truckform>

}