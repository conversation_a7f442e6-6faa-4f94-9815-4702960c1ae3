<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(regionId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{region()?.regionName}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

  <div class="card component">
    
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="regionName" class="required-label">{{'Region' | translate}}</label>
          <mat-form-field>
            <input id="regionName" formControlName="regionName" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.regionName.errors?.['required']) {
              <!-- Region is required -->
              {{'Region' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="regionRegionalName" class="required-label">{{'Region' | translate}} {{'InTamil' | translate}}</label>
          <mat-form-field>
            <input id="regionRegionalName" formControlName="regionRegionalName" matInput
              (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
            <mat-error>
              @if(form.controls.regionRegionalName.errors?.['required']) {
              <!-- Region in tamil is required -->
              {{'Region' | translate}} {{'InTamil' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="regionLgdCode" class="required-label">{{'Region' | translate}} {{'LGDCode' | translate}}</label>
          <mat-form-field>
            <input id="regionLgdCode" formControlName="regionLgdCode" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.regionLgdCode.errors?.['required']) {
              <!-- Region LGD code is required -->
              {{'Region' | translate}} {{'LGDCode' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="deltaTypeId" class="required-label">{{"DeltaType" | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="deltaTypeId" formControlName="deltaTypeId">
               <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (role of filteredDeltas(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.deltaTypeId.errors?.['required']) {
              <!-- Delta type is required -->
              {{'DeltaType' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

      </div>

      <div class="actions">
      
        @if(editable() === true) {
          @if(mode() === 'add') {
            <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' | translate}}</button>
            }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{regionId() == 0
          ? ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>


    </form>
  </div>
