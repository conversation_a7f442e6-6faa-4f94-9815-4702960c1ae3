import { HashLocationStrategy, LocationStrategy, PathLocationStrategy } from '@angular/common';
import { Component } from '@angular/core';

@Component({
  selector: 'app-nav',
  imports: [],
  providers: [{ provide: LocationStrategy, useClass: HashLocationStrategy }],
  templateUrl: './nav.component.html',
  styleUrl: './nav.component.scss'
})
export class NavComponent {
  constructor(private location: Location) {}

  goBack(): void {
    // this.location.back();
  }
}
