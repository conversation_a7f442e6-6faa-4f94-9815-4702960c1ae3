.bg-img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.component {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: end;
  // max-width: 350px;
  margin-left: auto;
  width: 100%;
  
  @media (max-width: 800px) {
    justify-content: center;
    padding-right: 0;
    padding: 20px;
  }
  
  .login-form {
    position: relative;
    top: -20px;
    width: 100%;
    max-width: 350px;
    margin-right: 7%;
  }
}

mat-card {
  padding: var(--15px);
  width: 100%;
}

mat-card-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

mat-card-actions {
  justify-content: space-between;
}

mat-card-content {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  margin-block: 10px;
}

mat-form-field {
  max-width: 100%;
}