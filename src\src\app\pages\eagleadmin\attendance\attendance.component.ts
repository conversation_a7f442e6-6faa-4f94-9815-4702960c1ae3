import { Component, OnInit, inject, ViewChild, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';

import { CommonModule } from '@angular/common';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';


import { signal, computed } from '@angular/core';

import { AttendanceFormComponent } from './attendance-form.component';
import { PayrollService } from '../../../services/m_apis/service/payroll.service';
import { ExcelService } from '../../../services/x_apis/excel.service';
import { AlertService } from '../../../services/alert.service';
import { confirmAndDelete } from '../../../models/x_models/masters/shared';

// Employee interface based on payroll data structure
export interface Employee {
  empId: string;
  employeeName: string;
  department: string;
  employeeRole: string;
  grade: string;
  panNumber: string;
  pfNumber: string;
  specialPayType: string;
}

// Attendance record interface
export interface AttendanceRecord {
  empId: string;
  empName: string;
  category: string;
  department: string;
  attendanceDate: string;
  inTime: string;
  outTime: string;
  lateIn: string;
  lateOut: string;
  inTimeLatitude?: number;
  inTimeLongitude?: number;
  outTimeLatitude?: number;
  outTimeLongitude?: number;
  zoneName?: string;
  status: 'Present' | 'Absent' | 'Leave' | 'Late';
}

@Component({
  selector: 'app-attendance',
  imports: [
    CommonModule,
    MatTableModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatPaginatorModule,
    MatSortModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    ReactiveFormsModule,
    AttendanceFormComponent
  ],
  templateUrl: './attendance.component.html',
  styleUrl: './attendance.component.scss'
})
export class AttendanceComponent implements OnInit, AfterViewInit {

  // ViewChild references
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  // Injected services
  excelService = inject(ExcelService);
  alertService = inject(AlertService);

  // Employee data from payroll service
  employees: Employee[] = [];

  // Attendance data
  attendanceDataSource = new MatTableDataSource<AttendanceRecord>([]);
  attendanceDisplayedColumns: string[] = [
    'sno', 'empId', 'empName', 'category', 'designation', 'attendanceDate',
    'inTime', 'outTime', 'status', 'actions'
  ];

  // Summary counts
  attendancePresentCount: number = 0;
  attendanceAbsentCount: number = 0;
  attendanceLeaveCount: number = 0;
  attendanceTotalEmployees: number = 0;

  // Current year for footer
  currentYear: number = new Date().getFullYear();

  // Modal and mode management
  showAttendanceForm = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  selectedAttendanceRecord = signal<AttendanceRecord | null>(null);

  // Filter properties
  attendanceStatusFilterControl = new FormControl([]);
  employeeSearchControl = new FormControl('');
  designationFilterControl = new FormControl('');
  dateRangeControl = new FormControl('TODAY');
  selectedDateRange: string = 'TODAY';
  selectedEmployeeChips: any[] = [];
  selectedDepartments: any[] = [];
  selectedDesignations: any[] = [];
  selectedAttendanceStatuses: any[] = [];

  // Date picker properties
  startDate: Date | null = null;
  endDate: Date | null = null;
  maxDate: Date = new Date();

  // Custom date range properties
  customFromDate: Date | null = null;
  customToDate: Date | null = null;

  // Date range options
  dateRangeOptions: string[] = ['TODAY', 'YESTERDAY', 'THIS_WEEK', 'THIS_MONTH',  'CUSTOM'];
  dateRangeLabels: { [key: string]: string } = {
    'TODAY': 'Today',
    'YESTERDAY': 'Yesterday',
    'THIS_WEEK': 'This Week',
    'THIS_MONTH': 'This Month',
    'CUSTOM': 'Custom Range'
  };

  // Filter options
  designations: string[] = [];
  allDesignations: string[] = ['Software Developer', 'HR Manager', 'Accountant', 'System Analyst', 'Operations Manager', 'Marketing Manager', 'Financial Analyst', 'HR Specialist'];

  constructor(
    private router: Router,
    private payrollService: PayrollService
  ) {}

  ngOnInit() {
    console.log('Attendance component ngOnInit');
    console.log('Date range options:', this.dateRangeOptions);
    console.log('Date range labels:', this.dateRangeLabels);
    this.loadEmployeeData();
    this.setupFilters();
  }

  ngAfterViewInit() {
    // Use setTimeout to ensure ViewChild elements are properly initialized
    setTimeout(() => {
      this.setupDataSourcePagination();
    });
  }

  private setupDataSourcePagination() {
    if (this.paginator && this.sort) {
      this.attendanceDataSource.paginator = this.paginator;
      this.attendanceDataSource.sort = this.sort;
      console.log('Pagination setup completed. Data length:', this.attendanceDataSource.data.length);
    } else {
      console.log('Paginator or Sort not available yet');
    }
  }

  setupFilters() {
    // Setup search filter
    this.employeeSearchControl.valueChanges.subscribe(value => {
      this.applyFilters();
    });

    // Setup designation filter
    this.designationFilterControl.valueChanges.subscribe(value => {
      this.applyFilters();
    });

    // Setup date range filter
    this.dateRangeControl.valueChanges.subscribe(value => {
      this.selectedDateRange = value || 'TODAY';
      this.applyDateFilter();
    });
  }

  navigateToPayroll() {
    this.router.navigateByUrl("/main/payroll");
  }

  navigateToAttendance() {
    this.router.navigateByUrl("/main/attendance");
  }

  // Load employee data from payroll service
  loadEmployeeData() {
    this.payrollService.getList().subscribe({
      next: (data: any) => {
        this.employees = data.responseData || [];
        this.attendanceTotalEmployees = this.employees.length;
        this.populateDesignations();
        this.generateSampleAttendanceData();
      },
      error: (error) => {
        console.error('Error loading employee data:', error);
        // Generate sample data if API fails
        this.generateFallbackEmployeeData();
        this.populateDesignations();
        this.generateSampleAttendanceData();
      }
    });
  }

  // Generate fallback employee data if API fails
  generateFallbackEmployeeData() {
    this.employees = [
      {
        empId: 'EMP001',
        employeeName: 'John Doe',
        department: 'IT',
        employeeRole: 'Software Developer',
        grade: 'A',
        panNumber: '**********',
        pfNumber: 'PF001',
        specialPayType: 'Regular'
      },
      {
        empId: 'EMP002',
        employeeName: 'Jane Smith',
        department: 'HR',
        employeeRole: 'HR Manager',
        grade: 'B',
        panNumber: '**********',
        pfNumber: 'PF002',
        specialPayType: 'Regular'
      },
      {
        empId: 'EMP003',
        employeeName: 'Mike Johnson',
        department: 'Finance',
        employeeRole: 'Accountant',
        grade: 'A',
        panNumber: '**********',
        pfNumber: 'PF003',
        specialPayType: 'Regular'
      },
      {
        empId: 'EMP004',
        employeeName: 'Sarah Wilson',
        department: 'IT',
        employeeRole: 'System Analyst',
        grade: 'A',
        panNumber: '**********',
        pfNumber: 'PF004',
        specialPayType: 'Regular'
      },
      {
        empId: 'EMP005',
        employeeName: 'David Brown',
        department: 'Operations',
        employeeRole: 'Operations Manager',
        grade: 'B',
        panNumber: '**********',
        pfNumber: 'PF005',
        specialPayType: 'Regular'
      },
      {
        empId: 'EMP006',
        employeeName: 'Lisa Garcia',
        department: 'Marketing',
        employeeRole: 'Marketing Manager',
        grade: 'B',
        panNumber: '**********',
        pfNumber: 'PF006',
        specialPayType: 'Regular'
      },
      {
        empId: 'EMP007',
        employeeName: 'Robert Taylor',
        department: 'Finance',
        employeeRole: 'Financial Analyst',
        grade: 'A',
        panNumber: '**********',
        pfNumber: 'PF007',
        specialPayType: 'Regular'
      },
      {
        empId: 'EMP008',
        employeeName: 'Jennifer Lee',
        department: 'HR',
        employeeRole: 'HR Specialist',
        grade: 'A',
        panNumber: '**********',
        pfNumber: 'PF008',
        specialPayType: 'Regular'
      }
    ];
    this.attendanceTotalEmployees = this.employees.length;
  }

  // Populate designations from employee data
  populateDesignations() {
    const uniqueDesignations = new Set<string>();

    // Add designations from actual employee data
    this.employees.forEach(employee => {
      if (employee.employeeRole) {
        uniqueDesignations.add(employee.employeeRole);
      }
    });

    // Add any additional designations from the predefined list
    this.allDesignations.forEach(designation => {
      uniqueDesignations.add(designation);
    });

    this.designations = Array.from(uniqueDesignations).sort();
  }

  // Generate sample attendance data based on employee data and date range
  generateSampleAttendanceData() {
    const attendanceRecords: AttendanceRecord[] = [];
    const dateRange = this.getDateRangeForFilter();

    // Sample attendance statuses with different probabilities for different date ranges
    const statuses: ('Present' | 'Absent' | 'Leave' | 'Late')[] = ['Present', 'Present', 'Present', 'Late', 'Absent', 'Leave'];

    // Sample locations
    const locations = [
      { lat: 17.4065, lng: 78.4772, zone: 'Hyderabad Office' },
      { lat: 17.4125, lng: 78.4825, zone: 'Gachibowli Branch' },
      { lat: 17.3850, lng: 78.4867, zone: 'Madhapur Office' }
    ];

    // Generate records for each date in the range
    dateRange.forEach((date, dateIndex) => {
      this.employees.forEach((employee, empIndex) => {
        // Create variation in attendance based on date and employee
        const statusIndex = (dateIndex + empIndex) % statuses.length;
        const status = statuses[statusIndex];
        const location = locations[empIndex % locations.length];

        // Generate random times with some variation based on date
        const baseInHour = 9 + (dateIndex % 2); // Vary between 9-10 AM
        const inMinute = Math.floor(Math.random() * 60);
        const baseOutHour = 17 + (dateIndex % 3); // Vary between 5-7 PM
        const outMinute = Math.floor(Math.random() * 60);

        const inTime = status !== 'Absent' ? `${baseInHour.toString().padStart(2, '0')}:${inMinute.toString().padStart(2, '0')}` : '';
        const outTime = status === 'Present' || status === 'Late' ? `${baseOutHour.toString().padStart(2, '0')}:${outMinute.toString().padStart(2, '0')}` : '';

        // Calculate late in/out
        const lateIn = status === 'Late' ? '00:15' : '';
        const lateOut = '';

        const record: AttendanceRecord = {
          empId: employee.empId,
          empName: employee.employeeName,
          category: employee.department,
          department: employee.employeeRole,
          attendanceDate: this.formatDateToString(date),
          inTime: inTime,
          outTime: outTime,
          lateIn: lateIn,
          lateOut: lateOut,
          inTimeLatitude: status !== 'Absent' ? location.lat : undefined,
          inTimeLongitude: status !== 'Absent' ? location.lng : undefined,
          outTimeLatitude: status === 'Present' || status === 'Late' ? location.lat : undefined,
          outTimeLongitude: status === 'Present' || status === 'Late' ? location.lng : undefined,
          zoneName: status !== 'Absent' ? location.zone : undefined,
          status: status
        };

        attendanceRecords.push(record);
      });
    });

    this.attendanceDataSource.data = attendanceRecords;
    this.updateAttendanceCounts();
    this.setupDataSourcePagination();
  }

  // Get date range based on selected filter
  getDateRangeForFilter(): Date[] {
    const today = new Date();
    const dates: Date[] = [];

    switch (this.selectedDateRange) {
      case 'TODAY':
        dates.push(new Date(today));
        break;
      case 'YESTERDAY':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        dates.push(yesterday);
        break;
      case 'THIS_WEEK':
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        for (let i = 0; i < 7; i++) {
          const date = new Date(startOfWeek);
          date.setDate(startOfWeek.getDate() + i);
          dates.push(date);
        }
        break;
      case 'LAST_WEEK':
        const lastWeekStart = new Date(today);
        lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
        for (let i = 0; i < 7; i++) {
          const date = new Date(lastWeekStart);
          date.setDate(lastWeekStart.getDate() + i);
          dates.push(date);
        }
        break;
      case 'THIS_MONTH':
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        for (let d = new Date(startOfMonth); d <= endOfMonth; d.setDate(d.getDate() + 1)) {
          dates.push(new Date(d));
        }
        break;
      case 'LAST_MONTH':
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
        for (let d = new Date(lastMonthStart); d <= lastMonthEnd; d.setDate(d.getDate() + 1)) {
          dates.push(new Date(d));
        }
        break;
      case 'CUSTOM':
        if (this.customFromDate && this.customToDate) {
          const fromDate = this.getLocalDate(this.customFromDate);
          const toDate = this.getLocalDate(this.customToDate);
          for (let d = new Date(fromDate); d <= toDate; d.setDate(d.getDate() + 1)) {
            dates.push(new Date(d));
          }
        } else if (this.startDate && this.endDate) {
          const fromDate = this.getLocalDate(this.startDate);
          const toDate = this.getLocalDate(this.endDate);
          for (let d = new Date(fromDate); d <= toDate; d.setDate(d.getDate() + 1)) {
            dates.push(new Date(d));
          }
        } else {
          dates.push(new Date(today));
        }
        break;
      default:
        dates.push(new Date(today));
    }

    return dates;
  }

  // Update attendance summary counts
  updateAttendanceCounts() {
    const data = this.attendanceDataSource.data;
    this.attendancePresentCount = data.filter(record => record.status === 'Present' || record.status === 'Late').length;
    this.attendanceAbsentCount = data.filter(record => record.status === 'Absent').length;
    this.attendanceLeaveCount = data.filter(record => record.status === 'Leave').length;
    this.attendanceTotalEmployees = data.length;
  }

  // Utility methods for template
  formatDateForDisplay(date: string): string {
    if (!date) return '-';
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-GB');
  }

  // Helper method to get local date without timezone issues
  private getLocalDate(date: Date): Date {
    // Create a new date using the local timezone components
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();
    return new Date(year, month, day);
  }

  // Helper method to format date to YYYY-MM-DD string in local timezone
  private formatDateToString(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  formatTimeForDisplay(time: string): string {
    if (!time) return '-';
    return time;
  }

  getLocationText(lat?: number, lng?: number, zoneName?: string): string {
    if (zoneName) return zoneName;
    if (lat && lng) return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
    return '-';
  }

  getStatusColor(record: AttendanceRecord): string {
    switch (record.status) {
      case 'Present': return '#4CAF50';
      case 'Late': return '#FF9800';
      case 'Absent': return '#F44336';
      case 'Leave': return '#2196F3';
      default: return '#757575';
    }
  }

  getStatusIcon(record: AttendanceRecord): string {
    switch (record.status) {
      case 'Present': return 'check_circle';
      case 'Late': return 'schedule';
      case 'Absent': return 'cancel';
      case 'Leave': return 'event_busy';
      default: return 'help';
    }
  }

  getStatusIconClass(record: AttendanceRecord): string {
    return `status-${record.status.toLowerCase()}`;
  }

  getStatusText(record: AttendanceRecord): string {
    return record.status;
  }

  // Action methods similar to devices component
  onAdd() {
    this.selectedAttendanceRecord.set({
      empId: '',
      empName: '',
      category: '',
      department: '',
      attendanceDate: new Date().toISOString().split('T')[0],
      inTime: '',
      outTime: '',
      lateIn: '',
      lateOut: '',
      status: 'Present'
    });
    this.mode.set('add');
    this.showAttendanceForm.set(true);
  }

  onView(record: AttendanceRecord) {
    this.selectedAttendanceRecord.set(record);
    this.mode.set('view');
    this.showAttendanceForm.set(true);
  }

  onEdit(record: AttendanceRecord) {
    // Check if record is editable
    if (!this.isAttendanceEditable(record)) {
      this.alertService.warning('Can only edit today\'s attendance records');
      return;
    }

    this.selectedAttendanceRecord.set(record);
    this.mode.set('edit');
    this.showAttendanceForm.set(true);
  }

  onClose() {
    this.showAttendanceForm.set(false);
    this.loadEmployeeData();
  }

  onRefresh() {
    this.loadEmployeeData();
  }

  onFormSave(record: AttendanceRecord) {
    const currentData = this.attendanceDataSource.data;

    if (this.mode() === 'add') {
      // Add new record
      currentData.push(record);
      this.alertService.success('Attendance record added successfully');
    } else if (this.mode() === 'edit') {
      // Update existing record
      const index = currentData.findIndex(r =>
        r.empId === record.empId && r.attendanceDate === record.attendanceDate
      );
      if (index !== -1) {
        currentData[index] = record;
        this.alertService.success('Attendance record updated successfully');
      }
    }

    this.attendanceDataSource.data = [...currentData];
    this.updateAttendanceCounts();
    this.setupDataSourcePagination();
    this.showAttendanceForm.set(false);
  }

  onFormCancel() {
    this.showAttendanceForm.set(false);
  }

  onFormEdit() {
    this.mode.set('edit');
  }

  async onDelete(record: AttendanceRecord) {
    // Check if record is editable
    if (!this.isAttendanceEditable(record)) {
      this.alertService.warning('Can only delete today\'s attendance records');
      return;
    }

    // Create a record with id for confirmAndDelete compatibility
    const recordWithId = { ...record, id: `${record.empId}-${record.attendanceDate}` };

    // Create a mock service-like object for confirmAndDelete
    const mockService = {
      delete: async (id: any) => {
        // Simulate deletion by removing from data source
        const currentData = this.attendanceDataSource.data;
        const filteredData = currentData.filter(r =>
          !(r.empId === record.empId && r.attendanceDate === record.attendanceDate)
        );
        this.attendanceDataSource.data = filteredData;
        this.updateAttendanceCounts();
        this.setupDataSourcePagination();
        this.alertService.success('Attendance record deleted successfully');
        return { isSuccess: true };
      }
    };

    await confirmAndDelete(
      recordWithId,
      `${record.empName} - ${this.formatDateForDisplay(record.attendanceDate)}`,
      'Attendance Record',
      mockService,
      () => this.loadEmployeeData()
    );
  }

  // Legacy methods for backward compatibility
  addAttendance() {
    this.onAdd();
  }

  refreshAttendanceData() {
    this.onRefresh();
  }

  exportAttendance() {
    const resultdata = this.attendanceDataSource.data;
    if (!resultdata || resultdata.length === 0) {
      this.alertService.info("No data available to export");
      return;
    }

    const excelList: any[] = [];
    const excelColumns = [
      "S.NO",
      "Employee ID",
      "Employee Name",
      "Category",
      "Designation",
      "Attendance Date",
      "In Time",
      "Out Time",
      "Late In",
      "Late Out",
      "In Location",
      "Out Location",
      "Zone Name",
      "Status"
    ];

    resultdata.forEach((record, index) => {
      excelList.push({
        "sno": index + 1,
        "Employee ID": record.empId,
        "Employee Name": record.empName,
        "Category": record.category,
        "Designation": record.department,
        "Attendance Date": this.formatDateForDisplay(record.attendanceDate),
        "In Time": this.formatTimeForDisplay(record.inTime),
        "Out Time": this.formatTimeForDisplay(record.outTime),
        "Late In": this.formatTimeForDisplay(record.lateIn),
        "Late Out": this.formatTimeForDisplay(record.lateOut),
        "In Location": this.getLocationText(record.inTimeLatitude, record.inTimeLongitude, record.zoneName),
        "Out Location": this.getLocationText(record.outTimeLatitude, record.outTimeLongitude, record.zoneName),
        "Zone Name": record.zoneName || '-',
        "Status": record.status
      });
    });

    this.excelService.exportAsExcelFile(excelList, "Attendance", excelColumns);
  }

  exportAttendanceToPdf() {
    console.log('Export attendance to PDF');
    // PDF export functionality can be implemented later
    this.alertService.info("PDF export functionality will be implemented soon");
  }

  editAttendance(record: AttendanceRecord) {
    this.onEdit(record);
  }

  deleteAttendance(record: AttendanceRecord) {
    this.onDelete(record);
  }

  openStatusUpdateDialog(record: AttendanceRecord) {
    console.log('Update status:', record);
    // Implement status update dialog logic
  }

  isAttendanceEditable(record: AttendanceRecord): boolean {
    // Allow editing for today's records only
    const today = this.formatDateToString(new Date());
    return record.attendanceDate === today;
  }

  getEditTooltip(record: AttendanceRecord): string {
    return this.isAttendanceEditable(record) ? 'Edit attendance' : 'Can only edit today\'s attendance records';
  }

  getDeleteTooltip(record: AttendanceRecord): string {
    return this.isAttendanceEditable(record) ? 'Delete attendance' : 'Can only delete today\'s attendance records';
  }

  onAttendancePaginationChange(event: any) {
    console.log('Pagination changed:', event);
    // Handle pagination change
  }

  // Filter methods
  applyFilters() {
    const searchTerm = this.employeeSearchControl.value?.toLowerCase() || '';
    const selectedDesignation = this.designationFilterControl.value || '';

    this.attendanceDataSource.filterPredicate = (data: AttendanceRecord, filter: string) => {
      const searchString = `${data.empId} ${data.empName} ${data.category} ${data.department}`.toLowerCase();
      const matchesSearch = searchString.includes(searchTerm);
      const matchesDesignation = !selectedDesignation || data.department === selectedDesignation;

      return matchesSearch && matchesDesignation;
    };

    // Use a dummy filter value since we're using custom filterPredicate
    this.attendanceDataSource.filter = searchTerm + selectedDesignation;
    this.updateAttendanceCounts();

    // Reset paginator to first page when filtering
    if (this.paginator) {
      this.paginator.firstPage();
    }
  }

  applyDateFilter() {
    // This method can be expanded to filter by actual date ranges
    // For now, it just refreshes the data
    this.loadEmployeeData();
  }

  clearAttendanceFilters() {
    this.attendanceStatusFilterControl.setValue([]);
    this.employeeSearchControl.setValue('');
    this.designationFilterControl.setValue('');
    this.dateRangeControl.setValue('TODAY');
    this.selectedDateRange = 'TODAY';
    this.selectedEmployeeChips = [];
    this.selectedDepartments = [];
    this.selectedDesignations = [];
    this.selectedAttendanceStatuses = [];
    this.startDate = null;
    this.endDate = null;
    this.attendanceDataSource.filter = '';
    this.loadEmployeeData();
  }

  onDateRangeChange(value: string) {
    this.selectedDateRange = value;
    if (value !== 'CUSTOM') {
      this.startDate = null;
      this.endDate = null;
      this.customFromDate = null;
      this.customToDate = null;
    } else {
      // Initialize custom dates if not set
      if (!this.customFromDate) {
        this.customFromDate = new Date();
      }
      if (!this.customToDate) {
        this.customToDate = new Date();
      }
    }
    this.applyDateFilter();
  }

  onStartDateChange() {
    this.applyDateFilter();
  }

  onEndDateChange() {
    this.applyDateFilter();
  }

  onFromDateInput(event: any) {
    const selectedDate = event.value;
    if (selectedDate) {
      // Ensure we're working with local date
      this.customFromDate = this.getLocalDate(selectedDate);
    }
  }

  onToDateInput(event: any) {
    const selectedDate = event.value;
    if (selectedDate) {
      // Ensure we're working with local date
      this.customToDate = this.getLocalDate(selectedDate);
    }
  }

  onCustomDateChange() {
    // Regenerate data when custom dates change
    if (this.selectedDateRange === 'CUSTOM' && this.customFromDate && this.customToDate) {
      // Ensure from date is not after to date using local dates
      const fromLocal = this.getLocalDate(this.customFromDate);
      const toLocal = this.getLocalDate(this.customToDate);

      if (fromLocal > toLocal) {
        this.customToDate = new Date(this.customFromDate);
      }
      this.applyDateFilter();
    }
  }




}

