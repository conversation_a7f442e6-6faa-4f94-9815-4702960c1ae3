import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PaddyformComponent } from './paddyform.component';
import { PaddyService } from '../../../../services/x_apis/acknowledge/paddy.service';
import { PaddyList } from '../../../../models/x_models/acknowledge/paddy';

@Component({
  selector: 'app-paddylist',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatFormFieldModule,
    TranslateModule,
    PaddyformComponent],
  providers: [DatePipe],
  templateUrl: './paddylist.component.html',
  styleUrls: ['./paddylist.component.scss']
})
export class PaddylistComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  paddyService = inject(PaddyService)
  listResource = resource({ loader: () => this.paddyService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.listResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  list = signal<PaddyList>({
    id: 0,
    truckMemoId: '',
    truckMemoDate: '',
    from: '',
    to: '',
    memoType: '',
    totalQty: 0,
    vehicleNo: 0,
    refOrderId: ''
  });


  displayedColumns: string[] = [
    'truckMemoId',
    'truckMemoDate',
    'from',
    'to',
    'memoType',
    'totalQty',
    'vehicleNo',
    'refOrderId',
    'actions',
  ];

  protected readonly AppRoutes = AppRoutes;

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }

    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.list.set({
      id: 0,
      truckMemoId: '',
      truckMemoDate: '',
      from: '',
      to: '',
      memoType: '',
      totalQty: 0,
      vehicleNo: 0,
      refOrderId: ''
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(data: PaddyList) {
    this.list.set(data);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(data: PaddyList) {
    this.list.set(data);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.listResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.listResource.reload();
    this.search.setValue("");
  }

  async onDelete(data: PaddyList) {
    await confirmAndDelete(data, data.truckMemoId, 'Truck Acknowledgement', this.paddyService, () => this.listResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
