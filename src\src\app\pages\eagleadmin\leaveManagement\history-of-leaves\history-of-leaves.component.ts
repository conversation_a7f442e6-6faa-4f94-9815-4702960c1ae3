import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { LeaveManagementService } from '../../../../services/m_apis/service/leave-management.service';
import { MatChipsModule } from '@angular/material/chips';

@Component({
  selector: 'app-history-of-leaves',
  imports: [CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatSelectModule,
    MatOptionModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatIconModule,
    MatSnackBarModule,
    MatChipsModule
  ],
  templateUrl: './history-of-leaves.component.html',
  styleUrl: './history-of-leaves.component.scss'
})
export class HistoryOfLeavesComponent implements OnInit {
  employeeId: string | null = '';
  displayedColumns: string[] = ['leaveType', 'fromDate', 'toDate', 'reason', 'status'];
  leaveHistory: any[] = [];
  isLoading = false;
  dataSource = new MatTableDataSource<any>([]);
  totaldays: any;
  constructor(private leaveService:LeaveManagementService){
    this.employeeId = 'EMP040';
  }
  ngOnInit(): void {
    this.getLeaveHistory();
  }

  getLeaveHistory():void{
    this.leaveService.getLeaveHistory(this.employeeId).subscribe({
      next: (response) => {
        this.leaveHistory = response.data;
        this.dataSource.data = this.leaveHistory;
        console.log('Leave History Data:', this.leaveHistory); // Add this line

      },
      error: (error) => {
        console.error('Error fetching leave history:', error);
      }
    });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    // Custom filter predicate
    this.dataSource.filterPredicate = (data: any, filter: string) => {
      return data.leaveTypeName.toLowerCase().includes(filter) ||
             data.fromDate.toLowerCase().includes(filter) ||
             data.toDate.toLowerCase().includes(filter) ||
             data.reason.toLowerCase().includes(filter) ||
             data.status.toLowerCase().includes(filter);
    };
  }


}
