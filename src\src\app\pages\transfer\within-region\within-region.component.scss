// Variables
$primary-color: #203664;
$accent-color: #203664;
$success-color: #4caf50;
$error-color: #f44336;
$warning-color: #ff9800;
$info-color: #2196f3;
$light-gray: #f5f5f5;
$border-radius: 8px;
$box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

// Container Styles
.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  min-height: calc(100vh - 100px);
}

// Card Styles
.form-card {
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  overflow: hidden;

  .card-header {
    background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
    color: white;
    padding: 24px;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;

      .header-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
      }

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }

    mat-card-subtitle {
      color: rgba(255, 255, 255, 0.8);
      margin-top: 8px;
      font-size: 14px;
    }
  }

  .progress-bar {
    height: 3px;

    ::ng-deep .mat-progress-bar-fill::after {
      background-color: $accent-color;
    }
  }

  .card-content {
    padding: 0;
  }
}

// Form Container
.form-container {
  padding: 32px;
}

// Section Styles
.section {
  margin-bottom: 40px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: $primary-color;
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 16px 0;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }

  mat-divider {
    margin-bottom: 24px;
    border-color: lighten($primary-color, 60%);
  }
}

// Form Grid
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-field {
    width: 100%;
  }

  .full-width {
    grid-column: 1 / -1;
  }
}

// Form Field Styles
mat-form-field {
  width: 100%;

  &.mat-form-field-appearance-outline {
    .mat-form-field-outline {
      color: lighten($primary-color, 40%);
    }

    &.mat-focused .mat-form-field-outline-thick {
      color: $primary-color;
    }

    .mat-form-field-label {
      color: $primary-color;
    }

    &.mat-focused .mat-form-field-label {
      color: $primary-color;
    }
  }

  .mat-form-field-suffix mat-icon {
    color: lighten($primary-color, 20%);
  }

  .mat-hint {
    color: lighten($primary-color, 30%);
    font-size: 12px;
  }

  .mat-error {
    color: $error-color;
    font-size: 12px;
  }
}

// Required Field Asterisk - Override the global asterisk removal
.required-asterisk {
  color: $error-color !important;
  font-weight: bold !important;
  margin-left: 2px !important;
  display: inline !important;
}

// Targeted override to prevent automatic asterisks only from form fields
.mat-mdc-form-field,
.mat-form-field,
.mdc-text-field {
  &::after {
    content: none !important;
    display: none !important;
  }
}

:host ::ng-deep {
  // Remove all possible automatic asterisks
  .mat-mdc-form-field .mat-mdc-floating-label::after,
  .mat-form-field-label::after,
  mat-label::after,
  .mdc-floating-label::after,
  .mat-form-field .mat-form-field-label::after,
  .mat-form-field-required-marker::after,
  [required]::after,
  .ng-invalid::after,
  .mat-mdc-form-field::after,
  .mat-form-field::after,
  .mat-mdc-text-field-wrapper::after,
  .mdc-text-field::after {
    content: none !important;
    display: none !important;
  }

  // Enhanced form field design
  .mat-mdc-form-field {
    width: 100%;
    margin-bottom: 16px;

    // Remove any automatic asterisks
    .mat-mdc-floating-label::after,
    .mat-form-field-label::after {
      content: none !important;
      display: none !important;
    }

    .mat-mdc-text-field-wrapper {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      }

      .mat-mdc-form-field-flex {
        background-color: transparent;

        .mat-mdc-form-field-infix {
          padding: 16px 12px;

          // Input styling
          input {
            background-color: transparent;
            border: none;
            outline: none;
            font-family: inherit;
            font-size: 14px;
            color: $primary-color;

            &::placeholder {
              color: lighten($primary-color, 50%);
              opacity: 1;
            }
          }

          // Textarea styling
          textarea {
            background-color: transparent;
            border: none;
            outline: none;
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.5;
            color: $primary-color;
            padding: 8px 0;

            &::placeholder {
              color: lighten($primary-color, 50%);
              opacity: 1;
            }
          }
        }
      }
    }

    // Outline styling
    .mat-mdc-form-field-outline {
      .mat-mdc-form-field-outline-start,
      .mat-mdc-form-field-outline-notch,
      .mat-mdc-form-field-outline-end {
        border-color: lighten($primary-color, 40%);
        border-width: 1px;
        transition: all 0.3s ease;
      }
    }

    // Focused state
    &.mat-focused {
      .mat-mdc-text-field-wrapper {
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }

      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-notch,
        .mat-mdc-form-field-outline-end {
          border-color: $primary-color;
          border-width: 2px;
        }
      }
    }

    // Full width modifier
    &.full-width {
      width: 100%;

      .mat-mdc-form-field-infix {
        min-height: 120px;
      }
    }

    // Label styling
    .mat-mdc-floating-label {
      color: $primary-color;
      font-weight: 500;

      &.mat-mdc-floating-label-floating {
        color: $primary-color;
      }
    }

    // Suffix icon styling
    .mat-mdc-form-field-icon-suffix {
      color: lighten($primary-color, 20%);
    }

    // Hint styling
    .mat-mdc-form-field-hint-wrapper {
      .mat-mdc-form-field-hint {
        color: lighten($primary-color, 30%);
        font-size: 12px;
      }
    }

    // Error styling
    .mat-mdc-form-field-error-wrapper {
      .mat-mdc-form-field-error {
        color: $error-color;
        font-size: 12px;
      }
    }
  }

  // Dropdown/Select styling
  .mat-mdc-select {
    .mat-mdc-select-trigger {
      .mat-mdc-select-value {
        color: $primary-color;
        font-size: 14px;
      }

      .mat-mdc-select-arrow-wrapper {
        .mat-mdc-select-arrow {
          color: lighten($primary-color, 20%);
        }
      }
    }
  }
}

// Global dropdown panel styling (outside :host ::ng-deep)
::ng-deep .mat-mdc-select-panel {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid lighten($primary-color, 40%);
  max-height: 300px;

  .mat-mdc-option {
    padding: 12px 16px;
    font-size: 14px;
    color: $primary-color;
    transition: all 0.2s ease;

    &:hover {
      background-color: lighten($primary-color, 50%);
      color: $primary-color;
    }

    &.mat-mdc-option-active {
      background-color: lighten($primary-color, 45%);
      color: $primary-color;
    }

    &.mdc-list-item--selected {
      background-color: $primary-color;
      color: white;

      &:hover {
        background-color: darken($primary-color, 10%);
      }
    }

    .mat-mdc-option-text {
      font-weight: 500;
    }
  }
}

// Override global required-label asterisk to prevent duplicates
.mat-mdc-form-field .mat-mdc-floating-label.required-label::after,
.mat-form-field-label.required-label::after,
.required-label::after,
mat-label.required-label::after,
.mat-mdc-form-field mat-label::after,
mat-form-field mat-label::after,
.mat-mdc-form-field .mat-mdc-floating-label::after,
.mat-form-field-label::after,
mat-label::after,
.mat-mdc-floating-label::after,
.mdc-floating-label::after,
.mat-form-field .mat-form-field-label::after,
.mat-form-field-required-marker,
.mat-form-field-required-marker::after {
  content: none !important;
  display: none !important;
}

// Priority Option Styles
.priority-option {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;

  &.low {
    background-color: lighten($info-color, 40%);
    color: $info-color;
  }

  &.medium {
    background-color: lighten($warning-color, 40%);
    color: darken($warning-color, 20%);
  }

  &.high {
    background-color: lighten($error-color, 40%);
    color: $error-color;
  }

  &.urgent {
    background-color: $error-color;
    color: white;
  }
}

// Form Actions
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-start;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid lighten($primary-color, 60%);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
  }

  button {
    min-width: 160px;
    height: 44px;
    border-radius: $border-radius;
    font-weight: 500;
    text-transform: none;

    @media (max-width: 768px) {
      min-width: 100%;
    }

    mat-icon {
      margin-right: 8px;
    }

    mat-spinner {
      margin-right: 8px;
    }

    &.submit-btn {
      background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
      color: white;
      box-shadow: 0 2px 4px rgba($primary-color, 0.3);

      &:hover:not(:disabled) {
        box-shadow: 0 4px 8px rgba($primary-color, 0.4);
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    &.draft-btn {
      border-color: $accent-color;
      color: $accent-color;

      &:hover:not(:disabled) {
        background-color: lighten($accent-color, 45%);
      }
    }

    &.reset-btn {
      color: $error-color;

      &:hover:not(:disabled) {
        background-color: lighten($error-color, 45%);
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .container {
    padding: 16px;
  }

  .form-container {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .form-container {
    padding: 20px;
  }

  .form-card .card-header {
    padding: 20px;

    mat-card-title h1 {
      font-size: 20px;
    }
  }

  .section .section-title {
    font-size: 16px;
  }
}

// Animation
.form-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Snackbar Styles (Global)
::ng-deep {
  .success-snackbar {
    background-color: $success-color !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: $error-color !important;
    color: white !important;
  }

  .warning-snackbar {
    background-color: $warning-color !important;
    color: white !important;
  }
}