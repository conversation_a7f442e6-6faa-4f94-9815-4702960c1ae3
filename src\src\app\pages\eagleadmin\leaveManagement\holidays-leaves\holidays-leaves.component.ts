import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MonthCalenderDialogComponent } from '../month-calender-dialog/month-calender-dialog.component';
import { LeaveManagementService } from '../../../../services/m_apis/service/leave-management.service';
interface Holiday {
  date: string;
  day: string;
  name: string;
}

interface Month {
  name: string;
  holidays: Holiday[];
}

@Component({
  selector: 'app-holidays-leaves',
  imports: [CommonModule, MatCardModule, MatGridListModule,  MatCardModule,
    MatIconModule,
    MatDialogModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatTableModule,
    MatOptionModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressBarModule,
    MatSelectModule,
    MatToolbarModule,
    MatTooltipModule],
  templateUrl: './holidays-leaves.component.html',
  styleUrl: './holidays-leaves.component.scss'
})
export class HolidaysLeavesComponent {
  selectedYear: string = '2025';
  months: Month[] = [
    { name: 'Jan', holidays: [] },
    { name: 'Feb', holidays: [] },
    { name: 'Mar', holidays: [] },
    { name: 'Apr', holidays: [] },
    { name: 'May', holidays: [] },
    { name: 'Jun', holidays: [] },
    { name: 'Jul', holidays: [] },
    { name: 'Aug', holidays: [] },
    { name: 'Sep', holidays: [] },
    { name: 'Oct', holidays: [] },
    { name: 'Nov', holidays: [] },
    { name: 'Dec', holidays: [] }
  ];

  constructor(public dialog: MatDialog, private holidayService: LeaveManagementService) {}

  ngOnInit(): void {
    this.loadHolidays();
  }

  loadHolidays(): void {
    const year = parseInt(this.selectedYear, 10);
    this.months.forEach((month, index) => {
      this.holidayService.getHolidays(year, index + 1).subscribe(holidays => {
        month.holidays = holidays.map(holiday => ({
          date: new Date(holiday.holidayDate).getDate().toString(), // Extract only day (1-31)
          day: holiday.holidayDay,
          name: holiday.holidayName
        }));
      });
    });
  }

  openMonthDialog(monthIndex: number): void {
    const monthData = this.months[monthIndex];
    this.dialog.open(MonthCalenderDialogComponent, {
      width: '400px',
      data: {
        month: monthIndex,
        year: parseInt(this.selectedYear, 10),
        holidays: monthData.holidays
      }
    });
  }

  onYearChange(event: any): void {
    this.selectedYear = event.value;
    this.loadHolidays();
  }
}
