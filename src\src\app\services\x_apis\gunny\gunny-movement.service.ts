import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { GunnyAllocationResponse, GunnyAllocations } from '../../../models/x_models/gunny/gunny-allocation';
import { GunnyMovement, GunnyMovementResponse } from '../../../models/x_models/gunny/gunny-movement';


@Injectable({
    providedIn: 'root'
})
export class GunnyMovementService {
    dataService = inject(DataService)

    create(data: GunnyMovement) {
        return this.dataService.post<Response>("/gunnymovement", data)
    }

    qrCreate(data: GunnyMovement) {
        return this.dataService.post<Response>("/gunnymovement/savescan", data)
    }
  
    getByQrId(id: number) {
        return this.dataService.get<GunnyMovement>(`/gunnymovement/scannedgunnys/${id}`)
    }

    getGunnyList(data: any) {
        return this.dataService.post<Response>("/gunnymovement/movementlist", data)
    }

    get() {
        return this.dataService.get<GunnyMovementResponse>("/gunnymovement")
    }

    getById(id: number) {
        return this.dataService.get<GunnyMovement>(`/gunnymovement/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/gunnymovement/${id}`)
    }

   sendtoDpc(id: number) {
        return this.dataService.get<GunnyMovement>(`/gunnymovement/sendtodpc/${id}`)
    }

}
