import { Component, inject, input, OnInit, output, resource, signal } from '@angular/core';
import { Form<PERSON><PERSON>er, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AlertService } from '../../../../services/alert.service';
import { DatePipe } from '@angular/common';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DriverService } from '../../../../services/x_apis/masters/driver.service';
import { Driver } from '../../../../models/x_models/masters/driver';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-driver',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatToolbarModule,
    MatFormFieldModule, TranslateModule
  ],
  templateUrl: './driver.component.html',
  styleUrls: ['./driver.component.scss'],
  providers: [provideNativeDateAdapter(), DatePipe],
})
export class DriverComponent implements OnInit {
  menuService = inject(MenuService);
  driverService = inject(DriverService)
  alert = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  lookupService = inject(LookupService);
  seasons = resource({ loader: () => this.lookupService.getSeason() }).value;

  mode = input.required<string>();
  uniqueId = input.required<any>()
  closed = output<boolean>();
  router = inject(Router);
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  driver = signal<Driver | null>(null);
  minToDate = signal<Date>(new Date());

  form = this.fb.group({
    id: [0],
    driverName: this.fb.control<string>('', Validators.required),
    driverMobileNo: this.fb.control<string>('', [Validators.required, Validators.pattern("^[6-9][0-9]{9}$")]),
    dob: this.fb.control<string>('', Validators.required),
    licenseNo: this.fb.control<string | null>(null, Validators.required)
  });

  constructor(private datePipe: DatePipe) { }

  ngOnInit() {
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getDriver()
    }
  }

  async getDriver() {
    const res = await this.driverService.getById(this.uniqueId());
    this.driver.set(res);
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      formValue.dob = this.datePipe.transform(this.form.value.dob, 'yyyy-MM-dd');
      const res = await this.driverService.create(formValue as Driver)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alert.success(`Driver ${formValue.driverName} Created Successfully.`)
        }
        else {
          this.alert.success(`Driver ${formValue.driverName} Updated Successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }
}
