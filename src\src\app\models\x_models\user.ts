import { Response } from "./api-response";

export interface DeleteUserPayload {
    userId: number;
}

export interface CreateUserPayload {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    userName: string;
    mobileNo: string;
    roleId: number;
}

export interface UpdateUserStatusPayload {
    id: number,
    statusType: number
}

export interface User {
    id: number
    fullName: string
    email: string
    statusType: number
    mobileNo: string
    roleName: string
}

export interface UserByIdResponse {
    id: number
    firstName: string
    email: string
    userName: string
    mobileNo: string
    lastName: string
    roleId: number
}

export type UserResponse = User[]