import { Component, OnInit, signal } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';

interface District {
  name: string;
  code: string;
}

@Component({
  selector: 'app-personal-details',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatSnackBarModule,
    MatDividerModule
  ],
  templateUrl: './personal-details.component.html',
  styleUrls: ['./personal-details.component.scss']
})
export class PersonalDetailsComponent implements OnInit {
  personalDetailsForm!: FormGroup;
  isLoading = signal(false);
  selectedFile = signal<File | null>(null);
  maxDate = new Date();

  districtList: District[] = [
    { name: 'Ariyalur', code: 'AR' },
    { name: 'Chengalpattu', code: 'CH' },
    { name: 'Chennai', code: 'CN' },
    { name: 'Coimbatore', code: 'CB' },
    { name: 'Cuddalore', code: 'CD' },
    { name: 'Dharmapuri', code: 'DH' },
    { name: 'Dindigul', code: 'DI' },
    { name: 'Erode', code: 'ER' },
    { name: 'Kallakurichi', code: 'KK' },
    { name: 'Kanchipuram', code: 'KC' },
    { name: 'Kanyakumari', code: 'KN' },
    { name: 'Karur', code: 'KR' },
    { name: 'Krishnagiri', code: 'KG' },
    { name: 'Madurai', code: 'MD' },
    { name: 'Mayiladuthurai', code: 'MY' },
    { name: 'Nagapattinam', code: 'NG' },
    { name: 'Namakkal', code: 'NK' },
    { name: 'Nilgiris', code: 'NL' },
    { name: 'Perambalur', code: 'PB' },
    { name: 'Pudukkottai', code: 'PK' },
    { name: 'Ramanathapuram', code: 'RM' },
    { name: 'Ranipet', code: 'RP' },
    { name: 'Salem', code: 'SL' },
    { name: 'Sivaganga', code: 'SG' },
    { name: 'Tenkasi', code: 'TK' },
    { name: 'Thanjavur', code: 'TJ' },
    { name: 'Theni', code: 'TH' },
    { name: 'Thoothukudi', code: 'TD' },
    { name: 'Tiruchirappalli', code: 'TR' },
    { name: 'Tirunelveli', code: 'TN' },
    { name: 'Tirupathur', code: 'TP' },
    { name: 'Tiruppur', code: 'TU' },
    { name: 'Tiruvallur', code: 'TV' },
    { name: 'Tiruvannamalai', code: 'TA' },
    { name: 'Tiruvarur', code: 'TV' },
    { name: 'Vellore', code: 'VL' },
    { name: 'Viluppuram', code: 'VP' },
    { name: 'Virudhunagar', code: 'VR' }
  ];

  religionList = [
    'Hindu', 'Muslim', 'Christian', 'Sikh', 'Buddhist', 'Jain', 'Other'
  ];

  genderList = [
    { value: 'Male', label: 'Male' },
    { value: 'Female', label: 'Female' },
    { value: 'Other', label: 'Other' }
  ];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.personalDetailsForm = this.fb.group({
      applicantName: ['', [Validators.required, Validators.minLength(2), Validators.pattern(/^[a-zA-Z\s]+$/)]],
      mobileNumber: ['', [Validators.required, Validators.pattern(/^[6-9]\d{9}$/)]],
      fathersName: ['', [Validators.required, Validators.minLength(2), Validators.pattern(/^[a-zA-Z\s]+$/)]],
      mothersName: ['', [Validators.required, Validators.minLength(2), Validators.pattern(/^[a-zA-Z\s]+$/)]],
      dateOfBirth: ['', Validators.required],
      birthDistrict: ['', Validators.required],
      nativeDistrict: ['', Validators.required],
      aadhaarNumber: ['', [Validators.required, Validators.pattern(/^\d{12}$/)]],
      panNumber: ['', [Validators.required, Validators.pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/)]],
      gender: ['', Validators.required],
      religion: ['', Validators.required],
      nationality: ['Indian', Validators.required],
      communityCertificate: [null]
    });
  }

  onFileSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
      if (!allowedTypes.includes(file.type)) {
        this.showSnackBar('Please upload only PDF, JPEG, or PNG files', 'error');
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        this.showSnackBar('File size should not exceed 5MB', 'error');
        return;
      }

      this.selectedFile.set(file);
      this.personalDetailsForm.patchValue({ communityCertificate: file });
      this.personalDetailsForm.get('communityCertificate')?.updateValueAndValidity();
      this.showSnackBar('File uploaded successfully', 'success');
    }
  }

  removeFile(): void {
    this.selectedFile.set(null);
    this.personalDetailsForm.patchValue({ communityCertificate: null });
    // Reset file input
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  triggerFileInput(): void {
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  getErrorMessage(fieldName: string): string {
    const field = this.personalDetailsForm.get(fieldName);
    if (field?.hasError('required')) {
      return `${this.getFieldLabel(fieldName)} is required`;
    }
    if (field?.hasError('pattern')) {
      return this.getPatternErrorMessage(fieldName);
    }
    if (field?.hasError('minlength')) {
      return `${this.getFieldLabel(fieldName)} must be at least ${field.errors?.['minlength'].requiredLength} characters`;
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      applicantName: 'Applicant Name',
      mobileNumber: 'Mobile Number',
      fathersName: "Father's Name",
      mothersName: "Mother's Name",
      dateOfBirth: 'Date of Birth',
      birthDistrict: 'Birth District',
      nativeDistrict: 'Native District',
      aadhaarNumber: 'Aadhaar Number',
      panNumber: 'PAN Number',
      gender: 'Gender',
      religion: 'Religion',
      nationality: 'Nationality'
    };
    return labels[fieldName] || fieldName;
  }

  private getPatternErrorMessage(fieldName: string): string {
    const messages: { [key: string]: string } = {
      applicantName: 'Name should contain only letters and spaces',
      mobileNumber: 'Enter a valid 10-digit mobile number starting with 6-9',
      fathersName: 'Name should contain only letters and spaces',
      mothersName: 'Name should contain only letters and spaces',
      aadhaarNumber: 'Enter a valid 12-digit Aadhaar number',
      panNumber: 'Enter a valid PAN number (e.g., **********)'
    };
    return messages[fieldName] || 'Invalid format';
  }

  onSubmit(): void {
    console.log('Form Submitted:', this.personalDetailsForm.value);
    if (this.personalDetailsForm.valid) {
      this.isLoading.set(true);
      
      // Simulate API call
      setTimeout(() => {
        this.isLoading.set(false);
        console.log('Form Submitted:', this.personalDetailsForm.value);
        this.showSnackBar('Personal details submitted successfully!', 'success');
        // Optionally reset form or navigate to next step
      }, 2000);
    } else {
      this.markFormGroupTouched();
      this.showSnackBar('Please correct the errors in the form', 'error');
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.personalDetailsForm.controls).forEach(key => {
      const control = this.personalDetailsForm.get(key);
      control?.markAsTouched();
    });
  }

  private showSnackBar(message: string, type: 'success' | 'error'): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: type === 'success' ? 'success-snackbar' : 'error-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  onReset(): void {
    this.personalDetailsForm.reset();
    this.selectedFile.set(null);
    this.personalDetailsForm.patchValue({ nationality: 'Indian' });
    this.showSnackBar('Form reset successfully', 'success');
  }
}