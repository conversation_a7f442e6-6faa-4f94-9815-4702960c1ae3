{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/tooltip.mjs"], "sourcesContent": ["export { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, getMatTooltipInvalidPositionError } from './module-5X7oty_s.mjs';\nimport '@angular/core';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport 'rxjs/operators';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/keycodes';\nimport '@angular/common';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/portal';\nimport 'rxjs';\nimport './common-module-DoCSSHRt.mjs';\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matTooltipAnimations = {\n  // Represents:\n  // trigger('state', [\n  //   state('initial, void, hidden', style({opacity: 0, transform: 'scale(0.8)'})),\n  //   state('visible', style({transform: 'scale(1)'})),\n  //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n  //   transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)')),\n  // ])\n  /** Animation that transitions a tooltip in and out. */\n  tooltipState: {\n    type: 7,\n    name: 'state',\n    definitions: [{\n      type: 0,\n      name: 'initial, void, hidden',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(0.8)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'visible',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(1)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => visible',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '150ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => hidden',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '75ms cubic-bezier(0.4, 0, 1, 1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { matTooltipAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS3B,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": []}