.pac-container {
    translate: 0px 14px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    box-shadow: 0px 6px 14px -6px rgba(19, 25, 39, 0.12), 0px 10px 32px -4px rgba(19, 25, 39, 0.10);
    border-top: none;
    padding-block: 8px;
    font-family: "Roboto" !important;

    &::after {
        display: none;
    }

    .pac-item {
        border-top: none;
        height: 40px;
        display: flex;
        align-items: center;
        padding-inline: 12px;

        .pac-icon {
            margin-top: 0;
        }

        &:hover {
            background-color: var(--theme-30);
        }
    }

    .pac-item-selected, .pac-item-selected:hover {
        background-color: var(--theme-80);
        color: #fff;
        .pac-item-query {
            color: #fff;
        }
    }

}