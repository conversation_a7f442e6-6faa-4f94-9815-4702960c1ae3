import { Directive, ElementRef, HostListener } from '@angular/core';
import {NgControl} from '@angular/forms';
import { map } from 'rxjs';

@Directive({
  selector: '[appOnlyNumbers]'
})
export class OnlyNumbersDirective {
  regexStr = '^[0-9]*$';

  constructor(private el: ElementRef, private ngControl: NgControl) {}

  ngOnInit(): void {
    const control = this.ngControl.control;
    if (control) {
      control.valueChanges
        .pipe(
          map((value) => {
            console.log(value);
            return this.formatMobileNumber(value);
          })
        )
        .subscribe((v) => {
          control.setValue(v, { emitEvent: false });
        });
    }
  }

  ngAfterViewInit(): void {

  }

  @HostListener('keypress', ['$event']) onKeyPress(event: KeyboardEvent) {
    return new RegExp(this.regexStr).test(event.key);
  }

  @HostListener('paste', ['$event']) blockPaste(event: KeyboardEvent) {
    this.validateFields(event);
  }

  formatMobileNumber(phoneNumber: string) {
    const numbers = phoneNumber.replace(/[^0-9]/g, '');
    return phoneNumber.slice(0, 10)
  }

  validateFields(event: KeyboardEvent) {
    setTimeout(() => {
      this.el.nativeElement.value = this.formatMobileNumber(this.el.nativeElement.value.replace(
        /[^0-9]/g,
        ''
      ));
      event.preventDefault();
    }, 100);
  }
}
