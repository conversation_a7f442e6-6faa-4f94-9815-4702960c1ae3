import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { TransferStaticDataService } from './transfer-static-data.service';
import { Region, Department, Employee } from '../../enums/m_enums/transfer.model';


@Injectable({
  providedIn: 'root'
})
export class TransferDataService {
  // submitWithinRegionTransfer(value: any)  {
  //   throw new Error('Method not implemented.');
  // }

  constructor(
    private http: HttpClient,
    private staticData: TransferStaticDataService
  ) {}

  getRegions(): Observable<Region[]> {
    return of(this.staticData.getRegions());
  }

  getDepartments(): Observable<Department[]> {
    return of(this.staticData.getDepartments());
  }

  getEmployees(): Observable<Employee[]> {
    return of(this.staticData.getEmployees());
  }

  submitWithinRegionTransfer(data: any): Observable<any> {
    console.log('Submitting Within Region Transfer:', data);
    return of({ success: true });
  }

  submitInterRegionTransfer( data: any): Observable<any> {
    console.log('Submitting Inter Region Transfer:', data);
    return of({ success: true });
  }

  submitMutualTransfer( data: any): Observable<any> {
    console.log('Submitting Mutual Transfer:', data);
    return of({ success: true });
  }
}
