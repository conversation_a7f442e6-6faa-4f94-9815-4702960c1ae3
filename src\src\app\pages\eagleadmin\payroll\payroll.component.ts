import { Component, OnInit, ViewChild } from '@angular/core';

import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router, RouterOutlet } from '@angular/router';
import { MatTable, MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { HttpParams } from '@angular/common/http';
import { PayrollService } from '../../../services/m_apis/service/payroll.service';



export interface PeriodicElement {
  name: string;
  position: number;
  weight: number;
  symbol: string;
}

const ELEMENT_DATA: PeriodicElement[] = [
  {position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H'},
  {position: 2, name: 'Helium', weight: 4.0026, symbol: 'He'},
  {position: 3, name: 'Lithium', weight: 6.941, symbol: 'Li'},
  {position: 4, name: 'Beryllium', weight: 9.0122, symbol: 'Be'},
  {position: 5, name: 'Boron', weight: 10.811, symbol: 'B'},
  {position: 6, name: 'Carbon', weight: 12.0107, symbol: 'C'},
  {position: 7, name: 'Nitrogen', weight: 14.0067, symbol: 'N'},
  {position: 8, name: 'Oxygen', weight: 15.9994, symbol: 'O'},
  {position: 9, name: 'Fluorine', weight: 18.9984, symbol: 'F'},
  {position: 10, name: 'Neon', weight: 20.1797, symbol: 'Ne'},
];

@Component({
  selector: 'app-payroll',
  imports: [
    
    ReactiveFormsModule,
    CommonModule,
    RouterOutlet, 
    MatTableModule, 
    MatTabsModule,
    MatIconModule,
    MatButtonModule,
    MatPaginatorModule,
    MatSortModule,
    MatTooltipModule,
    MatCardModule,
    MatChipsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './payroll.component.html',
  styleUrl: './payroll.component.scss'
})
export class PayrollComponent implements OnInit {
  
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = [
    'position',
    'empId',
    'employeeName', 
    'department', 
    'employeeRole',
    'grade',
    'panNumber',
    'pfNumber',
    'specialPayType',
    'view'
  ];
  
  dataSource: any[] = [];
  mainList: any[] = [];
  count: number = 0;
  pageSize: number = 10;
  isLoading: boolean = false;

  constructor(
    private webService: PayrollService, 
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getEmployeeList();
  }

  navigateToPayroll() {
    this.router.navigateByUrl("/main/payroll");
  }

  navigateToAttendance() {
    this.router.navigateByUrl("/main/attendance");
  }

  getList() {
    // Keeping the original method structure in case needed
    // const params = new HttpParams().append('start', 0).append('pagesize', this.pageSize);
    // this.webService.getList(params).subscribe(
    //   (res: { responseType: number; responseData: any[]; }) => {
    //     this.mainList = [];
    //     this.count = res.responseType;
    //     this.mainList = res.responseData;
    //   },
    //   (error: any) => {
    //     console.error('Error fetching list:', error);
    //   }
    // );
  }

  goPageChange(empId: any) {
    this.router.navigate([`m/view/${empId}`]);
  }

  getEmployeeList() {
    this.isLoading = true;
    
    this.webService.getLists().subscribe({
      next: (data) => {
        this.mainList = [];
        console.log('Employee data:', (data as any).responseData);
        
        this.mainList = (data as any).responseData || [];
        this.dataSource = this.mainList;
        this.count = this.dataSource.length;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching employee list:', error);
        this.isLoading = false;
        this.dataSource = [];
        this.mainList = [];
        this.count = 0;
      }
    });
  }

  refreshData() {
    this.getEmployeeList();
  }

  viewEmployeeDetails(employee: any) {
    // Navigate to employee details or open a dialog
    console.log('Viewing employee details:', employee);
    // You can implement navigation to employee details page
    // this.router.navigate(['/employee-details', employee.empId]);
  }

  // Optional: Add filtering method
  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    // Implement filtering logic here if needed
    // You can filter the dataSource based on the filterValue
  }

  // Optional: Export functionality
  exportToExcel() {
    // Implement export functionality if needed
    console.log('Exporting employee list to Excel...');
  }
}