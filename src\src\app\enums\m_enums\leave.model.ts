export interface LeaveBalance {
    casual: number;
    sick: number;
    earned: number;
  }
  
  export interface LeaveApplicationRequest {
    employeeId: any | null;
    leaveTypeId: any;
    fromDate: Date | string;
    toDate: Date | string;
    fromSession: string;
    toSession: string;
    approverId: string;
    reason: string;
  }
  
  export interface LeaveApplicationResponse {
    applicationNumber: string;
    employeeId: string;
    leaveTypeId: string;
    fromDate: Date | string;
    toDate: Date | string;
    fromSession: string;
    toSession: string;
    totalDays: number;
    reason: string;
    approverId: string;
    approverName: string;
    status: string;
    createdAt: Date | string;
  }

  export interface LeaveBalanceDTO {
    balanceId: number;
    employeeId: string;
    leaveTypeId: number;
    leaveTypeName: string;
    leaveTypeCode: string;
    year: number;
    totalBalance: number;
    availedBalance: number;
    pendingBalance: number;
    encashedBalance: number;
    totalMedicalLeaveUsed: number;
  }