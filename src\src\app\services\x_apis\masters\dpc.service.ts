import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { dpc, DpcResponse } from '../../../models/x_models/masters/dpc';

@Injectable({
  providedIn: 'root'
})
export class DpcService {

  dataService = inject(DataService)

  create(data: dpc) {
    return this.dataService.post<Response>("/dpc", data)
  }

  get() {
    return this.dataService.get<DpcResponse>("/dpc")
  }

  getById(id: number) {
    return this.dataService.get<dpc>(`/dpc/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/dpc/${id}`)
  }

}
