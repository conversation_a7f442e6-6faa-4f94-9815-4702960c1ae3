import { Component, Input, Output, EventEmitter, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { AttendanceRecord } from './attendance.component';

@Component({
  selector: 'app-attendance-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule
  ],
  template: `
    <div class="attendance-form-container">
      <div class="form-header">
        <h2>{{ getTitle() }}</h2>
        <button mat-icon-button (click)="onCancel()" class="close-btn">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <form [formGroup]="attendanceForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Employee ID</mat-label>
            <input matInput formControlName="empId" [readonly]="mode === 'view'">
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Employee Name</mat-label>
            <input matInput formControlName="empName" [readonly]="mode === 'view'">
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Department</mat-label>
            <input matInput formControlName="category" [readonly]="mode === 'view'">
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Designation</mat-label>
            <input matInput formControlName="department" [readonly]="mode === 'view'">
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Attendance Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="attendanceDate" [readonly]="mode === 'view'">
            <mat-datepicker-toggle matSuffix [for]="picker" [disabled]="mode === 'view'"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Status</mat-label>
            <mat-select formControlName="status" [disabled]="mode === 'view'">
              <mat-option value="Present">Present</mat-option>
              <mat-option value="Absent">Absent</mat-option>
              <mat-option value="Leave">Leave</mat-option>
              <mat-option value="Late">Late</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>In Time</mat-label>
            <input matInput type="time" formControlName="inTime" [readonly]="mode === 'view'">
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Out Time</mat-label>
            <input matInput type="time" formControlName="outTime" [readonly]="mode === 'view'">
          </mat-form-field>
        </div>

        <div class="form-actions">
          @if (mode !== 'view') {
            <button mat-raised-button color="primary" type="submit" [disabled]="!attendanceForm.valid">
              {{ mode === 'add' ? 'Add' : 'Update' }}
            </button>
          }
          @if (mode === 'view') {
            <button mat-raised-button color="primary" (click)="onEdit()">
              Edit
            </button>
          }
          <button mat-button type="button" (click)="onCancel()">
            Cancel
          </button>
        </div>
      </form>
    </div>
  `,
  styles: [`
    .attendance-form-container {
      background: white;
      border-radius: 8px;
      padding: 24px;
      max-width: 800px;
      margin: 0 auto;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      border-bottom: 1px solid #e0e0e0;
      padding-bottom: 16px;
    }

    .form-header h2 {
      margin: 0;
      color: #333;
    }

    .close-btn {
      color: #666;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-field {
      flex: 1;
    }

    .form-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #e0e0e0;
    }

    @media (max-width: 768px) {
      .form-row {
        flex-direction: column;
      }
      
      .form-actions {
        flex-direction: column;
      }
    }
  `]
})
export class AttendanceFormComponent implements OnInit {
  @Input() record: AttendanceRecord | null = null;
  @Input() mode: 'view' | 'edit' | 'add' = 'add';
  @Output() save = new EventEmitter<AttendanceRecord>();
  @Output() cancel = new EventEmitter<void>();
  @Output() edit = new EventEmitter<void>();

  attendanceForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.attendanceForm = this.fb.group({
      empId: ['', Validators.required],
      empName: ['', Validators.required],
      category: ['', Validators.required],
      department: ['', Validators.required],
      attendanceDate: [new Date(), Validators.required],
      status: ['Present', Validators.required],
      inTime: [''],
      outTime: [''],
      lateIn: [''],
      lateOut: ['']
    });
  }

  ngOnInit() {
    if (this.record) {
      this.attendanceForm.patchValue({
        ...this.record,
        attendanceDate: new Date(this.record.attendanceDate)
      });
    }

    if (this.mode === 'view') {
      this.attendanceForm.disable();
    }
  }

  getTitle(): string {
    switch (this.mode) {
      case 'add': return 'Add Attendance Record';
      case 'edit': return 'Edit Attendance Record';
      case 'view': return 'View Attendance Record';
      default: return 'Attendance Record';
    }
  }

  onSubmit() {
    if (this.attendanceForm.valid) {
      const formValue = this.attendanceForm.value;
      const attendanceRecord: AttendanceRecord = {
        ...formValue,
        attendanceDate: formValue.attendanceDate.toISOString().split('T')[0]
      };
      this.save.emit(attendanceRecord);
    }
  }

  onEdit() {
    this.edit.emit();
  }

  onCancel() {
    this.cancel.emit();
  }
}
