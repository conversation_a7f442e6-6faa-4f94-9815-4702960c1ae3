import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Block, BlockResponse } from '../../../models/x_models/masters/block';

@Injectable({
  providedIn: 'root'
})
export class BlockService {

  dataService = inject(DataService)

  create(data: Block) {
      return this.dataService.post<Response>("/block", data)
    }
  
    get() {
      return this.dataService.get<BlockResponse>("/block")
    }
  
    getById(id: number) {
      return this.dataService.get<Block>(`/block/${id}`)
    }
  
    delete(id: number) {
      return this.dataService.delete<Response>(`/block/${id}`)
    }

}
