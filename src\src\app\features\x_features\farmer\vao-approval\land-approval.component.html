<div class="component">

  <div class="page-header">
    <h1>{{farmerName()}} {{' Land Details' | translate }}</h1>
    <button type="button" mat-flat-button (click)="back()" class="btn btn-secondary">{{'Back' |
      translate}}</button>

  </div>


  @for (detail of landApprovalDetails(); track $index) {
  <div class="card" style="border-radius: 20px;padding-bottom: 20px;" [ngStyle]="{'border-left':detail.vaoApprovalStatusType==AproveStatus.Approved?'5px solid green':detail.vaoApprovalStatusType==AproveStatus.Rejected?'5px solid #c50404':'5px solid orange',
      'margin-bottom':($index+1)==landApprovalDetails()?.length?'50px':'20px'
    }">
    <div style="padding-left: 20px;">Survey No&nbsp;/&nbsp;Subdivision&nbsp;&nbsp;<span
        style="font-weight: 600;color: #800080;">{{detail.surveyNo}}
        &nbsp;/&nbsp;{{detail.subDivsion}}</span> &nbsp;&nbsp;- &nbsp;&nbsp; <span
        style="font-weight: 600;color: #800080;">{{detail.regionName}}</span>
      {{'District'|translate}} /
      <span style="font-weight: 600;color: #800080;">{{detail.talukName}}</span> {{'Taluk'|translate}} /
      <span style="font-weight: 600;color: #800080;">{{detail.blockName}} </span> {{'Block' |translate}}
      / <span style="font-weight: 600;color: #800080;">{{detail.villageName}} </span> {{'Village'|translate}}
      <span style="margin-left: 20px;" [class.approved]="detail.vaoApprovalStatusType==AproveStatus.Approved"
        [class.sendback]="detail.vaoApprovalStatusType==AproveStatus.Rejected"
        [class.pending]="detail.vaoApprovalStatusType==AproveStatus.Pending">{{detail.vaoApprovalStatusType==AproveStatus.Rejected?'Send Back':detail.vaoApprovalStatusTypeName}}
      </span>
    </div>

    <div class="container-land">

      <div class="flex">
        <div class="width-50 label">{{'Land Lord Name'}}</div>
        <div class="width-50 "><span style="font-weight: 600;">{{detail.landLordName}}</span> </div>
      </div>
      <div class="flex">
        <div class="width-50 label">{{'Land Type'}}</div>
        <div class="width-50 "><span style="font-weight: 600;">{{detail.landTypeName}}</span> </div>
      </div>
      <div class="flex">
        <div class="width-50 label">{{'Land Area'}}</div>
        <div class="width-50 "> <span style="font-weight: 600;">{{detail.areaOfLand}} 
          @if(detail.areaOfLand>1){Acres}
          @else {Acre}
        </span>
         </div>
      </div>
      <div class="flex">
        <div class="width-50 label">{{'Estimated Yield'}} </div>
        <div class="width-50">
          <span style="font-weight: 600;">{{detail.expectedYield}}
            @if (detail.expectedYield>0) {
              @if(detail.expectedYield>1){Bags}
            @else{Bag}} </span>
        </div>
      </div>
      @if(detail.landAttachmentGetViewModel)
      {
      <div style="display: flex;">
        <div class="width-50 label">{{'Documents'}}</div>
        <div class="width-50"><button title="{{ 'Image' | translate }}" (click)="downloadDoc(detail)"
            class="btn-icon1 btn-icon-unstyled ">
            <mat-icon class="material-symbols-rounded" style="font-size: 25px;">image</mat-icon>
          </button> </div>
      </div>
      }

    </div>

    @if(detail.vaoApprovalStatusType==AproveStatus.Pending)
    {
    <div class="actions" style="margin-right: 20px;">
      @if(access()?.canSendBack){
      <button mat-flat-button class="reject btn btn-secondary" (click)="reject(detail,AproveStatus.Rejected)">Send
        Back</button>
      }
      @if(access()?.canApprove)
      {
      <button mat-flat-button class="approve btn btn-theme" type="submit"
        (click)="approve(detail,AproveStatus.Approved)">Approve</button>
      }
    </div>
    }
  </div>
  }

  <!-- <div class="card" style="padding: 0px;border-radius: 20px;padding-bottom: 20px;margin-bottom: 20px;">
    <div class="head">Survey No: 100/1/1 &nbsp;&nbsp;- &nbsp;&nbsp;  <span style="font-weight: 600;">Thanjavur</span> District / <span style="font-weight: 600;">Thanjavur </span>Block 
        / <span style="font-weight: 600;">Keeraikolai</span> Village</div>
        <div class="container-land">
            <div>div</div>
            <div>div</div>
            <div>div</div>
            <div>div</div>
            <div>div</div>
            <div>div</div>

        </div>

         <div class="actions" style="margin-right: 20px;">
        <button mat-flat-button (click)="reject()" class=" reject btn btn-secondary">Reject</button>
        <button mat-flat-button class="approve btn btn-theme" type="submit">Approve</button>
        </div>
  </div>

   <div class="card" style="padding: 0px;border-radius: 20px;padding-bottom: 20px;margin-bottom: 20px;">
    <div class="head">Survey No: 100/1/1 &nbsp;&nbsp;- &nbsp;&nbsp;  <span style="font-weight: 600;">Thanjavur</span> District / <span style="font-weight: 600;">Thanjavur </span>Block 
        / <span style="font-weight: 600;">Keeraikolai</span> Village</div>
        <div class="container-land">
            <div>div</div>
            <div>div</div>
            <div>div</div>
            <div>div</div>
            <div>div</div>
            <div>div</div>

        </div>

         <div class="actions" style="margin-right: 20px;">
        <button mat-flat-button (click)="reject()" class=" reject btn btn-secondary">Reject</button>
        <button mat-flat-button class="approve btn btn-theme" type="submit">Approve</button>
        </div>
  </div> -->

</div>