@if (!showAttendanceForm()) {
  <div class="component card">
    <div class="page-header">
      <h1>Attendance Management</h1>
    </div>

    <div class="header">
      <div class="filters-wrapper">
        <mat-form-field class="search hide-subscript" appearance="outline">
          <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
          <input [formControl]="employeeSearchControl" matInput placeholder="Search employees...">
        </mat-form-field>

        <mat-form-field class="designation-field hide-subscript" appearance="outline">
          <mat-label>Filter by Designation</mat-label>
          <mat-select [formControl]="designationFilterControl">
            <mat-option value="">All Designations</mat-option>
            @for (designation of designations; track designation) {
              <mat-option [value]="designation">{{ designation }}</mat-option>
            }
          </mat-select>
        </mat-form-field>

        <mat-form-field class="date-field hide-subscript" appearance="outline">
          <mat-label>Date Range</mat-label>
          <mat-select [formControl]="dateRangeControl" (selectionChange)="onDateRangeChange($event.value)">
            @for (option of dateRangeOptions; track option) {
              <mat-option [value]="option">{{ dateRangeLabels[option] }}</mat-option>
            }
          </mat-select>
        </mat-form-field>

        <!-- Custom Date Range Fields -->
        @if (selectedDateRange === 'CUSTOM') {
          <mat-form-field class="date-field hide-subscript" appearance="outline">
            <mat-label>From Date</mat-label>
            <input matInput [matDatepicker]="fromDatePicker" [(ngModel)]="customFromDate" [max]="maxDate" (dateInput)="onFromDateInput($event)" (dateChange)="onCustomDateChange()">
            <mat-datepicker-toggle matSuffix [for]="fromDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #fromDatePicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field class="date-field hide-subscript" appearance="outline">
            <mat-label>To Date</mat-label>
            <input matInput [matDatepicker]="toDatePicker" [(ngModel)]="customToDate" [max]="maxDate" [min]="customFromDate" (dateInput)="onToDateInput($event)" (dateChange)="onCustomDateChange()">
            <mat-datepicker-toggle matSuffix [for]="toDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #toDatePicker></mat-datepicker>
          </mat-form-field>
        }

        <div class="filters-more">
          <button (click)="onAdd()" class="btn btn-filter" mat-icon-button>
            <mat-icon class="icon-filter material-symbols-outlined" matPrefix>add</mat-icon>
          </button>
          <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
            <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
          </button>
          <button (click)="exportAttendance()" class="btn btn-filter" mat-icon-button>
            <mat-icon class="icon-filter material-symbols-outlined" matPrefix>file_download</mat-icon>
          </button>
        </div>
      </div>
    </div>

    <div class="content">
      <!-- Attendance Summary Cards -->
      <div class="attendance-summary">
        <div class="summary-cards">
          <mat-card class="summary-card present-card">
            <mat-card-content>
              <div class="summary-content">
                <mat-icon class="summary-icon">check_circle</mat-icon>
                <div class="summary-text">
                  <div class="summary-number">{{ attendancePresentCount || 0 }}</div>
                  <div class="summary-label">Present Employees</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="summary-card leave-card">
            <mat-card-content>
              <div class="summary-content">
                <mat-icon class="summary-icon">event_busy</mat-icon>
                <div class="summary-text">
                  <div class="summary-number">{{ attendanceLeaveCount || 0 }}</div>
                  <div class="summary-label">Leave Employees</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="summary-card absent-card">
            <mat-card-content>
              <div class="summary-content">
                <mat-icon class="summary-icon">cancel</mat-icon>
                <div class="summary-text">
                  <div class="summary-number">{{ attendanceAbsentCount || 0 }}</div>
                  <div class="summary-label">Absent Employees</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="summary-card total-card">
            <mat-card-content>
              <div class="summary-content">
                <mat-icon class="summary-icon">people</mat-icon>
                <div class="summary-text">
                  <div class="summary-number">{{ attendanceTotalEmployees || 0 }}</div>
                  <div class="summary-label">Total Employees</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>

      <div class="table-wrapper">
        <table mat-table [dataSource]="attendanceDataSource" matSort class="mat-elevation-z8">

          <ng-container matColumnDef="sno">
            <th mat-header-cell *matHeaderCellDef> S.No </th>
            <td mat-cell *matCellDef="let record; let i = index"> {{ i + 1 }} </td>
          </ng-container>

          <ng-container matColumnDef="empId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Employee ID </th>
            <td mat-cell *matCellDef="let record"> {{ record.empId }} </td>
          </ng-container>

          <ng-container matColumnDef="empName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Employee Name </th>
            <td mat-cell *matCellDef="let record"> {{ record.empName }} </td>
          </ng-container>

          <ng-container matColumnDef="category">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Department </th>
            <td mat-cell *matCellDef="let record"> {{ record.category }} </td>
          </ng-container>

          <ng-container matColumnDef="designation">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Designation </th>
            <td mat-cell *matCellDef="let record"> {{ record.department }} </td>
          </ng-container>

          <ng-container matColumnDef="attendanceDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Date </th>
            <td mat-cell *matCellDef="let record"> {{ formatDateForDisplay(record.attendanceDate) }} </td>
          </ng-container>

          <ng-container matColumnDef="inTime">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> In Time </th>
            <td mat-cell *matCellDef="let record">
              {{ formatTimeForDisplay(record.inTime) }}
              @if (record.lateIn) {
                <span class="late-indicator"> (Late: {{ record.lateIn }})</span>
              }
            </td>
          </ng-container>

          <ng-container matColumnDef="outTime">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Out Time </th>
            <td mat-cell *matCellDef="let record">
              {{ formatTimeForDisplay(record.outTime) }}
              @if (record.lateOut) {
                <span class="late-indicator"> (Late: {{ record.lateOut }})</span>
              }
            </td>
          </ng-container>

          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td mat-cell *matCellDef="let record">
              <span class="status-display"
                    [style.color]="getStatusColor(record)"
                    [style.font-weight]="'bold'">
                <mat-icon class="status-icon">
                  {{ getStatusIcon(record) }}
                </mat-icon>
                {{ getStatusText(record) }}
              </span>
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef class="action"> Actions </th>
            <td mat-cell *matCellDef="let record">
              <div class="table-controls">
                <button [title]="'View attendance record'" (click)="onView(record)" class="btn-icon btn-icon-unstyled">
                  <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                </button>
                <button [title]="getEditTooltip(record)" (click)="onEdit(record)" class="btn-icon btn-icon-unstyled"
                        [disabled]="!isAttendanceEditable(record)">
                  <mat-icon class="material-symbols-rounded">edit</mat-icon>
                </button>
                <button [title]="getDeleteTooltip(record)" (click)="onDelete(record)" class="btn-icon btn-delete btn-icon-unstyled"
                        [disabled]="!isAttendanceEditable(record)">
                  <mat-icon class="material-symbols-rounded">delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="attendanceDisplayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: attendanceDisplayedColumns;"></tr>

          <!-- No Data Row -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell no-data" [attr.colspan]="attendanceDisplayedColumns.length">
              No attendance records found
            </td>
          </tr>
        </table>

        <mat-paginator
          #paginator
          [pageSize]="10"
          [pageSizeOptions]="[5, 10, 25, 50, 100]"
          showFirstLastButtons
          aria-label="Select page of attendance records">
        </mat-paginator>

      </div>
    </div>
  </div>
  } @else {
    <app-attendance-form
      [record]="selectedAttendanceRecord()"
      [mode]="mode()"
      (save)="onFormSave($event)"
      (cancel)="onFormCancel()"
      (edit)="onFormEdit()">
    </app-attendance-form>
  }