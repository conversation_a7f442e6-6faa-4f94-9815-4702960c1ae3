import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Unit, UnitResponse } from '../../../models/x_models/masters/unit';

@Injectable({
    providedIn: 'root'
})
export class UnitService {
    dataService = inject(DataService)

    create(data: Unit) {
        return this.dataService.post<Response>("/unit", data)
    }

    get() {
        return this.dataService.get<UnitResponse>("/unit")
    }

    getById(id: number) {
        return this.dataService.get<Unit>(`/unit/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/unit/${id}`)
    }
}
