.content {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

// Page header
.page-header {
  margin-bottom: 2rem;
  text-align: center;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .header-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

    mat-icon {
      font-size: 30px;
      width: 30px;
      height: 30px;
    }
  }

  .header-text {
    h2 {
      margin: 0;
      color: #2c3e50;
      font-weight: 600;
      font-size: 2rem;
    }

    p {
      margin: 0.5rem 0;
      color: #7f8c8d;
      font-size: 1.1rem;
    }
  }
}

// Main card
.payslip-card {
  max-width: 1200px;
  margin: 0 auto;
  border-radius: 16px !important;
  padding: 0 !important;
  overflow: hidden;
  background: white;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15) !important;
  }
}

// Employee header section
.employee-header {
  background: linear-gradient(135deg, #203664 0%, #203664 100%);
  padding: 2rem;
  color: white;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    z-index: 1;
  }

  .employee-avatar {
    background: rgba(255, 255, 255, 0.2);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    z-index: 2;

    mat-icon {
      font-size: 40px;
      width: 40px;
      height: 40px;
    }
  }

  .employee-basic-info {
    flex: 1;
    z-index: 2;

    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.8rem;
      font-weight: 600;
    }

    .employee-id {
      margin: 0 0 1rem 0;
      opacity: 0.9;
      font-size: 1.1rem;
    }

    .employee-tags {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;

      .tag {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
    }
  }

  .status-badge {
    z-index: 2;

    .badge {
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.9rem;

      &.pending {
        background: #f39c12;
        color: white;
        box-shadow: 0 2px 10px rgba(243, 156, 18, 0.3);
      }
    }
  }
}

// Details section
.details-section {
  padding: 2rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;

  mat-icon {
    color: #203664;
    font-size: 24px;
    width: 24px;
    height: 24px;
  }
}

// Employee info grid
.employee-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  .info-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: #203664;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      background: white;

      &::before {
        transform: scaleY(1);
      }
    }

    &.highlight-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;

      &::before {
        background: #ffd700;
      }

      .info-icon {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }

      .pay-amount {
        font-size: 1.4rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }

    .info-icon {
      width: 50px;
      height: 50px;
      border-radius: 10px;
      background: #e8f0fe;
      color: #667eea;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }

    .info-content {
      flex: 1;

      label {
        display: block;
        font-size: 0.85rem;
        font-weight: 600;
        color: #6c757d;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      span {
        font-size: 1.1rem;
        font-weight: 500;
        color: #2c3e50;
        word-break: break-word;
      }
    }

    // Highlight card overrides for content
    &.highlight-card .info-content {
      label {
        color: rgba(255, 255, 255, 0.8);
      }

      span {
        color: white;
      }
    }
  }
}

// Action section
.action-section {
  background: #f8f9fa;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;

    button {
      padding: 0.75rem 2rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 1rem;
      min-width: 150px;
      transition: all 0.3s ease;

      mat-icon {
        margin-right: 0.5rem;
      }

      &.approve-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
      }

      &.reject-btn {
        border: 2px solid #dc3545;
        color: #dc3545;

        &:hover {
          background: #dc3545;
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }
      }
    }
  }
}

// Notification
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.notification {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.5s ease;

  mat-icon {
    font-size: 28px;
    width: 28px;
    height: 28px;
  }
}

// Animations
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .content {
    padding: 1rem;
  }

  .employee-header {
    padding: 1.5rem;
    text-align: center;

    .employee-basic-info h3 {
      font-size: 1.5rem;
    }
  }

  .details-section {
    padding: 1.5rem;
  }

  .employee-info-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-buttons {
    flex-direction: column;

    button {
      width: 100%;
    }
  }

  .notification {
    margin: 1rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .page-header .header-content {
    flex-direction: column;
    text-align: center;
  }

  .employee-header {
    flex-direction: column;
    text-align: center;

    .employee-avatar {
      width: 60px;
      height: 60px;

      mat-icon {
        font-size: 30px;
        width: 30px;
        height: 30px;
      }
    }
  }

  .info-card {
    padding: 1rem;

    .info-icon {
      width: 40px;
      height: 40px;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }
}