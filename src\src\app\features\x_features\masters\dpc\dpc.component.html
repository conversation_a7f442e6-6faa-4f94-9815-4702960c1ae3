<mat-toolbar color="primary" class="navbar">
    <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
      <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
      <mat-icon>chevron_right</mat-icon>
      <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
      <mat-icon>chevron_right</mat-icon>
      <span aria-current="page" class="nav-active">
        @if(uniqueId() == 0) {
        {{'New' | translate}} {{menuService.activeMenu()?.title}}
        } @else {
            {{dpc()?.dpcName}}
        }
      </span>
      @if(mode() === "view") {
      <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
        <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
      </button>
      }
    </nav>
    <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
      <mat-icon>arrow_back_ios</mat-icon>
      Back
    </button>
  </mat-toolbar>
  
    <div class="card component">
      
        <form [formGroup]="form" (ngSubmit)="onSubmit()">
            <div class="form">

                <div class="field">
                    <label for="RegionId" class="required-label">{{'Region' | translate }} </label>
                    <mat-form-field appearance="outline">
                        <mat-select id="RegionId" formControlName="regionId" (openedChange)="resetSearch('location')">
                           <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'region')">
                            </mat-form-field>
                            @for (region of filteredRegions(); track region) {
                            <mat-option [value]="region.key">{{region.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.regionId.errors?.['required']) {
                            <!-- Region is required -->
                            {{'Region' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="unitId" class="required-label">{{'Unit' | translate }} </label>
                    <mat-form-field appearance="outline">
                        <mat-select id="UnitId" formControlName="unitId" (openedChange)="resetSearch('unit')">
                           <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'unit')">
                            </mat-form-field>
                            @for (unit of filteredUnites(); track unit) {
                            <mat-option [value]="unit.key">{{unit.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.unitId.errors?.['required']) {
                            <!-- Unit is required -->
                            {{'Unit' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="talukId" class="required-label"> {{'Taluk' | translate }}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="talukId" formControlName="talukId" (openedChange)="resetSearch('taluk')">
                            <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search3 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'taluk')">
                            </mat-form-field>
                            @for (taluk of filteredTaluks(); track taluk) {
                            <mat-option [value]="taluk.key">{{taluk.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.talukId.errors?.['required']) {
                            <!-- Taluk is required -->
                            {{'Taluk' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="blockId" class="required-label">{{'Block' | translate }} </label>
                    <mat-form-field appearance="outline">
                        <mat-select id="blockId" formControlName="blockId" (openedChange)="resetSearch('block')">
                            <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search4 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'block')">
                            </mat-form-field>
                            @for (block of filteredBlocks(); track block) {
                            <mat-option [value]="block.key">{{block.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.blockId.errors?.['required']) {
                            <!-- Block is required -->
                            {{'Block' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="blockId" class="required-label"> {{'Village' | translate }}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="blockId" formControlName="villageId" (openedChange)="resetSearch('village')">
                            <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search5 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'village')">
                            </mat-form-field>
                            @for (village of filteredVillages(); track village) {
                            <mat-option [value]="village.key">{{village.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.villageId.errors?.['required']) {
                            <!-- Village is required -->
                            {{'Village' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="agencyId" class="required-label"> {{'Agency' | translate }}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="agencyId" formControlName="agencyId" (openedChange)="resetSearch('agency')">
                           <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search6 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'agency')">
                            </mat-form-field>
                            @for (agency of filteredAgencies(); track agency) {
                            <mat-option [value]="agency.key">{{agency.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.agencyId.errors?.['required']) {
                            <!-- Agency is required -->
                            {{'Agency' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="Name" class="required-label">{{'DPCName' | translate }}</label>
                    <mat-form-field>
                        <input id="Name" formControlName="dpcName" matInput
                            (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="100">
                        <mat-error>
                            @if(form.controls.dpcName.errors?.['required']) {
                            <!-- DPC Name is required -->
                            {{'DPCName' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="Regionalname" class="required-label"> {{'DPCName' | translate }} {{'InTamil' | translate
                        }}</label>
                    <mat-form-field>
                        <input id="Regionalname" formControlName="dpcRegionalName" matInput
                            (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
                        <mat-error>
                            @if(form.controls.dpcRegionalName.errors?.['required']) {
                            <!-- DPC Name in tamil is required -->
                            {{'DPCName' | translate }} {{'InTamil' | translate }} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                @if(uniqueId())
                {
                <div class="field">
                    <label for="Regionalname" class="required-label"> {{'DPC' | translate}} {{'Code' | translate
                        }}</label>
                    <mat-form-field [ariaDisabled]="true">
                        <input id="Regionalname" formControlName="dpcRegionalName" matInput maxlength="100">
                        <mat-error>
                            @if(form.controls.dpcRegionalName.errors?.['required']) {
                            <!-- DPC Name in tamil is required -->
                            {{'DPC' | translate}} {{'Code' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>
                }

                <div class="field">
                    <label for="Permanentaddress" class="required-label">{{'PermanentAddress' | translate }} </label>
                    <mat-form-field>
                        <input id="Permanentaddress" formControlName="permanentAddress" matInput
                            (input)="onInput($event,'/^[,a-zA-Z0-9\s -]*$/')" maxlength="250">
                        <mat-error>
                            @if(form.controls.permanentAddress.errors?.['required']) {
                            <!-- Permanent address is required -->
                            {{'PermanentAddress' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="presentAddress" class="required-label">{{'PresentAddress' | translate }} </label>
                    <mat-form-field>
                        <input id="presentAddress" formControlName="presentAddress" matInput
                            (input)="onInput($event,'/^[,a-zA-Z0-9\s -]*$/')" maxlength="250">
                        <mat-error>
                            @if(form.controls.presentAddress.errors?.['required']) {
                            <!-- Present address is required -->
                            {{'PresentAddress' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="Pincode" class="required-label">{{'Pincode' | translate }} </label>
                    <mat-form-field>
                        <input id="Pincode" formControlName="pincode" matInput (input)="onInput($event,'/^[0-9]*$/')"
                            maxlength="6" minlength="6">
                        <mat-error>
                            @if(form.controls.pincode.errors?.['required']) {
                            <!-- Pincode is required -->
                            {{'Pincode' | translate}} {{'IsRequired' | translate}}
                            } @else if (form.controls.pincode.errors?.['minlength']) {
                            {{'InvalidFormat' | translate}}
                            }
                            @else if (form.controls.pincode.errors?.['maxlength']) {
                            {{'InvalidFormat' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>


                <div class="field">
                    <label for="dpcType" class="required-label">{{'DPCType' | translate }} </label>
                    <mat-form-field appearance="outline">
                        <mat-select id="dpcType" formControlName="dpcType">
                            <!-- <mat-option value="" disabled>Select a DPC Type</mat-option> -->
                            @for (dpc of dpcs(); track dpc) {
                            <mat-option [value]="dpc.key">{{dpc.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.dpcType.errors?.['required']) {
                            <!-- DPC Type is required -->
                            {{'DPCType' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="dpcType" class="required-label"> {{'POSType' | translate }}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="dpcType" formControlName="posType">
                            <!-- <mat-option value="" disabled>Select a POS Type</mat-option> -->
                            @for (pos of POSs(); track pos) {
                            <mat-option [value]="pos.key">{{pos.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.posType.errors?.['required']) {
                            <!-- POS Type is required -->
                            {{'POSType' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="latitude" class="required-label">{{'Latitude' | translate }}</label>
                    <mat-form-field>
                        <input id="latitude" formControlName="latitude" matInput (input)="onInput($event,'/^[0-9.]*$/')"
                            maxlength="250">
                        <mat-error>
                            @if(form.controls.latitude.errors?.['required']) {
                            <!-- Latitude is required -->
                            {{'Latitude' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="longitude" class="required-label">{{'Longitude' | translate }}</label>
                    <mat-form-field>
                        <input id="longitude" formControlName="longitude" matInput
                            (input)="onInput($event,'/^[0-9.]*$/')" maxlength="250">
                        <mat-error>
                            @if(form.controls.longitude.errors?.['required']) {
                            <!-- Longitude is required -->
                            {{'Longitude' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="fromTs" class="required-label">{{'FromDate' | translate }}</label>
                    <mat-form-field>
                        <input matInput appDateInput [matDatepicker]="picker1" formControlName="fromDate">
                        <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
                        <mat-datepicker #picker1></mat-datepicker>
                        <mat-error>
                            @if(form.controls.fromDate.errors?.['required']) {
                            <!-- From date is required -->
                            {{'FromDate' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="toTs" class="required-label">{{'ToDate' | translate }}</label>
                    <mat-form-field>
                        <input matInput appDateInput [matDatepicker]="picker2" formControlName="toDate"
                            [min]="form.value.fromDate">
                        <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
                        <mat-datepicker #picker2></mat-datepicker>
                        <mat-error>
                            @if(form.controls.toDate.errors?.['required']) {
                            <!-- To date is required -->
                            {{'ToDate' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label id="example-radio-group-label" class="required-label">{{'AuthBypassFlag' | translate
                        }}</label>
                    <mat-radio-group formControlName="authBypassFlag" aria-labelledby="example-radio-group-label"
                        class="example-radio-group">
                        <mat-radio-button class="example-radio-button" [checked]="true"
                            [value]="true">True</mat-radio-button>
                        <mat-radio-button class="example-radio-button" [checked]="false"
                            [value]="false">False</mat-radio-button>
                    </mat-radio-group>
                    <mat-error>
                        @if(form.controls.authBypassFlag.errors?.['required']) {
                        <!-- Auth bypass flag is required -->
                        {{'AuthBypassFlag' | translate}} {{'IsRequired' | translate}}
                        }
                    </mat-error>
                </div>




            </div>

            <div class="actions">
                
                @if(editable() === true) {
                @if(mode() === 'add') {
                <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
                    translate}}</button>
                }
                <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme"
                    type="submit">{{uniqueId()
                    == 0 ?
                    ('Create' | translate) : ('Update' | translate)}}</button>
                }
            </div>

        </form>
    </div>
