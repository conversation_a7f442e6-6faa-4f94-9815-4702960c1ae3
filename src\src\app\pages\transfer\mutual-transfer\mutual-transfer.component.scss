// Variables
$primary-color: #203664;
$accent-color: #203664;
$success-color: #4caf50;
$error-color: #f44336;
$warning-color: #ff9800;
$info-color: #2196f3;
$light-gray: #f5f5f5;
$border-radius: 8px;
$box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

// Container Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: calc(100vh - 100px);
}

// Card Styles
.form-card {
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  overflow: hidden;

  .card-header {
    background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
    color: white;
    padding: 24px;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;

      .header-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
      }

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }

    mat-card-subtitle {
      color: rgba(255, 255, 255, 0.8);
      margin-top: 8px;
      font-size: 14px;
    }
  }

  .progress-bar {
    height: 3px;

    ::ng-deep .mat-progress-bar-fill::after {
      background-color: $accent-color;
    }
  }

  .card-content {
    padding: 0;
  }
}

// Form Container
.form-container {
  padding: 32px;
}

// Section Styles
.section {
  margin-bottom: 40px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: $primary-color;
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 16px 0;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }

  mat-divider {
    margin-bottom: 24px;
    border-color: lighten($primary-color, 60%);
  }
}

// Form Grid
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-field {
    width: 100%;
  }

  .full-width {
    grid-column: 1 / -1;
  }
}

// Search Section
.search-section {
  margin-bottom: 24px;

  .search-field {
    width: 100%;
    max-width: 500px;
  }
}

// Employee Selection Styles
.employee-option {
  padding: 8px 0;

  .employee-main {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .employee-details {
    font-size: 12px;
    color: lighten($primary-color, 20%);
    font-style: italic;
  }
}

// Employee Info Card
.employee-info-card {
  background-color: lighten($success-color, 45%);
  border-radius: $border-radius;
  padding: 20px;
  border-left: 4px solid $success-color;
}

// Form Field Styles
mat-form-field {
  width: 100%;

  &.mat-form-field-appearance-outline {
    .mat-form-field-outline {
      color: lighten($primary-color, 40%);
    }

    &.mat-focused .mat-form-field-outline-thick {
      color: $primary-color;
    }

    .mat-form-field-label {
      color: $primary-color;
    }

    &.mat-focused .mat-form-field-label {
      color: $primary-color;
    }
  }

  .mat-form-field-suffix mat-icon {
    color: lighten($primary-color, 20%);
  }

  .mat-hint {
    color: lighten($primary-color, 30%);
    font-size: 12px;
  }

  .mat-error {
    color: $error-color;
    font-size: 12px;
  }
}

// Required Field Asterisk
.required-asterisk {
  color: $error-color;
  font-weight: bold;
  margin-left: 2px;
}

// Priority Option Styles
.priority-option {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;

  &.low {
    background-color: lighten($info-color, 40%);
    color: $info-color;
  }

  &.medium {
    background-color: lighten($warning-color, 40%);
    color: darken($warning-color, 20%);
  }

  &.high {
    background-color: lighten($error-color, 40%);
    color: $error-color;
  }

  &.urgent {
    background-color: $error-color;
    color: white;
  }
}

// Agreement Section Styles
.agreement-section {
  margin-top: 32px;
  padding: 24px;
  background-color: lighten($primary-color, 50%);
  border-radius: $border-radius;
  border-left: 4px solid $primary-color;

  .agreement-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: $primary-color;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 20px 0;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }

  .agreement-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;

    .agreement-checkbox {
      ::ng-deep .mat-checkbox-label {
        .checkbox-text {
          font-size: 14px;
          line-height: 1.4;
          color: $primary-color;
        }
      }
    }
  }

  .agreement-note {
    display: flex;
    gap: 12px;
    padding: 16px;
    background-color: lighten($info-color, 45%);
    border-radius: $border-radius;
    border-left: 3px solid $info-color;

    mat-icon {
      color: $info-color;
      font-size: 20px;
      width: 20px;
      height: 20px;
      margin-top: 2px;
    }

    p {
      margin: 0;
      font-size: 13px;
      line-height: 1.4;
      color: darken($info-color, 10%);

      strong {
        font-weight: 600;
      }
    }
  }
}

// Form Actions
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-start;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid lighten($primary-color, 60%);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
  }

  button {
    min-width: 180px;
    height: 44px;
    border-radius: $border-radius;
    font-weight: 500;
    text-transform: none;

    @media (max-width: 768px) {
      min-width: 100%;
    }

    mat-icon {
      margin-right: 8px;
    }

    mat-spinner {
      margin-right: 8px;
    }

    &.submit-btn {
      background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
      color: white;
      box-shadow: 0 2px 4px rgba($primary-color, 0.3);

      &:hover:not(:disabled) {
        box-shadow: 0 4px 8px rgba($primary-color, 0.4);
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    &.draft-btn {
      border-color: $accent-color;
      color: $accent-color;

      &:hover:not(:disabled) {
        background-color: lighten($accent-color, 45%);
      }
    }

    &.reset-btn {
      color: $error-color;

      &:hover:not(:disabled) {
        background-color: lighten($error-color, 45%);
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .container {
    padding: 16px;
  }

  .form-container {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .form-container {
    padding: 20px;
  }

  .form-card .card-header {
    padding: 20px;

    mat-card-title h1 {
      font-size: 20px;
    }
  }

  .section .section-title {
    font-size: 16px;
  }

  .agreement-section {
    padding: 20px;

    .agreement-note {
      flex-direction: column;
      gap: 8px;

      mat-icon {
        align-self: flex-start;
      }
    }
  }
}

// Animation
.form-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Snackbar Styles (Global)
::ng-deep {
  .success-snackbar {
    background-color: $success-color !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: $error-color !important;
    color: white !important;
  }

  .warning-snackbar {
    background-color: $warning-color !important;
    color: white !important;
  }
}