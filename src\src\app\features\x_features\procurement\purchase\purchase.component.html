@if (!showGrid()) {
<div class="component card">
    <div class="page-header">
        <h1>{{menuService.activeMenu()?.title}} </h1>
    </div>
    <div class="header">
        <div class="filters-wrapper">
            <mat-form-field class="search hide-subscript" appearance="outline">
                <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
                <!-- <input id="search" [formControl]="search" (keydown.enter)="onSearch()" autocomplete="off" matInput
              placeholder="Search"> -->
                <input id="search" [formControl]="search" (input)="onSearch()" autocomplete="off" matInput
                    placeholder="{{ 'Search' | translate }}">
            </mat-form-field>
            <div class="filters-more">
                @if(access()?.canAdd) {
                <button (click)="onAdd()" class="btn btn-filter" mat-icon-button>
                    <mat-icon class="icon-filter material-symbols-outlined" matPrefix>add</mat-icon>
                </button>
                }
                <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
                    <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
                </button>
            </div>
        </div>
    </div>
    <div class="content">
        <div class="table-wrapper">
            <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">
               
                <ng-container matColumnDef="farmerName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Farmer Name' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.farmerNameCode}} </td>
                </ng-container>
                <ng-container matColumnDef="dpcName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'DPC Name' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.dpcNameCode}} </td>
                </ng-container>
                <ng-container matColumnDef="tokenNo">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Token No' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.tokenNo}} </td>
                </ng-container>
                <ng-container matColumnDef="vendorName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Vendor Name' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.vendorName}} </td>
                </ng-container>
                <ng-container matColumnDef="purchaseTxnRefNo">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Purchase Transaction Reference No' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.purchaseTxnRefNo}} </td>
                </ng-container>
                <ng-container matColumnDef="purchaseTs">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Purchase Date' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.purchaseTs | date}} </td>
                </ng-container>
                <ng-container matColumnDef="totalQty">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Total Quantity' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.totalQty}} </td>
                </ng-container>
                <ng-container matColumnDef="mspAmount">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'MSP Amount(Rs)' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.mspAmount}} </td>
                </ng-container>
                 <ng-container matColumnDef="moisturePercentage">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Moisture Percentage(%)' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.moisturePercentage}} </td>
                </ng-container>
                 <ng-container matColumnDef="incentiveAmount">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Incentive Amount(Rs)' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.incentiveAmount}} </td>
                </ng-container>
                <ng-container matColumnDef="authTxnTypeName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Incentive Amount(Rs)' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.authTxnTypeName}} </td>
                </ng-container>
                 <ng-container matColumnDef="noOfBags">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'No of Bags' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.noOfBags}} </td>
                </ng-container>
                 <ng-container matColumnDef="rateCutAmount">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ 'Ratecut Amount(Rs)' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.rateCutAmount}} </td>
                </ng-container>


                <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef class="action"> {{ 'Actions' | translate }} </th>
                    <td mat-cell *matCellDef="let row">
                        <div class="table-controls">
                            @if (access()?.canView) {
                            <button title="{{'View' | translate}}" (click)="onView(row)"
                                class="btn-icon btn-icon-unstyled">
                                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                            </button>
                            }
                            <!-- @if(access()?.canEdit) {
                            <button title="{{'Edit' | translate}}" (click)="onEdit(row)"
                                class="btn-icon btn-icon-unstyled">
                                <mat-icon class="material-symbols-rounded">edit</mat-icon>
                            </button>
                            }
                            @if(access()?.canDelete) {
                            <button title="{{'Delete' | translate}}" (click)="onDelete(row)"
                                class="btn-icon btn-delete btn-icon-unstyled">
                                <mat-icon class="material-symbols-rounded">delete</mat-icon>
                            </button>
                            } -->
                        </div>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" [attr.colspan]="displayedColumns.length" style="text-align: center;padding:10px">
                      No data found
                    </td>
                  </tr>
                <!-- <tr *matNoDataRow>
              <ng-container *ngTemplateOutlet="shimmer"></ng-container>
            </tr> -->
            </table>

        </div>
        <!-- @if (dataSource().filteredData.length==0) {
        <div style="text-align: center;">No Data</div>
        } -->
        <mat-paginator class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
            aria-label="Select page of users"></mat-paginator>

        <div class="mobile-wrapper">
            <div class="un-cards">
                @for (item of dataSource().data; track item) {
                <div class="un-card">
                    <div class="desc">
                        <div class="quote-no">{{item.farmerNameCode}}</div>
                        <!-- <div style="line-height: 1em;"><span class="tracking-no">{{item.seasonName}}</span> </div> -->
                        <div class="quote-no">{{item.dpcNameCode }} &nbsp;/&nbsp; {{item.tokenNo }}</div>
                        <div class="quote-no">{{item.purchaseTxnRefNo}} &nbsp;/&nbsp; {{item.purchaseTs }}</div>
                        <div class="quote-no">{{item.paymentAmount}}</div>
                    </div>
                    <div class="actions">
                        <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu"
                            aria-label="Card actions">
                            <mat-icon>more_vert</mat-icon>
                        </button>
                        <mat-menu #menu="matMenu">

                            @if (access()?.canView) {
                            <button (click)="onView(item)" mat-menu-item>
                                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                                <span>{{ 'View' | translate }}</span>
                            </button>
                            }

                            @if(access()?.canEdit) {
                            <button (click)="onEdit(item)" mat-menu-item>
                                <mat-icon class="material-symbols-rounded">edit</mat-icon>
                                <span>{{ 'Edit' | translate }}</span>
                            </button>
                            }

                            @if(access()?.canDelete) {
                            <button (click)="onDelete(item)" mat-menu-item>
                                <mat-icon class="material-symbols-rounded">delete</mat-icon>
                                <span>{{ 'Delete' | translate }}</span>
                            </button>
                            }
                        </mat-menu>
                    </div>
                </div>
                }
            </div>
        </div>
    </div>
</div>


}
@else {
    <app-procurementdetails (closed)="close()" [id]="id()"></app-procurementdetails>
}

