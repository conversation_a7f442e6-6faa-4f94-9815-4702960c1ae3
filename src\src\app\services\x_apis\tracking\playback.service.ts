import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Hulling, HullingResponse } from '../../../models/x_models/masters/hulling';
import { PlayBackDetails } from '../../../models/x_models/tracking/playback';

@Injectable({
    providedIn: 'root'
})
export class PlaybackService {

    dataService = inject(DataService)

    // create(data: Hulling) {
    //     return this.dataService.post<Response>("/hulling", data)
    // }

    // get() {
    //     return this.dataService.get<HullingResponse>("/hulling")
    // }

    // getById(id: number) {
    //     return this.dataService.get<Hulling>(`/hulling/${id}`)
    // }

    // delete(id: number) {
    //     return this.dataService.delete<Response>(`/hulling/${id}`)
    // }

    getPlayBackDetails(vehicleId :number, start:Date, end:Date){
        return this.dataService.get<PlayBackDetails>(`/LiveTracking/tripSheet/trackinghistory/${vehicleId}/${start}/${end}`)
      }

}
