export interface Purchase {
    id: number,
    purchaseTxnRefNo: string,
    purchaseTs: string,
    totalQty: number,
    mspAmount: number,
    incentiveAmount: number,
    rateCutAmount: number,
    paymentAmount: number,
    dpcNameCode: string,
    farmerNameCode: string,
    villageNameCode: string,
    seasonName: string,
    varietyName: string,
    tokenNo: string,
    authTxnTypeName: string,
    ddswCut: number,
    issCut: number,
    moistureCut: number,
    moisturePercentage: number,
    noOfBags: number
}

export interface PurchaseResponse {
    id: number,
    purchaseTxnRefNo: string,
    purchaseTs: string,
    totalQty: number,
    mspAmount: number,
    incentiveAmount: number,
    rateCutAmount: number,
    paymentAmount: number,
    dpcDeviceId: number,
    dpcId: number,
    farmerId: number,
    seasonId: number,
    procurementTokenId: number,
    authTxnType: 1,
    lotNo: number,
    isIncentiveEligible: boolean,
    spillageQty: number,
    latitude: number,
    longitude: number,
    moisturePercentage: number,
    amountPerQtl: number,
    moistureCut: number,
    noOfBags: number,
    productId: number,
    varietyId: number,
    organic: number,
    inOrganic: number,
    ddsW: number,
    iss: number,
    admix: number,
    procurementRateId: number,
    organicCut: number,
    inorganicCut: number,
    ddswCut: number,
    issCut: number,
    dpcName: string,
    dpcDeviceName: string,
    farmerName: string,
    seasonName: string,
    productName: string,
    varietyName: string
}

export type PurchaseList = Purchase[]
