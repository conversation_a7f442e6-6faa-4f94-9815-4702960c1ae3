import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, inject, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { TalukService } from '../../../../services/x_apis/masters/taluk.service';
import { Taluk, Taluks } from '../../../../models/x_models/masters/taluk';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { TalukComponent } from './taluk.component';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-taluks',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatOption,
    MatSelect,
    CloseFilterOnBlurDirective,
    MatFormFieldModule,
     TranslateModule,
     MatToolbarModule,
    TalukComponent
  ],
  templateUrl: './taluks.component.html',
  styleUrl: './taluks.component.scss'
})
export class TaluksComponent {
  menuService = inject(MenuService)
  talukService = inject(TalukService)
  search = new FormControl("")
  taluk = signal<Taluks>({
    id: 0,
    talukName: '',
    talukRegionalName: '',
    regionName: '',
    unitName: '',
    talukLgdCode: ''
  });

  translate = inject(TranslateService);
  paginator = viewChild<MatPaginator>(MatPaginator)
  sort = viewChild<MatSort>(MatSort)
  dataSource = signal(new MatTableDataSource<Taluks>([]))
  displayedColumns: string[] = [
    'talukName',
    'talukRegionalName',
    'talukLgdCode',
    'regionName',
    'unitName',
    'actions'
  ];
  taluks = signal<Taluks[]>([])
  loading = signal(false)
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  mode = signal<'view' | 'edit' | 'add'>('add');
  showTaluk = signal(false)
  status = StatusType;

  protected readonly AppRoutes = AppRoutes;

  ngOnInit(): void {
    this.getTaluks()
  }

  async getTaluks() {
    const res = await this.talukService.get();
    this.taluks.set(res);
    this.dataSource.set(new MatTableDataSource(this.taluks()))
    setTimeout(() => {
      this.dataSource().sort = this.sort()!;
      this.dataSource().paginator = this.paginator()!
    }, 100)
  }

  onAdd() {
    this.taluk.set({
      id: 0,
      talukName: '',
      talukRegionalName: '',
      regionName: '',
      unitName: '',
      talukLgdCode: ''
    });
    this.mode.set("add")
    this.showTaluk.set(true)
  }

  onView(taluk: Taluks) {
    this.taluk.set(taluk);
    this.mode.set('view');
    this.showTaluk.set(true)
  }

  onEdit(taluk: Taluks) {
    this.taluk.set(taluk);
    this.mode.set('edit');
    this.showTaluk.set(true)
  }

  onRefresh() {
    this.getTaluks();
    this.search.setValue("");
  }

  onClose() {
    this.showTaluk.set(false)
    this.getTaluks();
    this.search.setValue("");
  }

  async onDelete(taluk: Taluks) {
    await confirmAndDelete(taluk, taluk.talukName, 'Taluk', this.talukService, () => this.getTaluks());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
