import { Injectable, signal } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ViewModeService {
  viewMode = signal<'xs' | 'sm' | 'md' | 'lg'>('lg');
  #sideNavToggledSignal = signal(true)
  sideNavToggled = this.#sideNavToggledSignal.asReadonly()

  constructor() {
    this.setViewMode(window.innerWidth);
  }

  handleResize = () => {
    this.setViewMode(window.innerWidth);
  }

  setViewMode(innerWidth: number) {
    if (innerWidth < 700) {
      this.viewMode.set('xs');
      this.closeSideNav()
    }
    // else if (innerWidth >= 600 && innerWidth < 900) {
      // this.viewMode.set('sm');
    // }
    else if (innerWidth >= 700 && innerWidth < 1300) {
      this.viewMode.set('md');
    } else {
      this.viewMode.set('lg');
    }
  }

  openSideNav() {
    this.#sideNavToggledSignal.set(true)
  }

  

  closeSideNav() {
    this.#sideNavToggledSignal.set(false)
  }
}
