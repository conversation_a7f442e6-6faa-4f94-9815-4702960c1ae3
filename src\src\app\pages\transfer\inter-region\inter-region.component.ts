import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Region } from '../../../enums/m_enums/transfer.model';
import { TransferDataService } from '../../../services/m_apis/transfer-data.service';
import { CommonModule } from '@angular/common';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatOption } from '@angular/material/select';


@Component({
  selector: 'app-inter-region',
  imports:[
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatFormField,
    MatLabel,
    MatOption

  ],
  templateUrl: './inter-region.component.html',
  styleUrls: ['./inter-region.component.scss']
})
export class InterRegionComponent implements OnInit {

  transferForm!: FormGroup;
  regions: Region[] = [];

  constructor(
    private fb: FormBuilder,
    private transferService: TransferDataService
  ) {}

  ngOnInit(): void {
    this.transferForm = this.fb.group({
      currentRegion: ['', Validators.required],
      proposedRegion: ['', Validators.required],
      proposedPosition: [''],
      relocationRequired: [false]
    });

    this.transferService.getRegions().subscribe(regions => this.regions = regions);
  }

  onSubmit(): void {
    if (this.transferForm.valid) {
      this.transferService.submitInterRegionTransfer(this.transferForm.value).subscribe(res => {
        alert('Inter-region transfer submitted successfully!');
      });
    }
  }
}
