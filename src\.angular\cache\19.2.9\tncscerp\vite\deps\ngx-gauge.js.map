{"version": 3, "sources": ["../../../../../../node_modules/ngx-gauge/fesm2022/ngx-gauge.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ViewChild, ContentChild, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"canvas\"];\nconst _c1 = [\"rLabel\"];\nconst _c2 = [\"reading\"];\nconst _c3 = [[[\"ngx-gauge-prepend\"]], [[\"ngx-gauge-value\"]], [[\"ngx-gauge-append\"]], [[\"ngx-gauge-label\"]]];\nconst _c4 = [\"ngx-gauge-prepend\", \"ngx-gauge-value\", \"ngx-gauge-append\", \"ngx-gauge-label\"];\nfunction NgxGauge_ng_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 0, [\"*ngSwitchCase\", \"true\"]);\n  }\n}\nfunction NgxGauge_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.prepend);\n  }\n}\nfunction NgxGauge_ng_content_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1, [\"*ngSwitchCase\", \"true\"]);\n  }\n}\nfunction NgxGauge_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, ctx_r0.value));\n  }\n}\nfunction NgxGauge_ng_content_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2, [\"*ngSwitchCase\", \"true\"]);\n  }\n}\nfunction NgxGauge_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.append);\n  }\n}\nfunction NgxGauge_ng_content_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 3, [\"*ngSwitchCase\", \"true\"]);\n  }\n}\nfunction NgxGauge_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.label);\n  }\n}\nfunction clamp(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  return isNaN(parseFloat(value)) || isNaN(Number(value)) ? fallbackValue : Number(value);\n}\nfunction cssUnit(value) {\n  return `${value}px`;\n}\nfunction isNumber(value) {\n  return value != undefined && !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\nclass NgxGaugeAppend {\n  static {\n    this.ɵfac = function NgxGaugeAppend_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgxGaugeAppend)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgxGaugeAppend,\n      selectors: [[\"ngx-gauge-append\"]],\n      exportAs: [\"ngxGaugeAppend\"],\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGaugeAppend, [{\n    type: Directive,\n    args: [{\n      selector: \"ngx-gauge-append\",\n      exportAs: \"ngxGaugeAppend\",\n      standalone: false\n    }]\n  }], null, null);\n})();\nclass NgxGaugePrepend {\n  static {\n    this.ɵfac = function NgxGaugePrepend_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgxGaugePrepend)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgxGaugePrepend,\n      selectors: [[\"ngx-gauge-prepend\"]],\n      exportAs: [\"ngxGaugePrepend\"],\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGaugePrepend, [{\n    type: Directive,\n    args: [{\n      selector: \"ngx-gauge-prepend\",\n      exportAs: \"ngxGaugePrepend\",\n      standalone: false\n    }]\n  }], null, null);\n})();\nclass NgxGaugeValue {\n  static {\n    this.ɵfac = function NgxGaugeValue_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgxGaugeValue)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgxGaugeValue,\n      selectors: [[\"ngx-gauge-value\"]],\n      exportAs: [\"ngxGaugeValue\"],\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGaugeValue, [{\n    type: Directive,\n    args: [{\n      selector: \"ngx-gauge-value\",\n      exportAs: \"ngxGaugeValue\",\n      standalone: false\n    }]\n  }], null, null);\n})();\nclass NgxGaugeLabel {\n  static {\n    this.ɵfac = function NgxGaugeLabel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgxGaugeLabel)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgxGaugeLabel,\n      selectors: [[\"ngx-gauge-label\"]],\n      exportAs: [\"ngxGaugeLabel\"],\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGaugeLabel, [{\n    type: Directive,\n    args: [{\n      selector: \"ngx-gauge-label\",\n      exportAs: \"ngxGaugeLabel\",\n      standalone: false\n    }]\n  }], null, null);\n})();\nconst DEFAULTS = {\n  MIN: 0,\n  MAX: 100,\n  TYPE: 'arch',\n  THICK: 4,\n  FOREGROUND_COLOR: 'rgba(0, 150, 136, 1)',\n  BACKGROUND_COLOR: 'rgba(0, 0, 0, 0.1)',\n  CAP: 'butt',\n  SIZE: 200\n};\nclass NgxGauge {\n  get size() {\n    return this._size;\n  }\n  set size(value) {\n    this._size = coerceNumberProperty(value);\n  }\n  get margin() {\n    return this._margin;\n  }\n  set margin(value) {\n    this._margin = coerceNumberProperty(value);\n  }\n  get min() {\n    return this._min;\n  }\n  set min(value) {\n    this._min = coerceNumberProperty(value, DEFAULTS.MIN);\n  }\n  get animate() {\n    return this._animate;\n  }\n  set animate(value) {\n    this._animate = coerceBooleanProperty(value);\n  }\n  get max() {\n    return this._max;\n  }\n  set max(value) {\n    this._max = coerceNumberProperty(value, DEFAULTS.MAX);\n  }\n  get value() {\n    return this._value;\n  }\n  set value(val) {\n    this._value = coerceNumberProperty(val);\n  }\n  constructor(_elementRef, _renderer) {\n    this._elementRef = _elementRef;\n    this._renderer = _renderer;\n    this._size = DEFAULTS.SIZE;\n    this._min = DEFAULTS.MIN;\n    this._max = DEFAULTS.MAX;\n    this._animate = true;\n    this._margin = 0;\n    this._initialized = false;\n    this._animationRequestID = 0;\n    this.ariaLabel = '';\n    this.ariaLabelledby = null;\n    this.type = DEFAULTS.TYPE;\n    this.cap = DEFAULTS.CAP;\n    this.thick = DEFAULTS.THICK;\n    this.foregroundColor = DEFAULTS.FOREGROUND_COLOR;\n    this.backgroundColor = DEFAULTS.BACKGROUND_COLOR;\n    // { \"40\" : { color: \"green\", bgOpacity: .2 }, ... }\n    this.thresholds = Object.create(null);\n    // { \"25\": { color: '#ccc', type: 'line', size: 8, label: \"25 lbs\" }, ... }\n    this.markers = Object.create(null);\n    this._value = 0;\n    this.duration = 1200;\n  }\n  ngOnInit() {\n    // if markers are to be added, but no margin specified then here we add 10 px.\n    if (this.markers && Object.keys(this.markers).length > 0 && !this._margin) this._margin = 10;\n  }\n  ngOnChanges(changes) {\n    const isCanvasPropertyChanged = changes['thick'] || changes['type'] || changes['cap'] || changes['size'];\n    const isDataChanged = changes['value'] || changes['min'] || changes['max'];\n    if (this._initialized) {\n      if (isDataChanged) {\n        let nv;\n        if (changes['value']) {\n          nv = Number(changes['value'].currentValue);\n          nv = isNaN(nv) ? 0 : nv;\n          const prevVal = Number(changes['value'].previousValue);\n          this._oldChangeVal = isNaN(prevVal) ? this._oldChangeVal : prevVal;\n        }\n        this._update(nv, this._oldChangeVal);\n      }\n      if (isCanvasPropertyChanged) {\n        this._destroy();\n        this._init();\n      }\n    }\n  }\n  _updateSize() {\n    this._renderer.setStyle(this._elementRef.nativeElement, 'width', cssUnit(this._getWidth()));\n    this._renderer.setStyle(this._elementRef.nativeElement, 'height', cssUnit(this._getCanvasHeight()));\n    this._canvas.nativeElement.width = this._getWidth();\n    this._canvas.nativeElement.height = this._getCanvasHeight();\n    this._renderer.setStyle(this._label.nativeElement, 'transform', 'translateY(' + (this.size / 3 * 2 - this.size / 13 / 4) + 'px)');\n    this._renderer.setStyle(this._reading.nativeElement, 'transform', 'translateY(' + (this.size / 2 - this.size * 0.22 / 2) + 'px)');\n  }\n  ngAfterViewInit() {\n    if (this._canvas) {\n      this._init();\n    }\n  }\n  ngOnDestroy() {\n    this._destroy();\n  }\n  _getBounds(type) {\n    let head, tail, start, end;\n    if (type == 'semi') {\n      head = Math.PI;\n      tail = 2 * Math.PI;\n      start = 180;\n      end = 360;\n    } else if (type == 'full') {\n      head = 1.5 * Math.PI;\n      tail = 3.5 * Math.PI;\n      start = 270;\n      end = start + 360;\n    } else if (type === 'arch') {\n      head = 0.8 * Math.PI;\n      tail = 2.2 * Math.PI;\n      start = 180 - 0.2 * 180;\n      end = 360 + 0.2 * 180;\n    }\n    return {\n      head,\n      tail,\n      start,\n      end\n    };\n  }\n  _drawShell(start, middle, tail, color) {\n    let center = this._getCenter(),\n      radius = this._getRadius();\n    if (this._initialized) {\n      this._clear();\n      this._drawMarkersAndTicks();\n      let ranges = this._getBackgroundColorRanges();\n      this._context.lineWidth = this.thick;\n      if (ranges && ranges.length > 0) {\n        // if background color is not specified then use default background, unless opacity is provided in which case use the color\n        // and opactity against color, to form the background color.\n        this._context.lineCap = 'butt';\n        for (let i = 0; i < ranges.length; ++i) {\n          let r = ranges[i];\n          this._context.beginPath();\n          this._context.strokeStyle = r.backgroundColor ? r.backgroundColor : r.bgOpacity ? r.color : this.backgroundColor;\n          if (r.bgOpacity !== undefined && r.bgOpacity !== null) {\n            this._context.globalAlpha = r.bgOpacity;\n          }\n          this._context.arc(center.x, center.y, radius, this._getDisplacement(r.start), this._getDisplacement(r.end), false);\n          this._context.stroke();\n          this._context.globalAlpha = 1;\n        }\n      } else {\n        this._context.lineCap = this.cap;\n        this._context.beginPath();\n        this._context.strokeStyle = this.backgroundColor;\n        this._context.arc(center.x, center.y, radius, start, tail, false);\n        this._context.stroke();\n      }\n      this._drawFill(start, middle, tail, color);\n    }\n  }\n  _drawFill(start, middle, tail, color) {\n    let center = this._getCenter(),\n      radius = this._getRadius();\n    this._context.lineCap = this.cap;\n    this._context.lineWidth = this.thick;\n    middle = Math.max(middle, start); // never below 0%\n    middle = Math.min(middle, tail); // never exceed 100%\n    this._context.lineCap = this.cap;\n    this._context.lineWidth = this.thick;\n    this._context.beginPath();\n    this._context.strokeStyle = color;\n    this._context.arc(center.x, center.y, radius, start, middle, false);\n    this._context.stroke();\n  }\n  _addMarker(angle, color, label, type, len, font) {\n    var rad = angle * Math.PI / 180;\n    let offset = 2;\n    if (!len) len = 8;\n    if (!type) type = 'line';\n    let center = this._getCenter(),\n      radius = this._getRadius();\n    let x = (radius + this.thick / 2 + offset) * Math.cos(rad) + center.x;\n    let y = (radius + this.thick / 2 + offset) * Math.sin(rad) + center.y;\n    let x2 = (radius + this.thick / 2 + offset + len) * Math.cos(rad) + center.x;\n    let y2 = (radius + this.thick / 2 + offset + len) * Math.sin(rad) + center.y;\n    if (type == 'triangle') {\n      //Draw the triangle marker\n      this._context.beginPath();\n      this._context.strokeStyle = color;\n      this._context.moveTo(x, y);\n      this._context.lineWidth = 1;\n      let a2 = angle - 45;\n      let a3 = angle + 45;\n      if (a2 < 0) a2 += 360;\n      if (a2 > 360) a2 -= 360;\n      if (a3 < 0) a3 += 360;\n      if (a3 > 360) a3 -= 360;\n      let rad2 = a2 * Math.PI / 180;\n      let x3 = len * Math.cos(rad2) + x;\n      let y3 = len * Math.sin(rad2) + y;\n      this._context.lineTo(x3, y3);\n      let rad3 = a3 * Math.PI / 180;\n      let x4 = len * Math.cos(rad3) + x;\n      let y4 = len * Math.sin(rad3) + y;\n      this._context.lineTo(x4, y4);\n      this._context.lineTo(x, y);\n      this._context.closePath();\n      this._context.stroke();\n      this._context.fillStyle = color;\n      this._context.fill();\n    } else {\n      //line\n      this._context.beginPath();\n      this._context.lineWidth = .5;\n      this._context.strokeStyle = color;\n      this._context.moveTo(x, y);\n      this._context.lineTo(x2, y2);\n      this._context.closePath();\n      this._context.stroke();\n    }\n    if (label) {\n      this._context.save();\n      this._context.translate(x2, y2);\n      this._context.rotate((angle + 90) * (Math.PI / 180));\n      this._context.textAlign = \"center\";\n      this._context.font = font ? font : '13px Arial';\n      this._context.fillText(label, 0, -3);\n      this._context.restore();\n    }\n  }\n  _clear() {\n    this._context.clearRect(0, 0, this._getWidth(), this._getHeight());\n  }\n  _getWidth() {\n    return this.size;\n  }\n  _getHeight() {\n    return this.size;\n  }\n  // canvas height will be shorter for type 'semi' and 'arch'\n  _getCanvasHeight() {\n    return this.type == 'arch' || this.type == 'semi' ? 0.85 * this._getHeight() : this._getHeight();\n  }\n  _getRadius() {\n    const center = this._getCenter();\n    var rad = center.x - this.thick;\n    if (this._margin > 0) rad -= this._margin;\n    return rad;\n  }\n  _getCenter() {\n    var x = this._getWidth() / 2,\n      y = this._getHeight() / 2;\n    return {\n      x,\n      y\n    };\n  }\n  _init() {\n    this._context = this._canvas.nativeElement.getContext('2d');\n    this._initialized = true;\n    this._updateSize();\n    this._create();\n  }\n  _destroy() {\n    if (this._animationRequestID) {\n      window.cancelAnimationFrame(this._animationRequestID);\n      this._animationRequestID = 0;\n    }\n    this._clear();\n    this._context = null;\n    this._initialized = false;\n  }\n  _getForegroundColorByRange(value) {\n    const thresh = this._getThresholdMatchForValue(value);\n    return thresh && thresh.color ? thresh.color : this.foregroundColor;\n  }\n  _getThresholdMatchForValue(value) {\n    const match = Object.keys(this.thresholds).filter(function (item) {\n      return isNumber(item) && Number(item) <= value;\n    }).sort((a, b) => Number(a) - Number(b)).reverse()[0];\n    if (match !== undefined) {\n      const thresh = this.thresholds[match];\n      const t = {\n        color: thresh.color,\n        backgroundColor: thresh.backgroundColor,\n        bgOpacity: thresh.bgOpacity,\n        start: Number(match),\n        end: this._getNextThreshold(Number(match))\n      };\n      return t;\n    }\n  }\n  _getNextThreshold(value) {\n    const match = Object.keys(this.thresholds).filter(function (item) {\n      return isNumber(item) && Number(item) > value;\n    }).sort((a, b) => Number(a) - Number(b));\n    if (match && match[0] !== undefined) {\n      return Number(match[0]);\n    } else {\n      return this.max;\n    }\n  }\n  _getBackgroundColorRanges() {\n    let i = 0,\n      ranges = [];\n    do {\n      let thresh = this._getThresholdMatchForValue(i);\n      if (thresh) {\n        ranges.push({\n          start: thresh.start,\n          end: thresh.end,\n          color: thresh.color,\n          backgroundColor: thresh.backgroundColor,\n          bgOpacity: thresh.bgOpacity\n        });\n        i = thresh.end;\n        if (i >= this.max) break;\n      } else break;\n    } while (true);\n    return ranges;\n  }\n  _getDisplacement(v) {\n    let type = this.type,\n      bounds = this._getBounds(type),\n      min = this.min,\n      max = this.max,\n      start = bounds.head,\n      value = clamp(v, this.min, this.max),\n      unit = (bounds.tail - bounds.head) / (max - min),\n      displacement = unit * (value - min);\n    return start + displacement;\n  }\n  _create(nv, ov) {\n    const self = this;\n    const type = this.type;\n    const bounds = this._getBounds(type);\n    const duration = this.duration;\n    const min = this.min;\n    const max = this.max;\n    const value = clamp(this.value, min, max);\n    const start = bounds.head;\n    const unit = (bounds.tail - bounds.head) / (max - min);\n    let displacement = unit * (value - min);\n    const tail = bounds.tail;\n    const color = this._getForegroundColorByRange(value);\n    let startTime;\n    if (self._animationRequestID) {\n      window.cancelAnimationFrame(self._animationRequestID);\n    }\n    const animate = timestamp => {\n      timestamp = timestamp || new Date().getTime();\n      const runtime = timestamp - startTime;\n      const progress = Math.min(runtime / duration, 1);\n      const previousProgress = ov ? (ov - min) * unit : 0;\n      const middle = start + previousProgress + displacement * progress;\n      self._drawShell(start, middle, tail, color);\n      if (self._animationRequestID && runtime < duration) {\n        self._animationRequestID = window.requestAnimationFrame(ts => animate(ts));\n      } else {\n        window.cancelAnimationFrame(self._animationRequestID);\n      }\n    };\n    if (this._animate) {\n      if (nv !== undefined && ov !== undefined && ov !== 0) {\n        displacement = unit * nv - unit * ov;\n      }\n      self._animationRequestID = window.requestAnimationFrame(timestamp => {\n        startTime = timestamp || new Date().getTime();\n        animate(startTime);\n      });\n    } else {\n      self._drawShell(start, start + displacement, tail, color);\n    }\n  }\n  _drawMarkersAndTicks() {\n    /*\n     * example:\n    this.markers = {\n        '-10': {\n            color: '#555',\n            size: 5,\n            label: '-10',\n            font: '11px verdana',\n            type: 'line',\n        },\n        '10': {\n            color: '#555',\n            size: 5,\n            label: '10',\n            font: '11px verdana',\n            type: 'line',\n        },\n        '20': {\n            color: '#555',\n            size: 5,\n            label: '20',\n            type: 'line',\n        },\n    };\n    */\n    if (this.markers) {\n      const bounds = this._getBounds(this.type);\n      const degrees = bounds.end - bounds.start;\n      const perD = degrees / (this.max - this.min);\n      for (const mv in this.markers) {\n        const n = Number(mv) - this.min;\n        const angle = bounds.start + n * perD;\n        const m = this.markers[mv];\n        this._addMarker(angle, m.color, m.label, m.type, m.size, m.font);\n      }\n    }\n  }\n  _update(nv, ov) {\n    this._clear();\n    this._create(nv, ov);\n  }\n  static {\n    this.ɵfac = function NgxGauge_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgxGauge)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgxGauge,\n      selectors: [[\"ngx-gauge\"]],\n      contentQueries: function NgxGauge_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NgxGaugeLabel, 5);\n          i0.ɵɵcontentQuery(dirIndex, NgxGaugePrepend, 5);\n          i0.ɵɵcontentQuery(dirIndex, NgxGaugeAppend, 5);\n          i0.ɵɵcontentQuery(dirIndex, NgxGaugeValue, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._prependChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._appendChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._valueDisplayChild = _t.first);\n        }\n      },\n      viewQuery: function NgxGauge_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._canvas = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._reading = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"slider\", \"aria-readonly\", \"true\"],\n      hostVars: 7,\n      hostBindings: function NgxGauge_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-valuemin\", ctx.min)(\"aria-valuemax\", ctx.max)(\"aria-valuenow\", ctx.value)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby);\n          i0.ɵɵclassProp(\"ngx-gauge-meter\", true);\n        }\n      },\n      inputs: {\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        size: \"size\",\n        margin: \"margin\",\n        min: \"min\",\n        animate: \"animate\",\n        max: \"max\",\n        type: \"type\",\n        cap: \"cap\",\n        thick: \"thick\",\n        label: \"label\",\n        append: \"append\",\n        prepend: \"prepend\",\n        foregroundColor: \"foregroundColor\",\n        backgroundColor: \"backgroundColor\",\n        thresholds: \"thresholds\",\n        markers: \"markers\",\n        value: \"value\",\n        duration: \"duration\"\n      },\n      standalone: false,\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c4,\n      decls: 17,\n      vars: 16,\n      consts: [[\"reading\", \"\"], [\"rLabel\", \"\"], [\"canvas\", \"\"], [1, \"reading-block\"], [1, \"reading-affix\", 3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [3, \"ngSwitch\"], [1, \"reading-label\", 3, \"ngSwitch\"]],\n      template: function NgxGauge_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵelementStart(0, \"div\", 3, 0)(2, \"u\", 4);\n          i0.ɵɵtemplate(3, NgxGauge_ng_content_3_Template, 1, 0, \"ng-content\", 5)(4, NgxGauge_ng_container_4_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementContainerStart(5, 6);\n          i0.ɵɵtemplate(6, NgxGauge_ng_content_6_Template, 1, 0, \"ng-content\", 5)(7, NgxGauge_ng_container_7_Template, 3, 3, \"ng-container\", 5);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementStart(8, \"u\", 4);\n          i0.ɵɵtemplate(9, NgxGauge_ng_content_9_Template, 1, 0, \"ng-content\", 5)(10, NgxGauge_ng_container_10_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7, 1);\n          i0.ɵɵtemplate(13, NgxGauge_ng_content_13_Template, 1, 0, \"ng-content\", 5)(14, NgxGauge_ng_container_14_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"canvas\", null, 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"font-size\", (ctx.size - ctx.margin * 2) * 0.22 + \"px\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngSwitch\", ctx._prependChild != null);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitch\", ctx._valueDisplayChild != null);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitch\", ctx._appendChild != null);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"font-size\", (ctx.size - ctx.margin * 2) / 13 + \"px\");\n          i0.ɵɵproperty(\"ngSwitch\", ctx._labelChild != null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngSwitchCase\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", false);\n        }\n      },\n      dependencies: [i1.NgSwitch, i1.NgSwitchCase, i1.DecimalPipe],\n      styles: [\".ngx-gauge-meter{display:inline-block;text-align:center;position:relative}.reading-block{position:absolute;width:100%;font-weight:400;white-space:nowrap;text-align:center;overflow:hidden;text-overflow:ellipsis}.reading-label{font-family:inherit;width:100%;display:inline-block;position:absolute;text-align:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-weight:400}.reading-affix{text-decoration:none;font-size:.6em;opacity:.8;font-weight:200;padding:0 .18em}.reading-affix:first-child{padding-left:0}.reading-affix:last-child{padding-right:0}\\n\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGauge, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-gauge',\n      host: {\n        'role': 'slider',\n        'aria-readonly': 'true',\n        '[class.ngx-gauge-meter]': 'true',\n        '[attr.aria-valuemin]': 'min',\n        '[attr.aria-valuemax]': 'max',\n        '[attr.aria-valuenow]': 'value',\n        '[attr.aria-label]': 'ariaLabel',\n        '[attr.aria-labelledby]': 'ariaLabelledby'\n      },\n      encapsulation: ViewEncapsulation.None,\n      standalone: false,\n      template: \"<div class=\\\"reading-block\\\" #reading [style.fontSize]=\\\"(size-(margin*2)) * 0.22 + 'px'\\\">\\n  <!-- This block can not be indented correctly, because line breaks cause layout spacing, related problem: https://pt.stackoverflow.com/q/276760/2998 -->\\n  <u class=\\\"reading-affix\\\" [ngSwitch]=\\\"_prependChild != null\\\"><ng-content select=\\\"ngx-gauge-prepend\\\" *ngSwitchCase=\\\"true\\\"></ng-content><ng-container *ngSwitchCase=\\\"false\\\">{{prepend}}</ng-container></u><ng-container [ngSwitch]=\\\"_valueDisplayChild != null\\\"><ng-content *ngSwitchCase=\\\"true\\\" select=\\\"ngx-gauge-value\\\"></ng-content><ng-container *ngSwitchCase=\\\"false\\\">{{value | number}}</ng-container></ng-container><u class=\\\"reading-affix\\\" [ngSwitch]=\\\"_appendChild != null\\\"><ng-content select=\\\"ngx-gauge-append\\\" *ngSwitchCase=\\\"true\\\"></ng-content><ng-container *ngSwitchCase=\\\"false\\\">{{append}}</ng-container></u>\\n</div>\\n<div class=\\\"reading-label\\\" #rLabel\\n     [style.fontSize]=\\\"(size-(margin*2)) / 13 + 'px'\\\"\\n     [ngSwitch]=\\\"_labelChild != null\\\">\\n  <ng-content select=\\\"ngx-gauge-label\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n  <ng-container *ngSwitchCase=\\\"false\\\">{{label}}</ng-container>\\n</div>\\n<canvas #canvas></canvas>\\n\",\n      styles: [\".ngx-gauge-meter{display:inline-block;text-align:center;position:relative}.reading-block{position:absolute;width:100%;font-weight:400;white-space:nowrap;text-align:center;overflow:hidden;text-overflow:ellipsis}.reading-label{font-family:inherit;width:100%;display:inline-block;position:absolute;text-align:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-weight:400}.reading-affix{text-decoration:none;font-size:.6em;opacity:.8;font-weight:200;padding:0 .18em}.reading-affix:first-child{padding-left:0}.reading-affix:last-child{padding-right:0}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], {\n    _canvas: [{\n      type: ViewChild,\n      args: ['canvas', {\n        static: true\n      }]\n    }],\n    _label: [{\n      type: ViewChild,\n      args: ['rLabel', {\n        static: true\n      }]\n    }],\n    _reading: [{\n      type: ViewChild,\n      args: ['reading', {\n        static: true\n      }]\n    }],\n    _labelChild: [{\n      type: ContentChild,\n      args: [NgxGaugeLabel]\n    }],\n    _prependChild: [{\n      type: ContentChild,\n      args: [NgxGaugePrepend]\n    }],\n    _appendChild: [{\n      type: ContentChild,\n      args: [NgxGaugeAppend]\n    }],\n    _valueDisplayChild: [{\n      type: ContentChild,\n      args: [NgxGaugeValue]\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    size: [{\n      type: Input\n    }],\n    margin: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    animate: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    cap: [{\n      type: Input\n    }],\n    thick: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    append: [{\n      type: Input\n    }],\n    prepend: [{\n      type: Input\n    }],\n    foregroundColor: [{\n      type: Input\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    thresholds: [{\n      type: Input\n    }],\n    markers: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    duration: [{\n      type: Input\n    }]\n  });\n})();\nclass NgxGaugeModule {\n  static {\n    this.ɵfac = function NgxGaugeModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgxGaugeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NgxGaugeModule,\n      declarations: [NgxGauge, NgxGaugeAppend, NgxGaugePrepend, NgxGaugeValue, NgxGaugeLabel],\n      imports: [CommonModule],\n      exports: [NgxGauge, NgxGaugeAppend, NgxGaugePrepend, NgxGaugeValue, NgxGaugeLabel]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGaugeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [NgxGauge, NgxGaugeAppend, NgxGaugePrepend, NgxGaugeValue, NgxGaugeLabel],\n      exports: [NgxGauge, NgxGaugeAppend, NgxGaugePrepend, NgxGaugeValue, NgxGaugeLabel]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public APIs of ngx-gauge\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgxGauge, NgxGaugeAppend, NgxGaugeLabel, NgxGaugeModule, NgxGaugePrepend, NgxGaugeValue };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,CAAC;AAC1G,IAAM,MAAM,CAAC,qBAAqB,mBAAmB,oBAAoB,iBAAiB;AAC1F,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,GAAG,CAAC,iBAAiB,MAAM,CAAC;AAAA,EACjD;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,GAAG,CAAC,iBAAiB,MAAM,CAAC;AAAA,EACjD;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,QAAQ;AACrB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,OAAO,KAAK,CAAC;AAAA,EACzD;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,GAAG,CAAC,iBAAiB,MAAM,CAAC;AAAA,EACjD;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,GAAG,CAAC,iBAAiB,MAAM,CAAC;AAAA,EACjD;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,MAAM,OAAO,KAAK,KAAK;AAC9B,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;AACA,SAAS,sBAAsB,OAAO;AACpC,SAAO,SAAS,QAAQ,GAAG,KAAK,OAAO;AACzC;AACA,SAAS,qBAAqB,OAAO,gBAAgB,GAAG;AACtD,SAAO,MAAM,WAAW,KAAK,CAAC,KAAK,MAAM,OAAO,KAAK,CAAC,IAAI,gBAAgB,OAAO,KAAK;AACxF;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,GAAG,KAAK;AACjB;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,SAAS,UAAa,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AAChF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,WAAW;AAAA,EACf,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AAAA,EACP,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,KAAK;AAAA,EACL,MAAM;AACR;AACA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ,qBAAqB,KAAK;AAAA,EACzC;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU,qBAAqB,KAAK;AAAA,EAC3C;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,OAAO;AACb,SAAK,OAAO,qBAAqB,OAAO,SAAS,GAAG;AAAA,EACtD;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,sBAAsB,KAAK;AAAA,EAC7C;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,OAAO;AACb,SAAK,OAAO,qBAAqB,OAAO,SAAS,GAAG;AAAA,EACtD;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS,qBAAqB,GAAG;AAAA,EACxC;AAAA,EACA,YAAY,aAAa,WAAW;AAClC,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,SAAS;AACrB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAC3B,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,MAAM,SAAS;AACpB,SAAK,QAAQ,SAAS;AACtB,SAAK,kBAAkB,SAAS;AAChC,SAAK,kBAAkB,SAAS;AAEhC,SAAK,aAAa,uBAAO,OAAO,IAAI;AAEpC,SAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AAET,QAAI,KAAK,WAAW,OAAO,KAAK,KAAK,OAAO,EAAE,SAAS,KAAK,CAAC,KAAK,QAAS,MAAK,UAAU;AAAA,EAC5F;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,0BAA0B,QAAQ,OAAO,KAAK,QAAQ,MAAM,KAAK,QAAQ,KAAK,KAAK,QAAQ,MAAM;AACvG,UAAM,gBAAgB,QAAQ,OAAO,KAAK,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACzE,QAAI,KAAK,cAAc;AACrB,UAAI,eAAe;AACjB,YAAI;AACJ,YAAI,QAAQ,OAAO,GAAG;AACpB,eAAK,OAAO,QAAQ,OAAO,EAAE,YAAY;AACzC,eAAK,MAAM,EAAE,IAAI,IAAI;AACrB,gBAAM,UAAU,OAAO,QAAQ,OAAO,EAAE,aAAa;AACrD,eAAK,gBAAgB,MAAM,OAAO,IAAI,KAAK,gBAAgB;AAAA,QAC7D;AACA,aAAK,QAAQ,IAAI,KAAK,aAAa;AAAA,MACrC;AACA,UAAI,yBAAyB;AAC3B,aAAK,SAAS;AACd,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,SAAS,KAAK,YAAY,eAAe,SAAS,QAAQ,KAAK,UAAU,CAAC,CAAC;AAC1F,SAAK,UAAU,SAAS,KAAK,YAAY,eAAe,UAAU,QAAQ,KAAK,iBAAiB,CAAC,CAAC;AAClG,SAAK,QAAQ,cAAc,QAAQ,KAAK,UAAU;AAClD,SAAK,QAAQ,cAAc,SAAS,KAAK,iBAAiB;AAC1D,SAAK,UAAU,SAAS,KAAK,OAAO,eAAe,aAAa,iBAAiB,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,KAAK,KAAK,KAAK;AAChI,SAAK,UAAU,SAAS,KAAK,SAAS,eAAe,aAAa,iBAAiB,KAAK,OAAO,IAAI,KAAK,OAAO,OAAO,KAAK,KAAK;AAAA,EAClI;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS;AAChB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,WAAW,MAAM;AACf,QAAI,MAAM,MAAM,OAAO;AACvB,QAAI,QAAQ,QAAQ;AAClB,aAAO,KAAK;AACZ,aAAO,IAAI,KAAK;AAChB,cAAQ;AACR,YAAM;AAAA,IACR,WAAW,QAAQ,QAAQ;AACzB,aAAO,MAAM,KAAK;AAClB,aAAO,MAAM,KAAK;AAClB,cAAQ;AACR,YAAM,QAAQ;AAAA,IAChB,WAAW,SAAS,QAAQ;AAC1B,aAAO,MAAM,KAAK;AAClB,aAAO,MAAM,KAAK;AAClB,cAAQ,MAAM,MAAM;AACpB,YAAM,MAAM,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,OAAO,QAAQ,MAAM,OAAO;AACrC,QAAI,SAAS,KAAK,WAAW,GAC3B,SAAS,KAAK,WAAW;AAC3B,QAAI,KAAK,cAAc;AACrB,WAAK,OAAO;AACZ,WAAK,qBAAqB;AAC1B,UAAI,SAAS,KAAK,0BAA0B;AAC5C,WAAK,SAAS,YAAY,KAAK;AAC/B,UAAI,UAAU,OAAO,SAAS,GAAG;AAG/B,aAAK,SAAS,UAAU;AACxB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,cAAI,IAAI,OAAO,CAAC;AAChB,eAAK,SAAS,UAAU;AACxB,eAAK,SAAS,cAAc,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,YAAY,EAAE,QAAQ,KAAK;AACjG,cAAI,EAAE,cAAc,UAAa,EAAE,cAAc,MAAM;AACrD,iBAAK,SAAS,cAAc,EAAE;AAAA,UAChC;AACA,eAAK,SAAS,IAAI,OAAO,GAAG,OAAO,GAAG,QAAQ,KAAK,iBAAiB,EAAE,KAAK,GAAG,KAAK,iBAAiB,EAAE,GAAG,GAAG,KAAK;AACjH,eAAK,SAAS,OAAO;AACrB,eAAK,SAAS,cAAc;AAAA,QAC9B;AAAA,MACF,OAAO;AACL,aAAK,SAAS,UAAU,KAAK;AAC7B,aAAK,SAAS,UAAU;AACxB,aAAK,SAAS,cAAc,KAAK;AACjC,aAAK,SAAS,IAAI,OAAO,GAAG,OAAO,GAAG,QAAQ,OAAO,MAAM,KAAK;AAChE,aAAK,SAAS,OAAO;AAAA,MACvB;AACA,WAAK,UAAU,OAAO,QAAQ,MAAM,KAAK;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,UAAU,OAAO,QAAQ,MAAM,OAAO;AACpC,QAAI,SAAS,KAAK,WAAW,GAC3B,SAAS,KAAK,WAAW;AAC3B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,SAAS,YAAY,KAAK;AAC/B,aAAS,KAAK,IAAI,QAAQ,KAAK;AAC/B,aAAS,KAAK,IAAI,QAAQ,IAAI;AAC9B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,SAAS,YAAY,KAAK;AAC/B,SAAK,SAAS,UAAU;AACxB,SAAK,SAAS,cAAc;AAC5B,SAAK,SAAS,IAAI,OAAO,GAAG,OAAO,GAAG,QAAQ,OAAO,QAAQ,KAAK;AAClE,SAAK,SAAS,OAAO;AAAA,EACvB;AAAA,EACA,WAAW,OAAO,OAAO,OAAO,MAAM,KAAK,MAAM;AAC/C,QAAI,MAAM,QAAQ,KAAK,KAAK;AAC5B,QAAI,SAAS;AACb,QAAI,CAAC,IAAK,OAAM;AAChB,QAAI,CAAC,KAAM,QAAO;AAClB,QAAI,SAAS,KAAK,WAAW,GAC3B,SAAS,KAAK,WAAW;AAC3B,QAAI,KAAK,SAAS,KAAK,QAAQ,IAAI,UAAU,KAAK,IAAI,GAAG,IAAI,OAAO;AACpE,QAAI,KAAK,SAAS,KAAK,QAAQ,IAAI,UAAU,KAAK,IAAI,GAAG,IAAI,OAAO;AACpE,QAAI,MAAM,SAAS,KAAK,QAAQ,IAAI,SAAS,OAAO,KAAK,IAAI,GAAG,IAAI,OAAO;AAC3E,QAAI,MAAM,SAAS,KAAK,QAAQ,IAAI,SAAS,OAAO,KAAK,IAAI,GAAG,IAAI,OAAO;AAC3E,QAAI,QAAQ,YAAY;AAEtB,WAAK,SAAS,UAAU;AACxB,WAAK,SAAS,cAAc;AAC5B,WAAK,SAAS,OAAO,GAAG,CAAC;AACzB,WAAK,SAAS,YAAY;AAC1B,UAAI,KAAK,QAAQ;AACjB,UAAI,KAAK,QAAQ;AACjB,UAAI,KAAK,EAAG,OAAM;AAClB,UAAI,KAAK,IAAK,OAAM;AACpB,UAAI,KAAK,EAAG,OAAM;AAClB,UAAI,KAAK,IAAK,OAAM;AACpB,UAAI,OAAO,KAAK,KAAK,KAAK;AAC1B,UAAI,KAAK,MAAM,KAAK,IAAI,IAAI,IAAI;AAChC,UAAI,KAAK,MAAM,KAAK,IAAI,IAAI,IAAI;AAChC,WAAK,SAAS,OAAO,IAAI,EAAE;AAC3B,UAAI,OAAO,KAAK,KAAK,KAAK;AAC1B,UAAI,KAAK,MAAM,KAAK,IAAI,IAAI,IAAI;AAChC,UAAI,KAAK,MAAM,KAAK,IAAI,IAAI,IAAI;AAChC,WAAK,SAAS,OAAO,IAAI,EAAE;AAC3B,WAAK,SAAS,OAAO,GAAG,CAAC;AACzB,WAAK,SAAS,UAAU;AACxB,WAAK,SAAS,OAAO;AACrB,WAAK,SAAS,YAAY;AAC1B,WAAK,SAAS,KAAK;AAAA,IACrB,OAAO;AAEL,WAAK,SAAS,UAAU;AACxB,WAAK,SAAS,YAAY;AAC1B,WAAK,SAAS,cAAc;AAC5B,WAAK,SAAS,OAAO,GAAG,CAAC;AACzB,WAAK,SAAS,OAAO,IAAI,EAAE;AAC3B,WAAK,SAAS,UAAU;AACxB,WAAK,SAAS,OAAO;AAAA,IACvB;AACA,QAAI,OAAO;AACT,WAAK,SAAS,KAAK;AACnB,WAAK,SAAS,UAAU,IAAI,EAAE;AAC9B,WAAK,SAAS,QAAQ,QAAQ,OAAO,KAAK,KAAK,IAAI;AACnD,WAAK,SAAS,YAAY;AAC1B,WAAK,SAAS,OAAO,OAAO,OAAO;AACnC,WAAK,SAAS,SAAS,OAAO,GAAG,EAAE;AACnC,WAAK,SAAS,QAAQ;AAAA,IACxB;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,SAAS,UAAU,GAAG,GAAG,KAAK,UAAU,GAAG,KAAK,WAAW,CAAC;AAAA,EACnE;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,mBAAmB;AACjB,WAAO,KAAK,QAAQ,UAAU,KAAK,QAAQ,SAAS,OAAO,KAAK,WAAW,IAAI,KAAK,WAAW;AAAA,EACjG;AAAA,EACA,aAAa;AACX,UAAM,SAAS,KAAK,WAAW;AAC/B,QAAI,MAAM,OAAO,IAAI,KAAK;AAC1B,QAAI,KAAK,UAAU,EAAG,QAAO,KAAK;AAClC,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,QAAI,IAAI,KAAK,UAAU,IAAI,GACzB,IAAI,KAAK,WAAW,IAAI;AAC1B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,WAAW,KAAK,QAAQ,cAAc,WAAW,IAAI;AAC1D,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW;AACT,QAAI,KAAK,qBAAqB;AAC5B,aAAO,qBAAqB,KAAK,mBAAmB;AACpD,WAAK,sBAAsB;AAAA,IAC7B;AACA,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,2BAA2B,OAAO;AAChC,UAAM,SAAS,KAAK,2BAA2B,KAAK;AACpD,WAAO,UAAU,OAAO,QAAQ,OAAO,QAAQ,KAAK;AAAA,EACtD;AAAA,EACA,2BAA2B,OAAO;AAChC,UAAM,QAAQ,OAAO,KAAK,KAAK,UAAU,EAAE,OAAO,SAAU,MAAM;AAChE,aAAO,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK;AAAA,IAC3C,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;AACpD,QAAI,UAAU,QAAW;AACvB,YAAM,SAAS,KAAK,WAAW,KAAK;AACpC,YAAM,IAAI;AAAA,QACR,OAAO,OAAO;AAAA,QACd,iBAAiB,OAAO;AAAA,QACxB,WAAW,OAAO;AAAA,QAClB,OAAO,OAAO,KAAK;AAAA,QACnB,KAAK,KAAK,kBAAkB,OAAO,KAAK,CAAC;AAAA,MAC3C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,QAAQ,OAAO,KAAK,KAAK,UAAU,EAAE,OAAO,SAAU,MAAM;AAChE,aAAO,SAAS,IAAI,KAAK,OAAO,IAAI,IAAI;AAAA,IAC1C,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;AACvC,QAAI,SAAS,MAAM,CAAC,MAAM,QAAW;AACnC,aAAO,OAAO,MAAM,CAAC,CAAC;AAAA,IACxB,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,QAAI,IAAI,GACN,SAAS,CAAC;AACZ,OAAG;AACD,UAAI,SAAS,KAAK,2BAA2B,CAAC;AAC9C,UAAI,QAAQ;AACV,eAAO,KAAK;AAAA,UACV,OAAO,OAAO;AAAA,UACd,KAAK,OAAO;AAAA,UACZ,OAAO,OAAO;AAAA,UACd,iBAAiB,OAAO;AAAA,UACxB,WAAW,OAAO;AAAA,QACpB,CAAC;AACD,YAAI,OAAO;AACX,YAAI,KAAK,KAAK,IAAK;AAAA,MACrB,MAAO;AAAA,IACT,SAAS;AACT,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,GAAG;AAClB,QAAI,OAAO,KAAK,MACd,SAAS,KAAK,WAAW,IAAI,GAC7B,MAAM,KAAK,KACX,MAAM,KAAK,KACX,QAAQ,OAAO,MACf,QAAQ,MAAM,GAAG,KAAK,KAAK,KAAK,GAAG,GACnC,QAAQ,OAAO,OAAO,OAAO,SAAS,MAAM,MAC5C,eAAe,QAAQ,QAAQ;AACjC,WAAO,QAAQ;AAAA,EACjB;AAAA,EACA,QAAQ,IAAI,IAAI;AACd,UAAM,OAAO;AACb,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK,WAAW,IAAI;AACnC,UAAM,WAAW,KAAK;AACtB,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,KAAK;AACjB,UAAM,QAAQ,MAAM,KAAK,OAAO,KAAK,GAAG;AACxC,UAAM,QAAQ,OAAO;AACrB,UAAM,QAAQ,OAAO,OAAO,OAAO,SAAS,MAAM;AAClD,QAAI,eAAe,QAAQ,QAAQ;AACnC,UAAM,OAAO,OAAO;AACpB,UAAM,QAAQ,KAAK,2BAA2B,KAAK;AACnD,QAAI;AACJ,QAAI,KAAK,qBAAqB;AAC5B,aAAO,qBAAqB,KAAK,mBAAmB;AAAA,IACtD;AACA,UAAM,UAAU,eAAa;AAC3B,kBAAY,cAAa,oBAAI,KAAK,GAAE,QAAQ;AAC5C,YAAM,UAAU,YAAY;AAC5B,YAAM,WAAW,KAAK,IAAI,UAAU,UAAU,CAAC;AAC/C,YAAM,mBAAmB,MAAM,KAAK,OAAO,OAAO;AAClD,YAAM,SAAS,QAAQ,mBAAmB,eAAe;AACzD,WAAK,WAAW,OAAO,QAAQ,MAAM,KAAK;AAC1C,UAAI,KAAK,uBAAuB,UAAU,UAAU;AAClD,aAAK,sBAAsB,OAAO,sBAAsB,QAAM,QAAQ,EAAE,CAAC;AAAA,MAC3E,OAAO;AACL,eAAO,qBAAqB,KAAK,mBAAmB;AAAA,MACtD;AAAA,IACF;AACA,QAAI,KAAK,UAAU;AACjB,UAAI,OAAO,UAAa,OAAO,UAAa,OAAO,GAAG;AACpD,uBAAe,OAAO,KAAK,OAAO;AAAA,MACpC;AACA,WAAK,sBAAsB,OAAO,sBAAsB,eAAa;AACnE,oBAAY,cAAa,oBAAI,KAAK,GAAE,QAAQ;AAC5C,gBAAQ,SAAS;AAAA,MACnB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,WAAW,OAAO,QAAQ,cAAc,MAAM,KAAK;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,uBAAuB;AA0BrB,QAAI,KAAK,SAAS;AAChB,YAAM,SAAS,KAAK,WAAW,KAAK,IAAI;AACxC,YAAM,UAAU,OAAO,MAAM,OAAO;AACpC,YAAM,OAAO,WAAW,KAAK,MAAM,KAAK;AACxC,iBAAW,MAAM,KAAK,SAAS;AAC7B,cAAM,IAAI,OAAO,EAAE,IAAI,KAAK;AAC5B,cAAM,QAAQ,OAAO,QAAQ,IAAI;AACjC,cAAM,IAAI,KAAK,QAAQ,EAAE;AACzB,aAAK,WAAW,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,IAAI,IAAI;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ,IAAI,EAAE;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,mBAAmB;AACvD,aAAO,KAAK,qBAAqB,WAAa,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,IACpH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,eAAe,CAAC;AAC5C,UAAG,eAAe,UAAU,iBAAiB,CAAC;AAC9C,UAAG,eAAe,UAAU,gBAAgB,CAAC;AAC7C,UAAG,eAAe,UAAU,eAAe,CAAC;AAAA,QAC9C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,QAC3E;AAAA,MACF;AAAA,MACA,WAAW,SAAS,eAAe,IAAI,KAAK;AAC1C,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAC9D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAC7D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,QACjE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,QAAQ,UAAU,iBAAiB,MAAM;AAAA,MACrD,UAAU;AAAA,MACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,IAAI,GAAG,EAAE,iBAAiB,IAAI,GAAG,EAAE,iBAAiB,IAAI,KAAK,EAAE,cAAc,IAAI,SAAS,EAAE,mBAAmB,IAAI,cAAc;AACjK,UAAG,YAAY,mBAAmB,IAAI;AAAA,QACxC;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,QACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,QACvD,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,SAAS;AAAA,QACT,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,MAClC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,iBAAiB,GAAG,UAAU,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,iBAAiB,GAAG,UAAU,CAAC;AAAA,MAC9L,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC;AAC3C,UAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,cAAc,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,gBAAgB,CAAC;AACpI,UAAG,aAAa;AAChB,UAAG,wBAAwB,GAAG,CAAC;AAC/B,UAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,cAAc,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,gBAAgB,CAAC;AACpI,UAAG,sBAAsB;AACzB,UAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,UAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,cAAc,CAAC,EAAE,IAAI,mCAAmC,GAAG,GAAG,gBAAgB,CAAC;AACtI,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,OAAO,GAAG,CAAC;AACjC,UAAG,WAAW,IAAI,iCAAiC,GAAG,GAAG,cAAc,CAAC,EAAE,IAAI,mCAAmC,GAAG,GAAG,gBAAgB,CAAC;AACxI,UAAG,aAAa;AAChB,UAAG,UAAU,IAAI,UAAU,MAAM,CAAC;AAAA,QACpC;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,cAAc,IAAI,OAAO,IAAI,SAAS,KAAK,OAAO,IAAI;AACrE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAY,IAAI,iBAAiB,IAAI;AACnD,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,IAAI;AAClC,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,KAAK;AACnC,UAAG,UAAU;AACb,UAAG,WAAW,YAAY,IAAI,sBAAsB,IAAI;AACxD,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,IAAI;AAClC,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,KAAK;AACnC,UAAG,UAAU;AACb,UAAG,WAAW,YAAY,IAAI,gBAAgB,IAAI;AAClD,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,IAAI;AAClC,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,KAAK;AACnC,UAAG,UAAU;AACb,UAAG,YAAY,cAAc,IAAI,OAAO,IAAI,SAAS,KAAK,KAAK,IAAI;AACnE,UAAG,WAAW,YAAY,IAAI,eAAe,IAAI;AACjD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,gBAAgB,IAAI;AAClC,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,KAAK;AAAA,QACrC;AAAA,MACF;AAAA,MACA,cAAc,CAAI,UAAa,cAAiB,WAAW;AAAA,MAC3D,QAAQ,CAAC,yjBAAyjB;AAAA,MAClkB,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,MAC5B;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,yjBAAyjB;AAAA,IACpkB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,UAAU,gBAAgB,iBAAiB,eAAe,aAAa;AAAA,MACtF,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,UAAU,gBAAgB,iBAAiB,eAAe,aAAa;AAAA,IACnF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,cAAc,CAAC,UAAU,gBAAgB,iBAAiB,eAAe,aAAa;AAAA,MACtF,SAAS,CAAC,UAAU,gBAAgB,iBAAiB,eAAe,aAAa;AAAA,IACnF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}