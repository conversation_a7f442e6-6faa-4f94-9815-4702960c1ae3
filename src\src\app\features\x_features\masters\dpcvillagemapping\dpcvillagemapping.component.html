<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{name()}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

  <div class="card component">

    <form [formGroup]="form" class="" novalidate>

      <div class="form">
        <div class="field">
          <label for="regionId" class="required-label">{{'Region' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="regionId" formControlName="regionId" (openedChange)="resetSearch('region')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event,'region')">
              </mat-form-field>
              @for (role of filteredRegions(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.regionId.errors?.['required']) {
              <!-- Region is required -->
              {{'Region' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>



        <div class="field">
          <label for="talukId" class="required-label">{{'Taluk' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="talukId" formControlName="talukId" (openedChange)="resetSearch('taluk')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'taluk')">
              </mat-form-field>
              @for (role of filteredTaluks(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.talukId.errors?.['required']) {
              <!-- Taluk is required -->
              {{'Taluk' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="dpcId" class="required-label">{{'DPC' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="dpcId" formControlName="dpcId" (openedChange)="resetSearch('dpc')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'dpc')">
              </mat-form-field>
              @for (role of filteredDPC(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.dpcId.errors?.['required']) {
              <!-- Taluk is required -->
              {{'DPC' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

      </div>
      <div class="container">
        <label for="dpcId" class="required-label">{{'Village(s)' | translate }}</label>

        <div class="wid-25">
          <section class="example-section selectall" style="text-align: center;">
            <mat-checkbox class="btn-block" formControlName="firstListDatas" [(ngModel)]="isSelectAllTransfer"
              labelPosition="before" (change)="selectAllTransfer($event)">Select All Villages
            </mat-checkbox>
          </section>
           <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search3 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'village')">
              </mat-form-field>
          <mat-selection-list #list formControlName="firstSelectedDatas" [(ngModel)]="select" class="border"
            (selectionChange)="onNgModelChange($event)">
            
            <mat-list-option *ngFor="let obj of filteredVillage()" [value]="obj" class="matlistd" style="margin-bottom: 10px">
              {{ obj.value }}
            </mat-list-option>
          </mat-selection-list>
        </div>
        <div style="align-self: center;">
          <!-- <i class="fa fa-arrow-right sty" matTooltip="{{ 'Transfer' | translate }}" aria-hidden="true"
        (click)="moveToTransfer()"></i> -->
          <mat-icon class="sty" matTooltip="{{ 'Transfer' | translate }}" aria-hidden="true"
            (click)="moveToTransfer()">arrow_right_alt</mat-icon>

          <br />
          @if(filteredSelect().length > 0)
          {
          <mat-icon class="sty1" matTooltip="{{ 'Remove' | translate }}" aria-hidden="true"
            (click)="removedToTransfer()">arrow_right_alt</mat-icon>
          }

        </div>
        <div class="wid-25">
          <section class="example-section selectall" style="text-align: center;">
            <mat-checkbox class="btn-block" formControlName="secondListDatas" [(ngModel)]="isSelectAllRemoveTransfer"
              labelPosition="before" (change)="selectAllRemoveTransfer($event)">Select All Villages
            </mat-checkbox>
          </section>
           <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search4 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'select')">
              </mat-form-field>
          <mat-selection-list #list formControlName="secondSelectedDatas" [(ngModel)]="selected" class="border"
            (selectionChange)="onNgModelChange($event)">
           
            <mat-list-option *ngFor="let list of filteredSelect()" [value]="list" class="matlistd"
              style="margin-bottom: 10px">
              {{ list.value }}
            </mat-list-option>
          </mat-selection-list>
        </div>
      </div>

      <div class="actions">
     
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
          translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" (click)="submit()"
          type="submit">{{uniqueId()
          == 0
          ? ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>

    </form>
  </div>
