import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Hulling, HullingResponse } from '../../../models/x_models/masters/hulling';

@Injectable({
  providedIn: 'root'
})
export class HullingService {

  dataService = inject(DataService)

    create(data: Hulling) {
      return this.dataService.post<Response>("/hulling", data)
    }
  
    get() {
      return this.dataService.get<HullingResponse>("/hulling")
    }
  
    getById(id: number) {
      return this.dataService.get<Hulling>(`/hulling/${id}`)
    }
  
    delete(id: number) {
      return this.dataService.delete<Response>(`/hulling/${id}`)
    }

}
