import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { StorageLocations } from '../../../../models/x_models/masters/storage-location';
import { StorageLocationService } from '../../../../services/x_apis/masters/storage-location.service';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { DpcvillagemappingComponent } from './dpcvillagemapping.component';
import { Dpcvillagemapping } from '../../../../models/x_models/masters/dpcvillagemapping';
import { DpcvillagemappingService } from '../../../../services/x_apis/masters/dpcvillagemapping.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-dpcvillagemappings',
   imports: [
        MatIcon,
        MatIcon,
        MatInput,
        MatPrefix,
        ReactiveFormsModule,
        MatPaginatorModule,
        MatTableModule,
        MatButtonModule,
        MatMenuModule,
        DatePipe,
        MatSortModule,
        TranslateModule,
        MatFormFieldModule,
        MatToolbarModule,
        DpcvillagemappingComponent ],
  templateUrl: './dpcvillagemappings.component.html',
  styleUrls: ['./dpcvillagemappings.component.scss']
})

export class DpcvillagemappingsComponent implements OnInit {
    menuService = inject(MenuService);
    access = computed(() => this.menuService.activeMenu()?.controlAccess);
    translate = inject(TranslateService);

    paginator = viewChild<MatPaginator>(MatPaginator);
    sort = viewChild<MatSort>(MatSort);

    dpcVillageMappingService = inject(DpcvillagemappingService);
    dpcVillageMappingResource = resource({ loader: () => this.dpcVillageMappingService.get() });
    dataSource = linkedSignal(() => new MatTableDataSource(this.dpcVillageMappingResource.value()));

    loading = signal(false);
    mode = signal<'view' | 'edit' | 'add'>('add');
    search = new FormControl("");
    showGrid = signal(false);
    status = StatusType;
    id=signal<number>(0)
    name=signal<string>('')

    displayedColumns: string[] = [
        'dpcNameCode',
        'villages',
        'actions'
    ];

    constructor() {
        effect(() => {
            if (this.paginator()) {
                this.dataSource().sort = this.sort()!;
                this.dataSource().paginator = this.paginator()!
            }
        });
    }

    ngOnInit(): void {
    }

    onAdd() {
        this.id.set(0)
        this.mode.set("add")
        this.showGrid.set(true)
    }

    onView(rowData: Dpcvillagemapping) {
        this.id.set(rowData.dpcId)
        this.name.set(rowData.dpcNameCode)
        this.mode.set('view');
        this.showGrid.set(true)
    }

    onEdit(rowData: Dpcvillagemapping) {
        this.id.set(rowData.dpcId);
        this.name.set(rowData.dpcNameCode)
        this.mode.set('edit');
        this.showGrid.set(true)
    }

    onClose() {
        this.showGrid.set(false)
        this.dpcVillageMappingResource.reload()
        this.search.setValue("");
    }

    onRefresh() {
        this.dpcVillageMappingResource.reload()
        this.search.setValue("");
    }

    async onDelete(rowData: Dpcvillagemapping) {
        await confirmAndDelete(rowData, rowData.dpcNameCode, 'Storage Location', this.dpcVillageMappingService, () => this.dpcVillageMappingResource.reload());
    }

    onSearch() {
        const filterValue = this.search.value?.trim().toLowerCase() || '';
        this.dataSource().filter = filterValue;
      }

}