import { Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { PurchaseService } from '../../../../services/x_apis/procurement/purchase.service';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { Purchase } from '../../../../models/x_models/procurement/purchase';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NgTemplateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatOption, MatSelect } from '@angular/material/select';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { ProcurementdetailsComponent } from './procurementdetails.component';

@Component({
  selector: 'app-purchase',
  imports: [ 
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatFormFieldModule,
    TranslateModule,
    ProcurementdetailsComponent
  ],
  templateUrl: './purchase.component.html',
  styleUrl: './purchase.component.scss'
})

export class PurchaseComponent {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  translate = inject(TranslateService);
  purchaseService = inject(PurchaseService);
  purchaseResource = resource({ loader: () => this.purchaseService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.purchaseResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  id=signal<number>(0);


  displayedColumns: string[] = [
    'farmerName',
    'dpcName',
    'tokenNo',
    // 'vendorName',
    'purchaseTxnRefNo',
    'purchaseTs',
    'totalQty',
    'mspAmount',
    'moisturePercentage',
    'incentiveAmount',
    // 'authTxnTypeName',
    'noOfBags',
    'rateCutAmount',
    'actions'
  ];


  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.id.set(0)
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(rowData: Purchase) {
    this.mode.set('view');
    this.showGrid.set(true)
    this.id.set(rowData.id)
  }

  onEdit(rowData: Purchase) {
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(true)
    this.purchaseResource.reload()
    this.search.setValue("");
  }
  close()
  {
    this.showGrid.set(false);
  }

  onRefresh() {
    this.purchaseResource.reload()
    this.search.setValue("");
  }

  async onDelete(rowData: Purchase) {
    await confirmAndDelete(rowData, rowData, 'Purchase', this.purchaseService, () => this.purchaseResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }
}