
import { Injectable } from '@angular/core';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class ExcelService {

  constructor() { }

  public exportAsExcelFile(json: any[], excelFileName: string, cols: any[], title?: string): void {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);
  
    // Update headers from cols array
    const range = XLSX.utils.decode_range(worksheet['!ref'] as string);
    for (let index = range.s.c; index <= range.e.c; ++index) {
      const address = XLSX.utils.encode_col(index) + '1';
      worksheet[address].v = cols[index];
    }
  
    // Insert title row if provided
    if (title) {
      XLSX.utils.sheet_add_aoa(worksheet, [[title]], { origin: 'A1' });
  
      // Merge A1 to the last column (e.g. A1:E1 for 5 columns)
      worksheet['!merges'] = [
        {
          s: { r: 0, c: 0 },
          e: { r: 0, c: cols.length - 1 }
        }
      ];
  
      // Shift all rows down by 1 to accommodate new title row
      for (let row = range.e.r + 1; row >= 1; --row) {
        for (let col = 0; col <= range.e.c; ++col) {
          const from = XLSX.utils.encode_cell({ r: row - 1, c: col });
          const to = XLSX.utils.encode_cell({ r: row, c: col });
          worksheet[to] = worksheet[from];
        }
      }
  
      // Set column headers again in new row 2
      for (let index = 0; index < cols.length; ++index) {
        const address = XLSX.utils.encode_col(index) + '2';
        worksheet[address] = { t: 's', v: cols[index] };
      }
  
      // Update reference
      worksheet['!ref'] = XLSX.utils.encode_range({
        s: { r: 0, c: 0 },
        e: { r: range.e.r + 1, c: range.e.c }
      });
    }
  
    const workbook: XLSX.WorkBook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    this.saveAsExcelFile(excelBuffer, excelFileName);
  }
  
  private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: EXCEL_TYPE
    });
    FileSaver.saveAs(data, fileName + '_export_' + new Date().getTime() + EXCEL_EXTENSION);
  }
}

