.component {
  //background: var(--theme-30);
  //border-radius: 12px;
  //box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  font-size: 14px;
  line-height: 20px;
  max-width: min(410px, 90vw);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
  }

  .btn {
    padding: 0;
    width: 20px;
    height: 20px;
    color: var(--theme-80);
  }
}

.body {
  max-width: calc(100% - 30px);
  .desc {
    margin-bottom: 20px;
  }
  .prompt {
    margin-bottom: 20px;
  }
}

.actions {
  .btn {
    --button-height: 25px;
    min-width: max-content;
    padding: 0;
  }
}


