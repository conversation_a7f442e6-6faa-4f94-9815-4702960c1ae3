import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Approval, ApprovalResponse } from '../../../models/x_models/payments/approval';

@Injectable({
    providedIn: 'root'
})
export class ProcureapprovalService {
    dataService = inject(DataService)

    create(data: Approval) {
        return this.dataService.post<Response>("/purchase", data)
    }

    get() {
        return this.dataService.get<ApprovalResponse>("/purchase")
    }

    getById(id: number) {
        return this.dataService.get<Approval>(`/purchase/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/purchase/${id}`)
    }
}
