import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { MatFormField, MatInput, MatLabel } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { PayrollService } from '../../../services/m_apis/service/payroll.service';

@Component({
  selector: 'app-view',
  imports: [
    CommonModule,
    MatLabel,
    MatFormField,
    MatInput,
    MatCardModule,
    MatGridListModule,
    MatButtonModule,
    MatIconModule,
    ReactiveFormsModule
  ],
  templateUrl: './view.component.html',
  styleUrl: './view.component.scss',
  animations: [
    trigger('slideIn', [
      state('in', style({ opacity: 1, transform: 'translateY(0)' })),
      transition('void => *', [
        style({ opacity: 0, transform: 'translateY(-20px)' }),
        animate(300)
      ]),
      transition('* => void', [
        animate(300, style({ opacity: 0, transform: 'translateY(-20px)' }))
      ])
    ])
  ]
})
export class ViewComponent implements OnInit {
  empId: string | null = null;
  empObj: any;
  showNotification = false;
  isLoading = false;

  constructor(
    private route: ActivatedRoute,
    private service: PayrollService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.empId = this.route.snapshot.paramMap.get('empId');
    if (this.empId) {
      this.getEmployeeId();
    } else {
      console.error('Employee ID is null');
      this.router.navigateByUrl('/main/payroll');
    }
  }

  navigateToPayroll() {
    this.router.navigateByUrl('/main/payroll');
  }

  navigateToAttendance() {
    this.router.navigateByUrl('/main/attendance');
  }

  getEmployeeId() {
    if (this.empId) {
      this.isLoading = true;
      this.service.getEmployeeById(this.empId).subscribe({
        next: (res: any) => {
          console.log('Employee Details:', res.responseData);
          this.empObj = res?.responseData;
          this.isLoading = false;
        },
        error: (error: any) => {
          console.error('Error fetching employee details:', error);
          this.isLoading = false;
          // Handle error appropriately
        }
      });
    }
  }

  submit() {
    if (!this.empObj) {
      console.error('No employee data to submit');
      return;
    }

    this.isLoading = true;
    this.service.submitEmployeeDetails(this.empObj).subscribe({
      next: (res: any) => {
        console.log('Form submitted successfully:', res);
        this.showNotification = true;
        this.isLoading = false;

        // Auto-hide notification and navigate after 3 seconds
        setTimeout(() => {
          this.showNotification = false;
          this.router.navigate(['/m/payroll']);
        }, 3000);
      },
      error: (error: any) => {
        console.error('Error submitting form:', error);
        this.isLoading = false;
        // Handle error appropriately - maybe show error notification
      }
    });
  }

  // Helper method to format currency
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  }

  // Helper method to check if field has value
  hasValue(value: any): boolean {
    return value !== null && value !== undefined && value !== '';
  }
}