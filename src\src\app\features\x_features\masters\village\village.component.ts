import { Component, computed, ElementRef, inject, input, OnInit, output, resource, signal, viewChild } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { VillageService } from '../../../../services/x_apis/masters/village.service';
import { LookUpResponse } from '../../../../models/x_models/lookup';
import { Village } from '../../../../models/x_models/masters/village';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-village',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatFormFieldModule,
    TranslateModule,
    MatToolbarModule,
    MatIconModule],
  templateUrl: './village.component.html',
  styleUrls: ['./village.component.scss']
})
export class VillageComponent implements OnInit {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  villageService = inject(VillageService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService)

  translate = inject(TranslateService);
  taluks = signal<LookUpResponse[]>([]);
  blocks = signal<LookUpResponse[]>([]);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  units = resource({ loader: () => this.lookupService.getUnit() }).value;

  uniqueId = input.required<number>();
  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);

  village = signal<Village | null>(null);
  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')
  search3 = viewChild.required<ElementRef<HTMLInputElement>>('search3')
  search4 = viewChild.required<ElementRef<HTMLInputElement>>('search4')




  form = this.fb.group({
    id: [0],
    villageName: ['', Validators.required],
    villageRegionalName: ['', Validators.required],
    regionId: this.fb.control<number | null>(null, Validators.required),
    unitId: this.fb.control<number | null>(null, Validators.required),
    talukId: this.fb.control<number | null>(null, Validators.required),
    blockId: this.fb.control<number | null>(null, Validators.required),
    villageLgdCode: ['', Validators.required]
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getVillage()
    }
    this.form.get("unitId")?.valueChanges.subscribe(res => {
      this.getTaluk(res);
    })
    this.form.get("talukId")?.valueChanges.subscribe(res => {
      this.getblock(res);
    })
  }

  readonly regionSearch = signal('');
  readonly unitSearch = signal('');
  readonly talukSearch = signal('');
  readonly blockSearch = signal('');

  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.regionSearch().toLowerCase())
    )
  );

  readonly filteredUnites = computed(() =>
    this.units()?.filter(x =>
      x.value.toLowerCase().includes(this.unitSearch().toLowerCase())
    )
  );

  readonly filteredTaluks = computed(() =>
    this.taluks()?.filter(x =>
      x.value.toLowerCase().includes(this.talukSearch().toLowerCase())
    )
  );

  readonly filteredBlocks = computed(() =>
    this.blocks()?.filter(x =>
      x.value.toLowerCase().includes(this.blockSearch().toLowerCase())
    )
  );


  filter(value: any, type: any) {
    // this.regionSearch.set(value.target.value);

    if (type === 'region') {
      this.regionSearch.set(value.target.value);
    } else if (type === 'unit') {
      this.unitSearch.set(value.target.value);
    } else if (type === 'taluk') {
      this.talukSearch.set(value.target.value);
    } else if (type === 'block') {
      this.blockSearch.set(value.target.value);
    }
  }

  resetSearch(type: any) {

    if (type === 'region') {
      this.search1().nativeElement.value = '';
      this.regionSearch.set('');
    } else if (type === 'unit') {
      this.unitSearch.set('');
      this.search2().nativeElement.value = '';


    } else if (type === 'taluk') {
      this.talukSearch.set('');
      this.search3().nativeElement.value = '';

    } else if (type === 'block') {
      this.blockSearch.set('');
      this.search4().nativeElement.value = '';

    }

  }


  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
    this.units.set(await this.lookupService.getUnit());
    this.getTaluk(this.form.value.unitId);
    this.getblock(this.form.value.talukId);
    this.getVillage();
  }

  async getVillage() {
    const res = await this.villageService.getById(this.uniqueId());
    this.village.set(res);
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

  async getTaluk(unitId: any) {
    const res = await this.lookupService.getTaluk(this.form.value.regionId!, unitId)
    this.taluks.set(res)
  }

  async getblock(talukId: any) {
    const res = await this.lookupService.getBlock(talukId)
    this.blocks.set(res)
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.villageService.create(formValue as Village);
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Village "${formValue.villageName}" Saved Successfully.`)
        }
        else {
          this.alertService.success(`Village "${formValue.villageName}" Updated Successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }


  goBack() {
    this.closed.emit(true)
  }

}
