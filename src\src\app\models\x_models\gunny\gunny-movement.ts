export interface GunnyMovement {
    id: number,
    gunnyMovementId: number,
    qrCode: string,
    fromStorageLocationId: number,
    toStorageLocationId: number
}

export interface GunnyMovements {
    id: number
    fromStorageLocationName: string
    toStorageLocationName: string
    truckMemoId: number
    noOfBags: number
     acknowledgementStatusType: number
    acknowledgementStatusTypeName: string
    // entryTs: string
    // userInfoId: number
}

export type GunnyMovementResponse = GunnyMovements[]