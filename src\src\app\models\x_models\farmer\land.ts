export interface Land {
    id: number,
    landApprovalId: number,
    farmerId: number,
    areaOfLand: number,
    surveyNo: number,
    subDivsion: string,
    landLordName: string,
    landUsage: number,
    expectedYield: number,
    villageName: string,
    blockName: string,
    talukName: string,
    regionName: string,
    landTypeName: string,
    vaoApprovalStatusType: number,
    vaoApprovalStatusTypeName: string,
    landAttachmentGetViewModel: LandAttachmentViewModel[]
}

export interface LandAttachmentViewModel {
    id: number,
    attachmentFilename: string,
    attachmentType: number,
    fileType: number,
    base64String: string,
    attachmentPath:string
   
}

export type LandDetails=Land[]

