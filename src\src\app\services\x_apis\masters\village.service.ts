import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Village, VillageResponse, Villages } from '../../../models/x_models/masters/village';
import { Response } from '../../../models/x_models/api-response';


@Injectable({
  providedIn: 'root'
})
export class VillageService {

  dataService = inject(DataService)

  create(data: Village) {
    return this.dataService.post<Response>("/village", data)
  }

  get() {
    return this.dataService.get<VillageResponse>("/village")
  }

  getById(id: number) {
    return this.dataService.get<Village>(`/village/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/village/${id}`)
  }

}
