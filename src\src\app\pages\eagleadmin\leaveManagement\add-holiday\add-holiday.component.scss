.add-holiday-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .header-card {
    background: linear-gradient(135deg, #203664 0%, darken(#203664, 10%) 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    transition: transform 0.3s ease;
  
    &:hover {
      transform: translateY(-4px);
    }
  
    .form-header {
      padding: 16px;
      display: flex;
      align-items: center;
  
      .header-content {
        display: flex;
        align-items: center;
        width: 100%;
      }
  
      .logo-section {
        margin-right: 16px;
  
        .tncsc-logo {
          font-size: 40px;
          width: 40px;
          height: 40px;
          color: white;
          background-color: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
  
      .title-section {
        .form-subtitle {
          font-size: 24px;
          font-weight: 500;
          margin: 0;
        }
  
        .subtitle {
          font-size: 14px;
          opacity: 0.8;
          margin: 4px 0 0;
        }
      }
    }
  }

  .form-card {
    max-width: 100%;
    margin: 0 auto;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #11074e;

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }

    .form-section {
      margin-bottom: 16px;

      .form-field {
        width: 100%;
      }

      &.submit-section {
        display: flex;
        justify-content: flex-end;
        gap: 16px;
        grid-column: 1 / -1; /* Full width for submit section */
      }

      button {
        &.mat-raised-button {
          background-color: #11074e;
          color: white;
          padding: 8px 16px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          gap: 8px;

          &:hover {
            background-color: #1a0c7a;
          }

          mat-icon {
            font-size: 1rem;
            width: 1rem;
            height: 1rem;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .header-card {
      .page-title {
        font-size: 2rem;

        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }
      }
    }

    .form-card {
      padding: 16px;

      .form-grid {
        grid-template-columns: 1fr; /* Single column on mobile */
        gap: 12px;
      }

      .form-section {
        margin-bottom: 12px;

        &.submit-section {
          flex-direction: column;
          align-items: flex-end;
          gap: 8px;
        }
      }
    }
  }
}