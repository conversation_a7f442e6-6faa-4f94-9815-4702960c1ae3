// Variables
$primary-color: #203664;
$accent-color: #203664;
$success-color: #4caf50;
$error-color: #f44336;
$warning-color: #ff9800;
$info-color: #2196f3;
$light-gray: #f5f5f5;
$border-radius: 8px;
$box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

// Container Styles
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  min-height: calc(100vh - 100px);
}

// Header Section
.header-section {
  text-align: center;
  margin-bottom: 32px;

  .page-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;

    .title-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      color: $primary-color;
    }

    h1 {
      margin: 0;
      color: $primary-color;
      font-size: 28px;
      font-weight: 500;
    }
  }

  .page-subtitle {
    color: lighten($primary-color, 20%);
    font-size: 16px;
    margin: 0;
  }
}

// Quick Actions Card
.quick-actions-card {
  margin-bottom: 24px;
  border-radius: $border-radius;
  box-shadow: $box-shadow;

  mat-card-header {
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: $primary-color;

      mat-icon {
        font-size: 20px;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      flex-direction: column;
    }

    .action-btn {
      flex: 1;
      min-width: 200px;
      height: 48px;
      border-radius: $border-radius;
      font-weight: 500;

      @media (max-width: 768px) {
        min-width: 100%;
      }

      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

// Statistics Section
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;

  .stat-card {
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
          color: white;
        }
      }

      .stat-info {
        h3 {
          margin: 0 0 4px 0;
          font-size: 24px;
          font-weight: 600;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: lighten($primary-color, 20%);
        }
      }
    }

    &.pending .stat-icon {
      background-color: $warning-color;
    }

    &.approved .stat-icon {
      background-color: $info-color;
    }

    &.rejected .stat-icon {
      background-color: $error-color;
    }

    &.completed .stat-icon {
      background-color: $success-color;
    }
  }
}

// Filters Card
.filters-card {
  margin-bottom: 24px;
  border-radius: $border-radius;
  box-shadow: $box-shadow;

  mat-card-header {
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: $primary-color;

      mat-icon {
        font-size: 20px;
      }
    }
  }

  .filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .filter-field {
      width: 100%;
    }
  }
}

// Requests Card
.requests-card {
  border-radius: $border-radius;
  box-shadow: $box-shadow;

  mat-card-header {
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: $primary-color;

      mat-icon {
        font-size: 20px;
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;

    p {
      margin-top: 16px;
      color: lighten($primary-color, 20%);
    }
  }

  .no-requests {
    text-align: center;
    padding: 40px;

    mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: lighten($primary-color, 40%);
      margin-bottom: 16px;
    }

    h3 {
      color: $primary-color;
      margin-bottom: 8px;
    }

    p {
      color: lighten($primary-color, 20%);
      margin-bottom: 24px;
    }
  }
}

// Requests List
.requests-list {
  .request-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .request-card {
    border-radius: $border-radius;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-left: 4px solid transparent;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    &.pending {
      border-left-color: $warning-color;
    }

    &.approved {
      border-left-color: $info-color;
    }

    &.completed {
      border-left-color: $success-color;
    }

    &.rejected {
      border-left-color: $error-color;
    }

    &.cancelled {
      border-left-color: lighten($primary-color, 40%);
    }

    mat-card-header {
      .request-avatar {
        background-color: $primary-color;
        color: white;

        mat-icon {
          font-size: 20px;
        }
      }

      .request-actions {
        margin-left: auto;
      }
    }

    .request-details {
      margin-bottom: 16px;

      .detail-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .label {
          font-weight: 500;
          color: $primary-color;
          min-width: 120px;
          margin-right: 8px;
        }

        .status-badge {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;

          &.pending {
            background-color: lighten($warning-color, 40%);
            color: darken($warning-color, 20%);
          }

          &.approved {
            background-color: lighten($info-color, 40%);
            color: $info-color;
          }

          &.completed {
            background-color: lighten($success-color, 40%);
            color: $success-color;
          }

          &.rejected {
            background-color: lighten($error-color, 40%);
            color: $error-color;
          }

          &.cancelled {
            background-color: lighten($primary-color, 50%);
            color: $primary-color;
          }
        }

        .priority-badge {
          padding: 2px 6px;
          border-radius: 8px;
          font-size: 11px;
          font-weight: 500;
          text-transform: uppercase;

          &.low {
            background-color: lighten($info-color, 40%);
            color: $info-color;
          }

          &.medium {
            background-color: lighten($warning-color, 40%);
            color: darken($warning-color, 20%);
          }

          &.high {
            background-color: lighten($error-color, 40%);
            color: $error-color;
          }

          &.urgent {
            background-color: $error-color;
            color: white;
          }
        }
      }
    }

    .progress-section {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid lighten($primary-color, 60%);

      h4 {
        margin: 0 0 12px 0;
        color: $primary-color;
        font-size: 14px;
        font-weight: 500;
      }

      .progress-steps {
        display: flex;
        gap: 12px;
        overflow-x: auto;

        .progress-step {
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 120px;
          padding: 8px;
          border-radius: $border-radius;

          &.approved {
            background-color: lighten($success-color, 45%);

            .step-icon {
              background-color: $success-color;
              color: white;
            }
          }

          &.rejected {
            background-color: lighten($error-color, 45%);

            .step-icon {
              background-color: $error-color;
              color: white;
            }
          }

          &.pending {
            background-color: lighten($warning-color, 45%);

            .step-icon {
              background-color: $warning-color;
              color: white;
            }
          }

          .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          .step-content {
            .step-title {
              font-size: 12px;
              font-weight: 500;
              color: $primary-color;
            }

            .step-status {
              font-size: 10px;
              text-transform: uppercase;
              color: lighten($primary-color, 20%);
            }

            .step-date {
              font-size: 10px;
              color: lighten($primary-color, 30%);
            }
          }
        }
      }
    }
  }
}

// Form Field Styles
mat-form-field {
  &.mat-form-field-appearance-outline {
    .mat-form-field-outline {
      color: lighten($primary-color, 40%);
    }

    &.mat-focused .mat-form-field-outline-thick {
      color: $primary-color;
    }

    .mat-form-field-label {
      color: $primary-color;
    }

    &.mat-focused .mat-form-field-label {
      color: $primary-color;
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .header-section .page-title {
    flex-direction: column;
    gap: 8px;

    h1 {
      font-size: 24px;
    }
  }

  .stats-section {
    grid-template-columns: 1fr;
  }

  .progress-steps {
    flex-direction: column;

    .progress-step {
      min-width: auto;
    }
  }
}

// Animation
.container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}