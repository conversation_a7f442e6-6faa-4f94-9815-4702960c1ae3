<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{storagelocation()?.storageLocationName}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

  <div class="card component">
   
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="regionId" class="required-label">{{'Region' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="regionId" formControlName="regionId" (openedChange)="resetSearch('region')">
             <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'region')">
              </mat-form-field>
              @for (region of filteredRegions(); track region) {
              <mat-option [value]="region.key">{{region.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.regionId.errors?.['required']) {
              <!-- Region is required -->
              {{'ToDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="storageLocationName" class="required-label">{{'StorageLocation' | translate}}</label>
          <mat-form-field>
            <input id="storageLocationName" formControlName="storageLocationName" matInput
              (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
            <mat-error>
              @if(form.controls.storageLocationName.errors?.['required']) {
              <!-- Storage location  is required -->
              {{'StorageLocation' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="storageLocationRegionalName" class="required-label">{{'StorageLocation' | translate}} {{'InTamil' | translate}}</label>
          <mat-form-field>
            <input id="storageLocationRegionalName" formControlName="storageLocationRegionalName" matInput
              (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
            <mat-error>
              @if(form.controls.storageLocationRegionalName.errors?.['required']) {
              <!-- Storage location in tamil is required -->
              {{'StorageLocation' | translate}} {{'InTamil' | translate}} {{'IsRequired' | translate}}
              }

            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="storageType" class="required-label">{{'StorageType' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="storageType" formControlName="storageType" (openedChange)="resetSearch('storage')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'storage')">
              </mat-form-field>
              @for (role of filteredStorageTypes(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.storageType.errors?.['required']) {
              <!-- Storage type is required -->
              {{'StorageType' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="capacity" class="required-label">{{'Capacity' | translate}}</label>
          <mat-form-field>
            <input id="capacity" formControlName="capacity" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.capacity.errors?.['required']) {
              <!-- Capacity is required -->
              {{'Capacity' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="carpetArea" class="required-label">{{'CarpetArea' | translate}}</label>
          <mat-form-field>
            <input id="carpetArea" formControlName="carpetArea" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.carpetArea.errors?.['required']) {
              <!-- Carpet Area is required -->
              {{'CarpetArea' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="latitude" class="required-label"> {{'Latitude' | translate}}</label>
          <mat-form-field>
            <input id="latitude" formControlName="latitude" matInput (input)="onInput($event,'/^[0-9.]*$/')">
            <mat-error>
              @if(form.controls.latitude.errors?.['required']) {
              <!-- Latitude is required -->
              {{'Latitude' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="longitude" class="required-label"> {{'Longitude' | translate}}</label>
          <mat-form-field>
            <input id="longitude" formControlName="longitude" matInput (input)="onInput($event,'/^[0-9.]*$/')">
            <mat-error>
              @if(form.controls.longitude.errors?.['required']) {
              <!-- Longitude is required -->
              {{'Longitude' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


      </div>

      <div class="actions">
       
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' | translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0
          ? ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>


    </form>
  </div>
