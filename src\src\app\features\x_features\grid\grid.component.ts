import { Component, computed, inject, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatCheckbox, MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { NgTemplateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatOption, MatSelect } from '@angular/material/select';
import { RouterLink } from '@angular/router';
import {MatSortModule} from '@angular/material/sort';
import {MatTooltip} from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import {CloseFilterOnBlurDirective} from '../../../directives/close-filter-on-blur.directive';
import {SideBarService} from '../../../services/side-bar.service';
import { AppRoutes } from '../../../enums/app-routes';
import {handleSelect, handleSelectAll} from '../../../helpers/helpers';


@Component({
  selector: 'app-grid',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatOption,
    MatSelect,
    RouterLink,
    CloseFilterOnBlurDirective, MatTooltip, CurrencyPipe, MatFormFieldModule],
  templateUrl: './grid.component.html',
  styleUrl: './grid.component.scss'
})
export class GridComponent {
  search = new FormControl("")
  showFilters = signal(false)
  sidebarService = inject(SideBarService)

  dataSource = signal(new MatTableDataSource([{
    quoteNo: "QU-06425",
    referenceNo: "CP930184",
    date: "01-09-2024",
    cost: 108002.91,
    status: "Quote",
    selected: false
  },
  {
    quoteNo: "QU-06425",
    referenceNo: "CP930185",
    date: "01-09-2024",
    cost: 108002.91,
    status: "Invoiced",
    selected: false
  }]))
  displayedColumns: string[] = ["selectAll", "quoteNo", "referenceNo", "date", "cost", "status"];

  pageLength = signal(10)
  pageIndex = signal(0)
  selectedItem = signal<any | null>(null)
  selectedItems = signal<any[]>([])
  partiallySelected = computed(() => !!(this.selectedItems().length && this.selectedItems().length !== this.dataSource().data.length))
  allSelected = computed(() => this.selectedItems().length === this.dataSource().data.length)
  loading = signal(false)

  protected readonly AppRoutes = AppRoutes;


  onSearch() {

  }

  onPageChange(event: PageEvent) {
    this.pageIndex.set(event.pageIndex)
    // this.getUnassignedCards(this.accountService.account()?.id!, event.pageSize, event.pageIndex+1)
  }

  onSelectAllChange(event: MatCheckboxChange) {
    this.selectedItems.set(handleSelectAll(event, this.dataSource))
  }

  onSelectItemChange(event: MatCheckboxChange, selected: any) {
    this.selectedItems.set(handleSelect(event, selected, this.dataSource))
  }
}
