import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { Crops } from '../../../../models/x_models/masters/crop';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { CropService } from '../../../../services/x_apis/masters/crop.service';
import { CropComponent } from './crop.component';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-crops',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatFormFieldModule,
    TranslateModule,
    MatToolbarModule,
    CropComponent],
    providers: [DatePipe],
  templateUrl: './crops.component.html',
  styleUrls: ['./crops.component.scss']
})
export class CropsComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  cropService = inject(CropService)
  cropResource = resource({ loader: () => this.cropService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.cropResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  crop = signal<Crops>({
    id: 0,
    cropName: '',
    fromDate: '',
    toDate: '',
    seasonName: '',
  });

  displayedColumns: string[] = [
    'cropName',
    'seasonName',
    'fromDate',
    'toDate',
    'actions',
  ];
  crops = signal<Crops[]>([]);
  protected readonly AppRoutes = AppRoutes;  // Need clarification

  constructor(private datePipe: DatePipe) {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
      this.dataSource().filterPredicate = (data: Crops, filter: string) => {
              const formattedFromDate = this.datePipe.transform(data.fromDate, 'dd-MM-yyyy') ?? '';
              const formattedToDate = this.datePipe.transform(data.toDate, 'dd-MM-yyyy') ?? '';
              const combinedData = `${data.seasonName} ${formattedFromDate} ${formattedToDate}`.toLowerCase();
              return combinedData.includes(filter);
            };
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.crop.set({
      id: 0,
      cropName: '',
      fromDate: '',
      toDate: '',
      seasonName: '',
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(crops: Crops) {
    this.crop.set(crops);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(crops: Crops) {
    this.crop.set(crops);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.cropResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.cropResource.reload();
    this.search.setValue("");
  }

  async onDelete(crop: Crops) {
    await confirmAndDelete(crop, crop.cropName, 'Crop', this.cropService, () => this.cropResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
