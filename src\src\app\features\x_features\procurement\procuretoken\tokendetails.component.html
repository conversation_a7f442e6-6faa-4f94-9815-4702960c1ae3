@if(showGrid() === 2) {
<div class="card">
    <div class="component1">
        <div class="page-header1">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a (click)="navigateBack()">{{menuService.activeMenu()?.title}}</a>
                    </li>
                    @if(tokenId() == 0) {
                    <li aria-current="page" class="breadcrumb-item active">{{'New' | translate}}
                        {{menuService.activeMenu()?.title}}</li>
                    } @else {
                    <li aria-current="page" class="breadcrumb-item active">
                        {{tokenData()?.tokenNo}}
                    </li>
                    }
                </ol>
            </nav>
            @if(tokenId() == 0) {
            <h1>{{'New' | translate}} {{menuService.activeMenu()?.title}}</h1>
            } @else {
            <h1>
                {{tokenData()?.tokenNo}}
            </h1>
            }
        </div>

        <div class="grid-container">
            <div class="row-12">
                <div class="actions">
                    <!-- <button mat-flat-button color="accent" (click)="navigateBack()"
                        style="text-align:center;padding:10px;border-radius:5px;">
                        Back
                    </button> -->
                </div>
            </div>
        </div>

        <div class="grid-container">
            <div class="row-3">
                <label class="label">{{'DPC' | translate}} </label>
                <label class="label">{{'FarmerName' | translate}}</label>
                <label class="label">{{'TokenNo' | translate}} </label>
                <label class="label">{{'RequestedPerson' | translate}} </label>
            </div>
            <div class="row-3">
                <label class="value">{{tokenData()?.dpcName}}</label>
                <label class="value">{{tokenData()?.farmerName}} | {{tokenData()?.farmerId}}</label>
                <label class="value">{{tokenData()?.tokenNo}} | {{tokenData()?.tokenDate | date : 'dd-MM-yyyy'}}
                </label>
                <label class="value">{{tokenData()?.requestedTypeName}} </label>
            </div>

            <div class="row-3">
                <label class="label">{{'ProductName' | translate}} </label>
                <label class="label">{{'VarietyName' | translate}}</label>
                <label class="label">{{'SeasonName' | translate}} </label>
                <label class="label">{{'NoofBags' | translate}} </label>
                <!-- <label class="label">{{'noofProcuredBags' | translate}} </label> -->
            </div>
            <div class="row-3">
                <label class="value">{{tokenData()?.productName}}</label>
                <label class="value">{{tokenData()?.varietyName}}</label>
                <label class="value">{{tokenData()?.seasonName}}</label>
                <label class="value">{{tokenData()?.noofBags}} </label>
                <!-- <label class="value">{{tokenData()?.noofProcuredBags}} </label> -->
            </div>
        </div>
    </div>

    <div class="row-12">
        <label class="land">Land Details</label>
    </div>
    <div class="card1">
        <div class="grid-container">

            @for (item of tokenData()?.landViewModel; track item.id; let i = $index) {
            <div class="row-12" style="color:rgb(12, 12, 175)">
                Land {{ i + 1 }}
            </div>
            <div class="row-3">
                <label class="label">{{'Region' | translate}} </label>
                <label class="label">{{'Unit' | translate}}</label>
                <label class="label">{{'Taluk' | translate}} </label>
                <label class="label">{{'Village' | translate}} </label>
            </div>
            <div class="row-3">
                <label class="value">{{item.regionName}}</label>
                <label class="value">{{item.unitName}}</label>
                <label class="value">{{item.talukName}}
                </label>
                <label class="value">{{item.villageName}} </label>
            </div>

            <div class="row-3">
                <label class="label">{{'SurveyNo' | translate}} </label>
                <label class="label">{{'LandType' | translate}}</label>
                <label class="label">{{'SubDivsion' | translate}} </label>
                <label class="label">{{'ExpectedYield' | translate}} </label>
            </div>
            <div class="row-3">
                <label class="value">{{item.surveyNo}}</label>
                <label class="value">{{item.landTypeName}}</label>
                <label class="value">{{item.subDivsion}}
                </label>
                <label class="value">{{item.expectedYield}} </label>
            </div>



            }
        </div>
    </div>

</div>
}