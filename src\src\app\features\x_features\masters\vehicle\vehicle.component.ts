import { Component, inject, input, output, resource, signal } from '@angular/core';
import { Form<PERSON>uilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { VehicleService } from '../../../../services/x_apis/masters/vehicle.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { Vehicle } from '../../../../models/x_models/masters/vehicle';
import { Router } from '@angular/router';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-vehicle',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatNativeDateModule,
    TranslateModule,
    MatToolbarModule,
    MatFormFieldModule],
  templateUrl: './vehicle.component.html',
  styleUrl: './vehicle.component.scss'
})
export class VehicleComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  vehicleService = inject(VehicleService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  vendor = resource({ loader: () => this.lookupService.getVendor() }).value;

  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  router = inject(Router);
  uinqueId = input.required<any>();
  vehicle = signal<Vehicle | null>(null);

  form = this.fb.group({
    id: this.fb.control<number>(0),
    regNo: this.fb.control<string>('', Validators.required),
    ownerName: this.fb.control<string>('', Validators.required),
    ownerMobileNo: this.fb.control<string>('', [Validators.required, Validators.pattern("^[6-9][0-9]{9}$")]),
    vendorId: this.fb.control<number | null>(null, Validators.required),
    contractorId :this.fb.control<number | null>(null, Validators.required),
  });

  ngOnInit() {
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

  async getFormData() {
    const res = await this.vehicleService.getById(this.uinqueId());
    this.vehicle.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.vehicleService.create(formValue as Vehicle)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Vehicle ${formValue.regNo} Created successfully.`)
        }
        else {
          this.alertService.success(`Vehicle ${formValue.regNo} Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

}
