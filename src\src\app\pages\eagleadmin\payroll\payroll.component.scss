.main-content {

  padding: 20px;
  min-height: calc(100vh - 60px); // Adjust based on your header height
  background-color: #f5f5f5;
}

.container-fluid {
  max-width: 100%;
  padding: 0;
}

.table-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  mat-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    
    mat-card-title {
      margin: 0;
      
      h2 {
        margin: 0;
        color: #333;
        font-size: 24px;
        font-weight: 500;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
      
      button {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
  
  mat-card-content {
    padding: 0;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  mat-progress-spinner {
    margin-bottom: 20px;
  }
  
  p {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
}

.table-container {
  overflow-x: auto;
  
  .full-width-table {
    width: 100%;
    min-width: 1200px;
    
    .mat-mdc-header-cell {
      background-color: #203664;
      color: white;
      font-weight: 600;
      font-size: 14px;
      padding: 16px 12px;
      border-bottom: 2px solid #e0e0e0;
      white-space: nowrap;
      text-align: center;
    }
    
    .mat-mdc-cell {
      padding: 16px 12px;
      font-size: 14px;
      color: #555;
      border-bottom: 1px solid #f0f0f0;
      white-space: nowrap;
      text-align: center;
      
      &.pan-number,
      &.pf-number {
        font-family: 'Courier New', monospace;
        font-weight: 500;
        color: #1976d2;
      }
      
      .no-data {
        color: #999;
        font-style: italic;
      }
    }
    
    .table-row {
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: #f8f9fa;
      }
      
      &.highlight-row {
        background-color: #e3f2fd;
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  
  .view-button {
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 80px;
    font-size: 12px;
  }
  
  button {
    &[mat-icon-button] {
      min-width: 40px;
      width: 40px;
      height: 40px;
      
      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

// Chip Styling
.grade-chip {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  font-weight: 500;
  font-size: 12px;
}

.special-pay-chip {
  background-color: #fff3e0 !important;
  color: #f57c00 !important;
  font-weight: 500;
  font-size: 12px;
}

.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  
  .no-data-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #ccc;
    margin-bottom: 20px;
  }
  
  h3 {
    color: #666;
    font-size: 24px;
    margin: 0 0 10px 0;
    font-weight: 500;
  }
  
  p {
    color: #888;
    font-size: 16px;
    margin: 0 0 30px 0;
  }
  
  button {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

// Status and Badge Styling
mat-chip-set {
  mat-chip {
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    padding: 4px 8px;
  }
}

// Responsive styles
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 10px;
  }
  
  .table-card {
    mat-card-header {
      flex-direction: column;
      align-items: stretch;
      gap: 15px;
      
      .header-actions {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }
  
  .table-container {
    .full-width-table {
      min-width: 1000px;
      
      .mat-mdc-header-cell,
      .mat-mdc-cell {
        padding: 12px 8px;
        font-size: 12px;
      }
    }
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
    
    .view-button {
      min-width: 70px;
      font-size: 11px;
      padding: 6px 12px;
    }
    
    button[mat-icon-button] {
      width: 36px;
      height: 36px;
      min-width: 36px;
      
      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }
}

// Tablet styles
@media (max-width: 1024px) and (min-width: 769px) {
  .table-container {
    .full-width-table {
      .mat-mdc-header-cell,
      .mat-mdc-cell {
        padding: 14px 10px;
        font-size: 13px;
      }
    }
  }
  
  .action-buttons {
    .view-button {
      min-width: 75px;
      font-size: 12px;
    }
  }
}

// Print styles
@media print {
  .main-content {
    margin-left: 0;
    padding: 0;
  }
  
  .header-actions,
  .action-buttons {
    display: none;
  }
  
  .table-card {
    box-shadow: none;
    border: 1px solid #000;
    
    mat-card-header {
      border-bottom: 2px solid #000;
    }
  }
  
  .table-container {
    .full-width-table {
      .mat-mdc-header-cell,
      .mat-mdc-cell {
        border: 1px solid #000;
        padding: 8px;
      }
    }
  }
}