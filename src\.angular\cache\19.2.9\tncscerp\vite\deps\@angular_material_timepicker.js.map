{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/timepicker.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ViewContainerRef, Injector, ANIMATION_MODULE_TYPE, signal, viewChild, viewChildren, input, output, booleanAttribute, computed, effect, ElementRef, afterNextRender, untracked, ViewEncapsulation, ChangeDetectionStrategy, Component, model, Renderer2, Directive, HostAttributeToken, NgModule } from '@angular/core';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { Overlay } from '@angular/cdk/overlay';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { _getEventTarget, _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { TAB, ESCAPE, hasModifierKey, ENTER, DOWN_ARROW, UP_ARROW } from '@angular/cdk/keycodes';\nimport { ActiveDescendantKeyManager, _IdGenerator } from '@angular/cdk/a11y';\nimport { Date<PERSON>dapter, MAT_DATE_FORMATS } from './date-formats-BQbO9F6H.mjs';\nimport { MatOption, MAT_OPTION_PARENT_COMPONENT } from './option-B6mQ8PwE.mjs';\nimport { Validators, NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nimport { MAT_FORM_FIELD } from './form-field-BPX7ZLIc.mjs';\nimport { MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-cp3A3zMa.mjs';\nimport { MatIconButton } from './icon-button-BUs2uBU3.mjs';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport 'rxjs';\nimport './ripple-BPguEKwi.mjs';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-Dy35mUmj.mjs';\nimport './structural-styles-B2ekkpE5.mjs';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './ripple-loader-BiPcTQRh.mjs';\n\n/** Pattern that interval strings have to match. */\nconst _c0 = [\"panelTemplate\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction MatTimepicker_ng_template_0_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-option\", 3);\n    i0.ɵɵlistener(\"onSelectionChange\", function MatTimepicker_ng_template_0_For_2_Template_mat_option_onSelectionChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1._selectValue($event.source));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r4.label);\n  }\n}\nfunction MatTimepicker_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"animationend\", function MatTimepicker_ng_template_0_Template_div_animationend_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleAnimationEnd($event));\n    });\n    i0.ɵɵrepeaterCreate(1, MatTimepicker_ng_template_0_For_2_Template, 2, 2, \"mat-option\", 2, _forTrack0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-timepicker-panel-animations-enabled\", !ctx_r1._animationsDisabled)(\"mat-timepicker-panel-exit\", !ctx_r1.isOpen());\n    i0.ɵɵproperty(\"id\", ctx_r1.panelId);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel() || null)(\"aria-labelledby\", ctx_r1._getAriaLabelledby());\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r1._timeOptions);\n  }\n}\nconst _c1 = [[[\"\", \"matTimepickerToggleIcon\", \"\"]]];\nconst _c2 = [\"[matTimepickerToggleIcon]\"];\nfunction MatTimepickerToggle_ProjectionFallback_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 1);\n    i0.ɵɵelement(1, \"path\", 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst INTERVAL_PATTERN = /^(\\d*\\.?\\d+)\\s*(h|hour|hours|m|min|minute|minutes|s|second|seconds)?$/i;\n/**\n * Injection token that can be used to configure the default options for the timepicker component.\n */\nconst MAT_TIMEPICKER_CONFIG = new InjectionToken('MAT_TIMEPICKER_CONFIG');\n/** Parses an interval value into seconds. */\nfunction parseInterval(value) {\n  let result;\n  if (value === null) {\n    return null;\n  } else if (typeof value === 'number') {\n    result = value;\n  } else {\n    if (value.trim().length === 0) {\n      return null;\n    }\n    const parsed = value.match(INTERVAL_PATTERN);\n    const amount = parsed ? parseFloat(parsed[1]) : null;\n    const unit = parsed?.[2]?.toLowerCase() || null;\n    if (!parsed || amount === null || isNaN(amount)) {\n      return null;\n    }\n    if (unit === 'h' || unit === 'hour' || unit === 'hours') {\n      result = amount * 3600;\n    } else if (unit === 'm' || unit === 'min' || unit === 'minute' || unit === 'minutes') {\n      result = amount * 60;\n    } else {\n      result = amount;\n    }\n  }\n  return result;\n}\n/**\n * Generates the options to show in a timepicker.\n * @param adapter Date adapter to be used to generate the options.\n * @param formats Formatting config to use when displaying the options.\n * @param min Time from which to start generating the options.\n * @param max Time at which to stop generating the options.\n * @param interval Amount of seconds between each option.\n */\nfunction generateOptions(adapter, formats, min, max, interval) {\n  const options = [];\n  let current = adapter.compareTime(min, max) < 1 ? min : max;\n  while (adapter.sameDate(current, min) && adapter.compareTime(current, max) < 1 && adapter.isValid(current)) {\n    options.push({\n      value: current,\n      label: adapter.format(current, formats.display.timeOptionLabel)\n    });\n    current = adapter.addSeconds(current, interval);\n  }\n  return options;\n}\n/** Checks whether a date adapter is set up correctly for use with the timepicker. */\nfunction validateAdapter(adapter, formats) {\n  function missingAdapterError(provider) {\n    return Error(`MatTimepicker: No provider found for ${provider}. You must add one of the following ` + `to your app config: provideNativeDateAdapter, provideDateFnsAdapter, ` + `provideLuxonDateAdapter, provideMomentDateAdapter, or provide a custom implementation.`);\n  }\n  if (!adapter) {\n    throw missingAdapterError('DateAdapter');\n  }\n  if (!formats) {\n    throw missingAdapterError('MAT_DATE_FORMATS');\n  }\n  if (formats.display.timeInput === undefined || formats.display.timeOptionLabel === undefined || formats.parse.timeInput === undefined) {\n    throw new Error('MatTimepicker: Incomplete `MAT_DATE_FORMATS` has been provided. ' + '`MAT_DATE_FORMATS` must provide `display.timeInput`, `display.timeOptionLabel` ' + 'and `parse.timeInput` formats in order to be compatible with MatTimepicker.');\n  }\n}\n\n/** Injection token used to configure the behavior of the timepicker dropdown while scrolling. */\nconst MAT_TIMEPICKER_SCROLL_STRATEGY = new InjectionToken('MAT_TIMEPICKER_SCROLL_STRATEGY', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * Renders out a listbox that can be used to select a time of day.\n * Intended to be used together with `MatTimepickerInput`.\n */\nclass MatTimepicker {\n  _overlay = inject(Overlay);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _viewContainerRef = inject(ViewContainerRef);\n  _injector = inject(Injector);\n  _defaultConfig = inject(MAT_TIMEPICKER_CONFIG, {\n    optional: true\n  });\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _dateFormats = inject(MAT_DATE_FORMATS, {\n    optional: true\n  });\n  _scrollStrategyFactory = inject(MAT_TIMEPICKER_SCROLL_STRATEGY);\n  _animationsDisabled = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  }) === 'NoopAnimations';\n  _isOpen = signal(false);\n  _activeDescendant = signal(null);\n  _input = signal(null);\n  _overlayRef = null;\n  _portal = null;\n  _optionsCacheKey = null;\n  _localeChanges;\n  _onOpenRender = null;\n  _panelTemplate = viewChild.required('panelTemplate');\n  _timeOptions = [];\n  _options = viewChildren(MatOption);\n  _keyManager = new ActiveDescendantKeyManager(this._options, this._injector).withHomeAndEnd(true).withPageUpDown(true).withVerticalOrientation(true);\n  /**\n   * Interval between each option in the timepicker. The value can either be an amount of\n   * seconds (e.g. 90) or a number with a unit (e.g. 45m). Supported units are `s` for seconds,\n   * `m` for minutes or `h` for hours.\n   */\n  interval = input(parseInterval(this._defaultConfig?.interval || null), {\n    transform: parseInterval\n  });\n  /**\n   * Array of pre-defined options that the user can select from, as an alternative to using the\n   * `interval` input. An error will be thrown if both `options` and `interval` are specified.\n   */\n  options = input(null);\n  /** Whether the timepicker is open. */\n  isOpen = this._isOpen.asReadonly();\n  /** Emits when the user selects a time. */\n  selected = output();\n  /** Emits when the timepicker is opened. */\n  opened = output();\n  /** Emits when the timepicker is closed. */\n  closed = output();\n  /** ID of the active descendant option. */\n  activeDescendant = this._activeDescendant.asReadonly();\n  /** Unique ID of the timepicker's panel */\n  panelId = inject(_IdGenerator).getId('mat-timepicker-panel-');\n  /** Whether ripples within the timepicker should be disabled. */\n  disableRipple = input(this._defaultConfig?.disableRipple ?? false, {\n    transform: booleanAttribute\n  });\n  /** ARIA label for the timepicker panel. */\n  ariaLabel = input(null, {\n    alias: 'aria-label'\n  });\n  /** ID of the label element for the timepicker panel. */\n  ariaLabelledby = input(null, {\n    alias: 'aria-labelledby'\n  });\n  /** Whether the timepicker is currently disabled. */\n  disabled = computed(() => !!this._input()?.disabled());\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      validateAdapter(this._dateAdapter, this._dateFormats);\n      effect(() => {\n        const options = this.options();\n        const interval = this.interval();\n        if (options !== null && interval !== null) {\n          throw new Error('Cannot specify both the `options` and `interval` inputs at the same time');\n        } else if (options?.length === 0) {\n          throw new Error('Value of `options` input cannot be an empty array');\n        }\n      });\n    }\n    // Since the panel ID is static, we can set it once without having to maintain a host binding.\n    const element = inject(ElementRef);\n    element.nativeElement.setAttribute('mat-timepicker-panel-id', this.panelId);\n    this._handleLocaleChanges();\n    this._handleInputStateChanges();\n    this._keyManager.change.subscribe(() => this._activeDescendant.set(this._keyManager.activeItem?.id || null));\n  }\n  /** Opens the timepicker. */\n  open() {\n    const input = this._input();\n    if (!input) {\n      return;\n    }\n    // Focus should already be on the input, but this call is in case the timepicker is opened\n    // programmatically. We need to call this even if the timepicker is already open, because\n    // the user might be clicking the toggle.\n    input.focus();\n    if (this._isOpen()) {\n      return;\n    }\n    this._isOpen.set(true);\n    this._generateOptions();\n    const overlayRef = this._getOverlayRef();\n    overlayRef.updateSize({\n      width: input.getOverlayOrigin().nativeElement.offsetWidth\n    });\n    this._portal ??= new TemplatePortal(this._panelTemplate(), this._viewContainerRef);\n    // We need to check this in case `isOpen` was flipped, but change detection hasn't\n    // had a chance to run yet. See https://github.com/angular/components/issues/30637\n    if (!overlayRef.hasAttached()) {\n      overlayRef.attach(this._portal);\n    }\n    this._onOpenRender?.destroy();\n    this._onOpenRender = afterNextRender(() => {\n      const options = this._options();\n      this._syncSelectedState(input.value(), options, options[0]);\n      this._onOpenRender = null;\n    }, {\n      injector: this._injector\n    });\n    this.opened.emit();\n  }\n  /** Closes the timepicker. */\n  close() {\n    if (this._isOpen()) {\n      this._isOpen.set(false);\n      this.closed.emit();\n      if (this._animationsDisabled) {\n        this._overlayRef?.detach();\n      }\n    }\n  }\n  /** Registers an input with the timepicker. */\n  registerInput(input) {\n    const currentInput = this._input();\n    if (currentInput && input !== currentInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('MatTimepicker can only be registered with one input at a time');\n    }\n    this._input.set(input);\n  }\n  ngOnDestroy() {\n    this._keyManager.destroy();\n    this._localeChanges.unsubscribe();\n    this._onOpenRender?.destroy();\n    this._overlayRef?.dispose();\n  }\n  /** Selects a specific time value. */\n  _selectValue(option) {\n    this.close();\n    this._keyManager.setActiveItem(option);\n    this._options().forEach(current => {\n      // This is primarily here so we don't show two selected options while animating away.\n      if (current !== option) {\n        current.deselect(false);\n      }\n    });\n    this.selected.emit({\n      value: option.value,\n      source: this\n    });\n    this._input()?.focus();\n  }\n  /** Gets the value of the `aria-labelledby` attribute. */\n  _getAriaLabelledby() {\n    if (this.ariaLabel()) {\n      return null;\n    }\n    return this.ariaLabelledby() || this._input()?._getLabelId() || null;\n  }\n  /** Handles animation events coming from the panel. */\n  _handleAnimationEnd(event) {\n    if (event.animationName === '_mat-timepicker-exit') {\n      this._overlayRef?.detach();\n    }\n  }\n  /** Creates an overlay reference for the timepicker panel. */\n  _getOverlayRef() {\n    if (this._overlayRef) {\n      return this._overlayRef;\n    }\n    const positionStrategy = this._overlay.position().flexibleConnectedTo(this._input().getOverlayOrigin()).withFlexibleDimensions(false).withPush(false).withTransformOriginOn('.mat-timepicker-panel').withPositions([{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass: 'mat-timepicker-above'\n    }]);\n    this._overlayRef = this._overlay.create({\n      positionStrategy,\n      scrollStrategy: this._scrollStrategyFactory(),\n      direction: this._dir || 'ltr',\n      hasBackdrop: false\n    });\n    this._overlayRef.detachments().subscribe(() => this.close());\n    this._overlayRef.keydownEvents().subscribe(event => this._handleKeydown(event));\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      const target = _getEventTarget(event);\n      const origin = this._input()?.getOverlayOrigin().nativeElement;\n      if (target && origin && target !== origin && !origin.contains(target)) {\n        this.close();\n      }\n    });\n    return this._overlayRef;\n  }\n  /** Generates the list of options from which the user can select.. */\n  _generateOptions() {\n    // Default the interval to 30 minutes.\n    const interval = this.interval() ?? 30 * 60;\n    const options = this.options();\n    if (options !== null) {\n      this._timeOptions = options;\n    } else {\n      const input = this._input();\n      const adapter = this._dateAdapter;\n      const timeFormat = this._dateFormats.display.timeInput;\n      const min = input?.min() || adapter.setTime(adapter.today(), 0, 0, 0);\n      const max = input?.max() || adapter.setTime(adapter.today(), 23, 59, 0);\n      const cacheKey = interval + '/' + adapter.format(min, timeFormat) + '/' + adapter.format(max, timeFormat);\n      // Don't re-generate the options if the inputs haven't changed.\n      if (cacheKey !== this._optionsCacheKey) {\n        this._optionsCacheKey = cacheKey;\n        this._timeOptions = generateOptions(adapter, this._dateFormats, min, max, interval);\n      }\n    }\n  }\n  /**\n   * Synchronizes the internal state of the component based on a specific selected date.\n   * @param value Currently selected date.\n   * @param options Options rendered out in the timepicker.\n   * @param fallback Option to set as active if no option is selected.\n   */\n  _syncSelectedState(value, options, fallback) {\n    let hasSelected = false;\n    for (const option of options) {\n      if (value && this._dateAdapter.sameTime(option.value, value)) {\n        option.select(false);\n        scrollOptionIntoView(option, 'center');\n        untracked(() => this._keyManager.setActiveItem(option));\n        hasSelected = true;\n      } else {\n        option.deselect(false);\n      }\n    }\n    // If no option was selected, we need to reset the key manager since\n    // it might be holding onto an option that no longer exists.\n    if (!hasSelected) {\n      if (fallback) {\n        untracked(() => this._keyManager.setActiveItem(fallback));\n        scrollOptionIntoView(fallback, 'center');\n      } else {\n        untracked(() => this._keyManager.setActiveItem(-1));\n      }\n    }\n  }\n  /** Handles keyboard events while the overlay is open. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    if (keyCode === TAB) {\n      this.close();\n    } else if (keyCode === ESCAPE && !hasModifierKey(event)) {\n      event.preventDefault();\n      this.close();\n    } else if (keyCode === ENTER) {\n      event.preventDefault();\n      if (this._keyManager.activeItem) {\n        this._selectValue(this._keyManager.activeItem);\n      } else {\n        this.close();\n      }\n    } else {\n      const previousActive = this._keyManager.activeItem;\n      this._keyManager.onKeydown(event);\n      const currentActive = this._keyManager.activeItem;\n      if (currentActive && currentActive !== previousActive) {\n        scrollOptionIntoView(currentActive, 'nearest');\n      }\n    }\n  }\n  /** Sets up the logic that updates the timepicker when the locale changes. */\n  _handleLocaleChanges() {\n    // Re-generate the options list if the locale changes.\n    this._localeChanges = this._dateAdapter.localeChanges.subscribe(() => {\n      this._optionsCacheKey = null;\n      if (this.isOpen()) {\n        this._generateOptions();\n      }\n    });\n  }\n  /**\n   * Sets up the logic that updates the timepicker when the state of the connected input changes.\n   */\n  _handleInputStateChanges() {\n    effect(() => {\n      const input = this._input();\n      const options = this._options();\n      if (this._isOpen() && input) {\n        this._syncSelectedState(input.value(), options, null);\n      }\n    });\n  }\n  static ɵfac = function MatTimepicker_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTimepicker)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTimepicker,\n    selectors: [[\"mat-timepicker\"]],\n    viewQuery: function MatTimepicker_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuerySignal(ctx._panelTemplate, _c0, 5);\n        i0.ɵɵviewQuerySignal(ctx._options, MatOption, 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵqueryAdvance(2);\n      }\n    },\n    inputs: {\n      interval: [1, \"interval\"],\n      options: [1, \"options\"],\n      disableRipple: [1, \"disableRipple\"],\n      ariaLabel: [1, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [1, \"aria-labelledby\", \"ariaLabelledby\"]\n    },\n    outputs: {\n      selected: \"selected\",\n      opened: \"opened\",\n      closed: \"closed\"\n    },\n    exportAs: [\"matTimepicker\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_OPTION_PARENT_COMPONENT,\n      useExisting: MatTimepicker\n    }])],\n    decls: 2,\n    vars: 0,\n    consts: [[\"panelTemplate\", \"\"], [\"role\", \"listbox\", 1, \"mat-timepicker-panel\", 3, \"animationend\", \"id\"], [3, \"value\"], [3, \"onSelectionChange\", \"value\"]],\n    template: function MatTimepicker_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatTimepicker_ng_template_0_Template, 3, 7, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n    },\n    dependencies: [MatOption],\n    styles: [\"@keyframes _mat-timepicker-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-timepicker-exit{from{opacity:1}to{opacity:0}}mat-timepicker{display:none}.mat-timepicker-panel{width:100%;max-height:256px;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;border-bottom-left-radius:var(--mat-timepicker-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-timepicker-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-timepicker-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-timepicker-container-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){.mat-timepicker-panel{outline:solid 1px}}.mat-timepicker-above .mat-timepicker-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-timepicker-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mat-timepicker-container-shape, var(--mat-sys-corner-extra-small))}.mat-timepicker-panel-animations-enabled{animation:_mat-timepicker-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-timepicker-panel-animations-enabled.mat-timepicker-panel-exit{animation:_mat-timepicker-exit 100ms linear}.mat-timepicker-input[readonly]{cursor:pointer}@media(forced-colors: active){.mat-timepicker-toggle-default-icon{color:CanvasText}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTimepicker, [{\n    type: Component,\n    args: [{\n      selector: 'mat-timepicker',\n      exportAs: 'matTimepicker',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [MatOption],\n      providers: [{\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatTimepicker\n      }],\n      template: \"<ng-template #panelTemplate>\\n  <div\\n    role=\\\"listbox\\\"\\n    class=\\\"mat-timepicker-panel\\\"\\n    [class.mat-timepicker-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [class.mat-timepicker-panel-exit]=\\\"!isOpen()\\\"\\n    [attr.aria-label]=\\\"ariaLabel() || null\\\"\\n    [attr.aria-labelledby]=\\\"_getAriaLabelledby()\\\"\\n    [id]=\\\"panelId\\\"\\n    (animationend)=\\\"_handleAnimationEnd($event)\\\">\\n    @for (option of _timeOptions; track option.value) {\\n      <mat-option\\n        [value]=\\\"option.value\\\"\\n        (onSelectionChange)=\\\"_selectValue($event.source)\\\">{{option.label}}</mat-option>\\n    }\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"@keyframes _mat-timepicker-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-timepicker-exit{from{opacity:1}to{opacity:0}}mat-timepicker{display:none}.mat-timepicker-panel{width:100%;max-height:256px;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;border-bottom-left-radius:var(--mat-timepicker-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-timepicker-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-timepicker-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-timepicker-container-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){.mat-timepicker-panel{outline:solid 1px}}.mat-timepicker-above .mat-timepicker-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-timepicker-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mat-timepicker-container-shape, var(--mat-sys-corner-extra-small))}.mat-timepicker-panel-animations-enabled{animation:_mat-timepicker-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-timepicker-panel-animations-enabled.mat-timepicker-panel-exit{animation:_mat-timepicker-exit 100ms linear}.mat-timepicker-input[readonly]{cursor:pointer}@media(forced-colors: active){.mat-timepicker-toggle-default-icon{color:CanvasText}}\\n\"]\n    }]\n  }], () => [], null);\n})();\n/**\n * Scrolls an option into view.\n * @param option Option to be scrolled into view.\n * @param position Position to which to align the option relative to the scrollable container.\n */\nfunction scrollOptionIntoView(option, position) {\n  option._getHostElement().scrollIntoView({\n    block: position,\n    inline: position\n  });\n}\n\n/**\n * Input that can be used to enter time and connect to a `mat-timepicker`.\n */\nclass MatTimepickerInput {\n  _elementRef = inject(ElementRef);\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _dateFormats = inject(MAT_DATE_FORMATS, {\n    optional: true\n  });\n  _formField = inject(MAT_FORM_FIELD, {\n    optional: true\n  });\n  _onChange;\n  _onTouched;\n  _validatorOnChange;\n  _cleanupClick;\n  _accessorDisabled = signal(false);\n  _localeSubscription;\n  _timepickerSubscription;\n  _validator;\n  _lastValueValid = true;\n  _lastValidDate = null;\n  /** Value of the `aria-activedescendant` attribute. */\n  _ariaActiveDescendant = computed(() => {\n    const timepicker = this.timepicker();\n    const isOpen = timepicker.isOpen();\n    const activeDescendant = timepicker.activeDescendant();\n    return isOpen && activeDescendant ? activeDescendant : null;\n  });\n  /** Value of the `aria-expanded` attribute. */\n  _ariaExpanded = computed(() => this.timepicker().isOpen() + '');\n  /** Value of the `aria-controls` attribute. */\n  _ariaControls = computed(() => {\n    const timepicker = this.timepicker();\n    return timepicker.isOpen() ? timepicker.panelId : null;\n  });\n  /** Current value of the input. */\n  value = model(null);\n  /** Timepicker that the input is associated with. */\n  timepicker = input.required({\n    alias: 'matTimepicker'\n  });\n  /**\n   * Minimum time that can be selected or typed in. Can be either\n   * a date object (only time will be used) or a valid time string.\n   */\n  min = input(null, {\n    alias: 'matTimepickerMin',\n    transform: value => this._transformDateInput(value)\n  });\n  /**\n   * Maximum time that can be selected or typed in. Can be either\n   * a date object (only time will be used) or a valid time string.\n   */\n  max = input(null, {\n    alias: 'matTimepickerMax',\n    transform: value => this._transformDateInput(value)\n  });\n  /** Whether the input is disabled. */\n  disabled = computed(() => this.disabledInput() || this._accessorDisabled());\n  /**\n   * Whether the input should be disabled through the template.\n   * @docs-private\n   */\n  disabledInput = input(false, {\n    transform: booleanAttribute,\n    alias: 'disabled'\n  });\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      validateAdapter(this._dateAdapter, this._dateFormats);\n    }\n    const renderer = inject(Renderer2);\n    this._validator = this._getValidator();\n    this._respondToValueChanges();\n    this._respondToMinMaxChanges();\n    this._registerTimepicker();\n    this._localeSubscription = this._dateAdapter.localeChanges.subscribe(() => {\n      if (!this._hasFocus()) {\n        this._formatValue(this.value());\n      }\n    });\n    // Bind the click listener manually to the overlay origin, because we want the entire\n    // form field to be clickable, if the timepicker is used in `mat-form-field`.\n    this._cleanupClick = renderer.listen(this.getOverlayOrigin().nativeElement, 'click', this._handleClick);\n  }\n  /**\n   * Implemented as a part of `ControlValueAccessor`.\n   * @docs-private\n   */\n  writeValue(value) {\n    // Note that we need to deserialize here, rather than depend on the value change effect,\n    // because `getValidDateOrNull` will clobber the value if it's parseable, but not created by\n    // the current adapter (see #30140).\n    const deserialized = this._dateAdapter.deserialize(value);\n    this.value.set(this._dateAdapter.getValidDateOrNull(deserialized));\n  }\n  /**\n   * Implemented as a part of `ControlValueAccessor`.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Implemented as a part of `ControlValueAccessor`.\n   * @docs-private\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Implemented as a part of `ControlValueAccessor`.\n   * @docs-private\n   */\n  setDisabledState(isDisabled) {\n    this._accessorDisabled.set(isDisabled);\n  }\n  /**\n   * Implemented as a part of `Validator`.\n   * @docs-private\n   */\n  validate(control) {\n    return this._validator(control);\n  }\n  /**\n   * Implemented as a part of `Validator`.\n   * @docs-private\n   */\n  registerOnValidatorChange(fn) {\n    this._validatorOnChange = fn;\n  }\n  /** Gets the element to which the timepicker popup should be attached. */\n  getOverlayOrigin() {\n    return this._formField?.getConnectedOverlayOrigin() || this._elementRef;\n  }\n  /** Focuses the input. */\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n  ngOnDestroy() {\n    this._cleanupClick();\n    this._timepickerSubscription?.unsubscribe();\n    this._localeSubscription.unsubscribe();\n  }\n  /** Gets the ID of the input's label. */\n  _getLabelId() {\n    return this._formField?.getLabelId() || null;\n  }\n  /** Handles clicks on the input or the containing form field. */\n  _handleClick = () => {\n    if (!this.disabled()) {\n      this.timepicker().open();\n    }\n  };\n  /** Handles the `input` event. */\n  _handleInput(value) {\n    const currentValue = this.value();\n    const date = this._dateAdapter.parseTime(value, this._dateFormats.parse.timeInput);\n    const hasChanged = !this._dateAdapter.sameTime(date, currentValue);\n    if (!date || hasChanged || !!(value && !currentValue)) {\n      // We need to fire the CVA change event for all nulls, otherwise the validators won't run.\n      this._assignUserSelection(date, true);\n    } else {\n      // Call the validator even if the value hasn't changed since\n      // some fields change depending on what the user has entered.\n      this._validatorOnChange?.();\n    }\n  }\n  /** Handles the `blur` event. */\n  _handleBlur() {\n    const value = this.value();\n    // Only reformat on blur so the value doesn't change while the user is interacting.\n    if (value && this._isValid(value)) {\n      this._formatValue(value);\n    }\n    if (!this.timepicker().isOpen()) {\n      this._onTouched?.();\n    }\n  }\n  /** Handles the `keydown` event. */\n  _handleKeydown(event) {\n    // All keyboard events while open are handled through the timepicker.\n    if (this.timepicker().isOpen() || this.disabled()) {\n      return;\n    }\n    if (event.keyCode === ESCAPE && !hasModifierKey(event) && this.value() !== null) {\n      event.preventDefault();\n      this.value.set(null);\n      this._formatValue(null);\n    } else if (event.keyCode === DOWN_ARROW || event.keyCode === UP_ARROW) {\n      event.preventDefault();\n      this.timepicker().open();\n    }\n  }\n  /** Sets up the code that watches for changes in the value and adjusts the input. */\n  _respondToValueChanges() {\n    effect(() => {\n      const value = this._dateAdapter.deserialize(this.value());\n      const wasValid = this._lastValueValid;\n      this._lastValueValid = this._isValid(value);\n      // Reformat the value if it changes while the user isn't interacting.\n      if (!this._hasFocus()) {\n        this._formatValue(value);\n      }\n      if (value && this._lastValueValid) {\n        this._lastValidDate = value;\n      }\n      // Trigger the validator if the state changed.\n      if (wasValid !== this._lastValueValid) {\n        this._validatorOnChange?.();\n      }\n    });\n  }\n  /** Sets up the logic that registers the input with the timepicker. */\n  _registerTimepicker() {\n    effect(() => {\n      const timepicker = this.timepicker();\n      timepicker.registerInput(this);\n      timepicker.closed.subscribe(() => this._onTouched?.());\n      timepicker.selected.subscribe(({\n        value\n      }) => {\n        if (!this._dateAdapter.sameTime(value, this.value())) {\n          this._assignUserSelection(value, true);\n          this._formatValue(value);\n        }\n      });\n    });\n  }\n  /** Sets up the logic that adjusts the input if the min/max changes. */\n  _respondToMinMaxChanges() {\n    effect(() => {\n      // Read the min/max so the effect knows when to fire.\n      this.min();\n      this.max();\n      this._validatorOnChange?.();\n    });\n  }\n  /**\n   * Assigns a value set by the user to the input's model.\n   * @param selection Time selected by the user that should be assigned.\n   * @param propagateToAccessor Whether the value should be propagated to the ControlValueAccessor.\n   */\n  _assignUserSelection(selection, propagateToAccessor) {\n    if (selection == null || !this._isValid(selection)) {\n      this.value.set(selection);\n    } else {\n      // If a datepicker and timepicker are writing to the same object and the user enters an\n      // invalid time into the timepicker, we may end up clearing their selection from the\n      // datepicker. If the user enters a valid time afterwards, the datepicker's selection will\n      // have been lost. This logic restores the previously-valid date and sets its time to\n      // the newly-selected time.\n      const adapter = this._dateAdapter;\n      const target = adapter.getValidDateOrNull(this._lastValidDate || this.value());\n      const hours = adapter.getHours(selection);\n      const minutes = adapter.getMinutes(selection);\n      const seconds = adapter.getSeconds(selection);\n      this.value.set(target ? adapter.setTime(target, hours, minutes, seconds) : selection);\n    }\n    if (propagateToAccessor) {\n      this._onChange?.(this.value());\n    }\n  }\n  /** Formats the current value and assigns it to the input. */\n  _formatValue(value) {\n    value = this._dateAdapter.getValidDateOrNull(value);\n    this._elementRef.nativeElement.value = value == null ? '' : this._dateAdapter.format(value, this._dateFormats.display.timeInput);\n  }\n  /** Checks whether a value is valid. */\n  _isValid(value) {\n    return !value || this._dateAdapter.isValid(value);\n  }\n  /** Transforms an arbitrary value into a value that can be assigned to a date-based input. */\n  _transformDateInput(value) {\n    const date = typeof value === 'string' ? this._dateAdapter.parseTime(value, this._dateFormats.parse.timeInput) : this._dateAdapter.deserialize(value);\n    return date && this._dateAdapter.isValid(date) ? date : null;\n  }\n  /** Whether the input is currently focused. */\n  _hasFocus() {\n    return _getFocusedElementPierceShadowDom() === this._elementRef.nativeElement;\n  }\n  /** Gets a function that can be used to validate the input. */\n  _getValidator() {\n    return Validators.compose([() => this._lastValueValid ? null : {\n      'matTimepickerParse': {\n        'text': this._elementRef.nativeElement.value\n      }\n    }, control => {\n      const controlValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(control.value));\n      const min = this.min();\n      return !min || !controlValue || this._dateAdapter.compareTime(min, controlValue) <= 0 ? null : {\n        'matTimepickerMin': {\n          'min': min,\n          'actual': controlValue\n        }\n      };\n    }, control => {\n      const controlValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(control.value));\n      const max = this.max();\n      return !max || !controlValue || this._dateAdapter.compareTime(max, controlValue) >= 0 ? null : {\n        'matTimepickerMax': {\n          'max': max,\n          'actual': controlValue\n        }\n      };\n    }]);\n  }\n  static ɵfac = function MatTimepickerInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTimepickerInput)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTimepickerInput,\n    selectors: [[\"input\", \"matTimepicker\", \"\"]],\n    hostAttrs: [\"role\", \"combobox\", \"type\", \"text\", \"aria-haspopup\", \"listbox\", 1, \"mat-timepicker-input\"],\n    hostVars: 5,\n    hostBindings: function MatTimepickerInput_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"blur\", function MatTimepickerInput_blur_HostBindingHandler() {\n          return ctx._handleBlur();\n        })(\"input\", function MatTimepickerInput_input_HostBindingHandler($event) {\n          return ctx._handleInput($event.target.value);\n        })(\"keydown\", function MatTimepickerInput_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        let tmp_4_0;\n        i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n        i0.ɵɵattribute(\"aria-activedescendant\", ctx._ariaActiveDescendant())(\"aria-expanded\", ctx._ariaExpanded())(\"aria-controls\", ctx._ariaControls())(\"mat-timepicker-id\", (tmp_4_0 = ctx.timepicker()) == null ? null : tmp_4_0.panelId);\n      }\n    },\n    inputs: {\n      value: [1, \"value\"],\n      timepicker: [1, \"matTimepicker\", \"timepicker\"],\n      min: [1, \"matTimepickerMin\", \"min\"],\n      max: [1, \"matTimepickerMax\", \"max\"],\n      disabledInput: [1, \"disabled\", \"disabledInput\"]\n    },\n    outputs: {\n      value: \"valueChange\"\n    },\n    exportAs: [\"matTimepickerInput\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: MatTimepickerInput,\n      multi: true\n    }, {\n      provide: NG_VALIDATORS,\n      useExisting: MatTimepickerInput,\n      multi: true\n    }, {\n      provide: MAT_INPUT_VALUE_ACCESSOR,\n      useExisting: MatTimepickerInput\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTimepickerInput, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matTimepicker]',\n      exportAs: 'matTimepickerInput',\n      host: {\n        'class': 'mat-timepicker-input',\n        'role': 'combobox',\n        'type': 'text',\n        'aria-haspopup': 'listbox',\n        '[attr.aria-activedescendant]': '_ariaActiveDescendant()',\n        '[attr.aria-expanded]': '_ariaExpanded()',\n        '[attr.aria-controls]': '_ariaControls()',\n        '[attr.mat-timepicker-id]': 'timepicker()?.panelId',\n        '[disabled]': 'disabled()',\n        '(blur)': '_handleBlur()',\n        '(input)': '_handleInput($event.target.value)',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: MatTimepickerInput,\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: MatTimepickerInput,\n        multi: true\n      }, {\n        provide: MAT_INPUT_VALUE_ACCESSOR,\n        useExisting: MatTimepickerInput\n      }]\n    }]\n  }], () => [], null);\n})();\n\n/** Button that can be used to open a `mat-timepicker`. */\nclass MatTimepickerToggle {\n  _defaultConfig = inject(MAT_TIMEPICKER_CONFIG, {\n    optional: true\n  });\n  _defaultTabIndex = (() => {\n    const value = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    const parsed = Number(value);\n    return isNaN(parsed) ? null : parsed;\n  })();\n  _isDisabled = computed(() => {\n    const timepicker = this.timepicker();\n    return this.disabled() || timepicker.disabled();\n  });\n  /** Timepicker instance that the button will toggle. */\n  timepicker = input.required({\n    alias: 'for'\n  });\n  /** Screen-reader label for the button. */\n  ariaLabel = input(undefined, {\n    alias: 'aria-label'\n  });\n  /** Screen-reader labelled by id for the button. */\n  ariaLabelledby = input(undefined, {\n    alias: 'aria-labelledby'\n  });\n  /** Default aria-label for the toggle if none is provided. */\n  _defaultAriaLabel = 'Open timepicker options';\n  /** Whether the toggle button is disabled. */\n  disabled = input(false, {\n    transform: booleanAttribute,\n    alias: 'disabled'\n  });\n  /** Tabindex for the toggle. */\n  tabIndex = input(this._defaultTabIndex);\n  /** Whether ripples on the toggle should be disabled. */\n  disableRipple = input(this._defaultConfig?.disableRipple ?? false, {\n    transform: booleanAttribute\n  });\n  /** Opens the connected timepicker. */\n  _open(event) {\n    if (this.timepicker() && !this._isDisabled()) {\n      this.timepicker().open();\n      event.stopPropagation();\n    }\n  }\n  /**\n   * Checks for ariaLabelledby and if empty uses custom\n   * aria-label or defaultAriaLabel if neither is provided.\n   */\n  getAriaLabel() {\n    return this.ariaLabelledby() ? null : this.ariaLabel() || this._defaultAriaLabel;\n  }\n  static ɵfac = function MatTimepickerToggle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTimepickerToggle)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTimepickerToggle,\n    selectors: [[\"mat-timepicker-toggle\"]],\n    hostAttrs: [1, \"mat-timepicker-toggle\"],\n    hostVars: 1,\n    hostBindings: function MatTimepickerToggle_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatTimepickerToggle_click_HostBindingHandler($event) {\n          return ctx._open($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"tabindex\", null);\n      }\n    },\n    inputs: {\n      timepicker: [1, \"for\", \"timepicker\"],\n      ariaLabel: [1, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [1, \"aria-labelledby\", \"ariaLabelledby\"],\n      disabled: [1, \"disabled\"],\n      tabIndex: [1, \"tabIndex\"],\n      disableRipple: [1, \"disableRipple\"]\n    },\n    exportAs: [\"matTimepickerToggle\"],\n    ngContentSelectors: _c2,\n    decls: 3,\n    vars: 6,\n    consts: [[\"mat-icon-button\", \"\", \"type\", \"button\", \"aria-haspopup\", \"listbox\", 3, \"disabled\", \"disableRipple\"], [\"height\", \"24px\", \"width\", \"24px\", \"viewBox\", \"0 -960 960 960\", \"fill\", \"currentColor\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-timepicker-toggle-default-icon\"], [\"d\", \"m612-292 56-56-148-148v-184h-80v216l172 172ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 320q133 0 226.5-93.5T800-480q0-133-93.5-226.5T480-800q-133 0-226.5 93.5T160-480q0 133 93.5 226.5T480-160Z\"]],\n    template: function MatTimepickerToggle_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelementStart(0, \"button\", 0);\n        i0.ɵɵprojection(1, 0, null, MatTimepickerToggle_ProjectionFallback_1_Template, 2, 0);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"disabled\", ctx._isDisabled())(\"disableRipple\", ctx.disableRipple());\n        i0.ɵɵattribute(\"aria-label\", ctx.getAriaLabel())(\"aria-labelledby\", ctx.ariaLabelledby())(\"aria-expanded\", ctx.timepicker().isOpen())(\"tabindex\", ctx._isDisabled() ? -1 : ctx.tabIndex());\n      }\n    },\n    dependencies: [MatIconButton],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTimepickerToggle, [{\n    type: Component,\n    args: [{\n      selector: 'mat-timepicker-toggle',\n      host: {\n        'class': 'mat-timepicker-toggle',\n        '[attr.tabindex]': 'null',\n        // Bind the `click` on the host, rather than the inner `button`, so that we can call\n        // `stopPropagation` on it without affecting the user's `click` handlers. We need to stop\n        // it so that the input doesn't get focused automatically by the form field (See #21836).\n        '(click)': '_open($event)'\n      },\n      exportAs: 'matTimepickerToggle',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatIconButton],\n      template: \"<button\\n  mat-icon-button\\n  type=\\\"button\\\"\\n  aria-haspopup=\\\"listbox\\\"\\n  [attr.aria-label]=\\\"getAriaLabel()\\\"\\n  [attr.aria-labelledby]=\\\"ariaLabelledby()\\\"\\n  [attr.aria-expanded]=\\\"timepicker().isOpen()\\\"\\n  [attr.tabindex]=\\\"_isDisabled() ? -1 : tabIndex()\\\"\\n  [disabled]=\\\"_isDisabled()\\\"\\n  [disableRipple]=\\\"disableRipple()\\\">\\n\\n  <ng-content select=\\\"[matTimepickerToggleIcon]\\\">\\n    <svg\\n      class=\\\"mat-timepicker-toggle-default-icon\\\"\\n      height=\\\"24px\\\"\\n      width=\\\"24px\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      fill=\\\"currentColor\\\"\\n      focusable=\\\"false\\\"\\n      aria-hidden=\\\"true\\\">\\n      <path d=\\\"m612-292 56-56-148-148v-184h-80v216l172 172ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 320q133 0 226.5-93.5T800-480q0-133-93.5-226.5T480-800q-133 0-226.5 93.5T160-480q0 133 93.5 226.5T480-160Z\\\"/>\\n    </svg>\\n  </ng-content>\\n</button>\\n\"\n    }]\n  }], null, null);\n})();\nclass MatTimepickerModule {\n  static ɵfac = function MatTimepickerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTimepickerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatTimepickerModule,\n    imports: [MatTimepicker, MatTimepickerInput, MatTimepickerToggle],\n    exports: [CdkScrollableModule, MatTimepicker, MatTimepickerInput, MatTimepickerToggle]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatTimepicker, MatTimepickerToggle, CdkScrollableModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTimepickerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatTimepicker, MatTimepickerInput, MatTimepickerToggle],\n      exports: [CdkScrollableModule, MatTimepicker, MatTimepickerInput, MatTimepickerToggle]\n    }]\n  }], null, null);\n})();\nexport { MAT_TIMEPICKER_CONFIG, MAT_TIMEPICKER_SCROLL_STRATEGY, MatTimepicker, MatTimepickerInput, MatTimepickerModule, MatTimepickerToggle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,IAAM,MAAM,CAAC,eAAe;AAC5B,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,cAAc,CAAC;AACpC,IAAG,WAAW,qBAAqB,SAAS,mFAAmF,QAAQ;AACrI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,OAAO,MAAM,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,SAAS,UAAU,KAAK;AACtC,IAAG,UAAU;AACb,IAAG,kBAAkB,UAAU,KAAK;AAAA,EACtC;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,gBAAgB,SAAS,iEAAiE,QAAQ;AAC9G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,iBAAiB,GAAG,4CAA4C,GAAG,GAAG,cAAc,GAAG,UAAU;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,2CAA2C,CAAC,OAAO,mBAAmB,EAAE,6BAA6B,CAAC,OAAO,OAAO,CAAC;AACpI,IAAG,WAAW,MAAM,OAAO,OAAO;AAClC,IAAG,YAAY,cAAc,OAAO,UAAU,KAAK,IAAI,EAAE,mBAAmB,OAAO,mBAAmB,CAAC;AACvG,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,YAAY;AAAA,EACnC;AACF;AACA,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;AAClD,IAAM,MAAM,CAAC,2BAA2B;AACxC,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,mBAAmB;AAIzB,IAAM,wBAAwB,IAAI,eAAe,uBAAuB;AAExE,SAAS,cAAc,OAAO;AAC5B,MAAI;AACJ,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT,WAAW,OAAO,UAAU,UAAU;AACpC,aAAS;AAAA,EACX,OAAO;AACL,QAAI,MAAM,KAAK,EAAE,WAAW,GAAG;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,SAAS,MAAM,MAAM,gBAAgB;AAC3C,UAAM,SAAS,SAAS,WAAW,OAAO,CAAC,CAAC,IAAI;AAChD,UAAM,OAAO,SAAS,CAAC,GAAG,YAAY,KAAK;AAC3C,QAAI,CAAC,UAAU,WAAW,QAAQ,MAAM,MAAM,GAAG;AAC/C,aAAO;AAAA,IACT;AACA,QAAI,SAAS,OAAO,SAAS,UAAU,SAAS,SAAS;AACvD,eAAS,SAAS;AAAA,IACpB,WAAW,SAAS,OAAO,SAAS,SAAS,SAAS,YAAY,SAAS,WAAW;AACpF,eAAS,SAAS;AAAA,IACpB,OAAO;AACL,eAAS;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;AASA,SAAS,gBAAgB,SAAS,SAAS,KAAK,KAAK,UAAU;AAC7D,QAAM,UAAU,CAAC;AACjB,MAAI,UAAU,QAAQ,YAAY,KAAK,GAAG,IAAI,IAAI,MAAM;AACxD,SAAO,QAAQ,SAAS,SAAS,GAAG,KAAK,QAAQ,YAAY,SAAS,GAAG,IAAI,KAAK,QAAQ,QAAQ,OAAO,GAAG;AAC1G,YAAQ,KAAK;AAAA,MACX,OAAO;AAAA,MACP,OAAO,QAAQ,OAAO,SAAS,QAAQ,QAAQ,eAAe;AAAA,IAChE,CAAC;AACD,cAAU,QAAQ,WAAW,SAAS,QAAQ;AAAA,EAChD;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,SAAS,SAAS;AACzC,WAAS,oBAAoB,UAAU;AACrC,WAAO,MAAM,wCAAwC,QAAQ,iMAA2M;AAAA,EAC1Q;AACA,MAAI,CAAC,SAAS;AACZ,UAAM,oBAAoB,aAAa;AAAA,EACzC;AACA,MAAI,CAAC,SAAS;AACZ,UAAM,oBAAoB,kBAAkB;AAAA,EAC9C;AACA,MAAI,QAAQ,QAAQ,cAAc,UAAa,QAAQ,QAAQ,oBAAoB,UAAa,QAAQ,MAAM,cAAc,QAAW;AACrI,UAAM,IAAI,MAAM,4NAAsO;AAAA,EACxP;AACF;AAGA,IAAM,iCAAiC,IAAI,eAAe,kCAAkC;AAAA,EAC1F,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,EACnD;AACF,CAAC;AAKD,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,WAAW,OAAO,OAAO;AAAA,EACzB,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,YAAY,OAAO,QAAQ;AAAA,EAC3B,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,kBAAkB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,yBAAyB,OAAO,8BAA8B;AAAA,EAC9D,sBAAsB,OAAO,uBAAuB;AAAA,IAClD,UAAU;AAAA,EACZ,CAAC,MAAM;AAAA,EACP,UAAU,OAAO,KAAK;AAAA,EACtB,oBAAoB,OAAO,IAAI;AAAA,EAC/B,SAAS,OAAO,IAAI;AAAA,EACpB,cAAc;AAAA,EACd,UAAU;AAAA,EACV,mBAAmB;AAAA,EACnB;AAAA,EACA,gBAAgB;AAAA,EAChB,iBAAiB,UAAU,SAAS,eAAe;AAAA,EACnD,eAAe,CAAC;AAAA,EAChB,WAAW,aAAa,SAAS;AAAA,EACjC,cAAc,IAAI,2BAA2B,KAAK,UAAU,KAAK,SAAS,EAAE,eAAe,IAAI,EAAE,eAAe,IAAI,EAAE,wBAAwB,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlJ,WAAW,MAAM,cAAc,KAAK,gBAAgB,YAAY,IAAI,GAAG;AAAA,IACrE,WAAW;AAAA,EACb,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,MAAM,IAAI;AAAA;AAAA,EAEpB,SAAS,KAAK,QAAQ,WAAW;AAAA;AAAA,EAEjC,WAAW,OAAO;AAAA;AAAA,EAElB,SAAS,OAAO;AAAA;AAAA,EAEhB,SAAS,OAAO;AAAA;AAAA,EAEhB,mBAAmB,KAAK,kBAAkB,WAAW;AAAA;AAAA,EAErD,UAAU,OAAO,YAAY,EAAE,MAAM,uBAAuB;AAAA;AAAA,EAE5D,gBAAgB,MAAM,KAAK,gBAAgB,iBAAiB,OAAO;AAAA,IACjE,WAAW;AAAA,EACb,CAAC;AAAA;AAAA,EAED,YAAY,MAAM,MAAM;AAAA,IACtB,OAAO;AAAA,EACT,CAAC;AAAA;AAAA,EAED,iBAAiB,MAAM,MAAM;AAAA,IAC3B,OAAO;AAAA,EACT,CAAC;AAAA;AAAA,EAED,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,OAAO,GAAG,SAAS,CAAC;AAAA,EACrD,cAAc;AACZ,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,sBAAgB,KAAK,cAAc,KAAK,YAAY;AACpD,aAAO,MAAM;AACX,cAAM,UAAU,KAAK,QAAQ;AAC7B,cAAM,WAAW,KAAK,SAAS;AAC/B,YAAI,YAAY,QAAQ,aAAa,MAAM;AACzC,gBAAM,IAAI,MAAM,0EAA0E;AAAA,QAC5F,WAAW,SAAS,WAAW,GAAG;AAChC,gBAAM,IAAI,MAAM,mDAAmD;AAAA,QACrE;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,UAAU,OAAO,UAAU;AACjC,YAAQ,cAAc,aAAa,2BAA2B,KAAK,OAAO;AAC1E,SAAK,qBAAqB;AAC1B,SAAK,yBAAyB;AAC9B,SAAK,YAAY,OAAO,UAAU,MAAM,KAAK,kBAAkB,IAAI,KAAK,YAAY,YAAY,MAAM,IAAI,CAAC;AAAA,EAC7G;AAAA;AAAA,EAEA,OAAO;AACL,UAAMA,SAAQ,KAAK,OAAO;AAC1B,QAAI,CAACA,QAAO;AACV;AAAA,IACF;AAIA,IAAAA,OAAM,MAAM;AACZ,QAAI,KAAK,QAAQ,GAAG;AAClB;AAAA,IACF;AACA,SAAK,QAAQ,IAAI,IAAI;AACrB,SAAK,iBAAiB;AACtB,UAAM,aAAa,KAAK,eAAe;AACvC,eAAW,WAAW;AAAA,MACpB,OAAOA,OAAM,iBAAiB,EAAE,cAAc;AAAA,IAChD,CAAC;AACD,SAAK,YAAY,IAAI,eAAe,KAAK,eAAe,GAAG,KAAK,iBAAiB;AAGjF,QAAI,CAAC,WAAW,YAAY,GAAG;AAC7B,iBAAW,OAAO,KAAK,OAAO;AAAA,IAChC;AACA,SAAK,eAAe,QAAQ;AAC5B,SAAK,gBAAgB,gBAAgB,MAAM;AACzC,YAAM,UAAU,KAAK,SAAS;AAC9B,WAAK,mBAAmBA,OAAM,MAAM,GAAG,SAAS,QAAQ,CAAC,CAAC;AAC1D,WAAK,gBAAgB;AAAA,IACvB,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AACD,SAAK,OAAO,KAAK;AAAA,EACnB;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,QAAQ,IAAI,KAAK;AACtB,WAAK,OAAO,KAAK;AACjB,UAAI,KAAK,qBAAqB;AAC5B,aAAK,aAAa,OAAO;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,cAAcA,QAAO;AACnB,UAAM,eAAe,KAAK,OAAO;AACjC,QAAI,gBAAgBA,WAAU,iBAAiB,OAAO,cAAc,eAAe,YAAY;AAC7F,YAAM,IAAI,MAAM,+DAA+D;AAAA,IACjF;AACA,SAAK,OAAO,IAAIA,MAAK;AAAA,EACvB;AAAA,EACA,cAAc;AACZ,SAAK,YAAY,QAAQ;AACzB,SAAK,eAAe,YAAY;AAChC,SAAK,eAAe,QAAQ;AAC5B,SAAK,aAAa,QAAQ;AAAA,EAC5B;AAAA;AAAA,EAEA,aAAa,QAAQ;AACnB,SAAK,MAAM;AACX,SAAK,YAAY,cAAc,MAAM;AACrC,SAAK,SAAS,EAAE,QAAQ,aAAW;AAEjC,UAAI,YAAY,QAAQ;AACtB,gBAAQ,SAAS,KAAK;AAAA,MACxB;AAAA,IACF,CAAC;AACD,SAAK,SAAS,KAAK;AAAA,MACjB,OAAO,OAAO;AAAA,MACd,QAAQ;AAAA,IACV,CAAC;AACD,SAAK,OAAO,GAAG,MAAM;AAAA,EACvB;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,eAAe,KAAK,KAAK,OAAO,GAAG,YAAY,KAAK;AAAA,EAClE;AAAA;AAAA,EAEA,oBAAoB,OAAO;AACzB,QAAI,MAAM,kBAAkB,wBAAwB;AAClD,WAAK,aAAa,OAAO;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,aAAa;AACpB,aAAO,KAAK;AAAA,IACd;AACA,UAAM,mBAAmB,KAAK,SAAS,SAAS,EAAE,oBAAoB,KAAK,OAAO,EAAE,iBAAiB,CAAC,EAAE,uBAAuB,KAAK,EAAE,SAAS,KAAK,EAAE,sBAAsB,uBAAuB,EAAE,cAAc,CAAC;AAAA,MAClN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC,CAAC;AACF,SAAK,cAAc,KAAK,SAAS,OAAO;AAAA,MACtC;AAAA,MACA,gBAAgB,KAAK,uBAAuB;AAAA,MAC5C,WAAW,KAAK,QAAQ;AAAA,MACxB,aAAa;AAAA,IACf,CAAC;AACD,SAAK,YAAY,YAAY,EAAE,UAAU,MAAM,KAAK,MAAM,CAAC;AAC3D,SAAK,YAAY,cAAc,EAAE,UAAU,WAAS,KAAK,eAAe,KAAK,CAAC;AAC9E,SAAK,YAAY,qBAAqB,EAAE,UAAU,WAAS;AACzD,YAAM,SAAS,gBAAgB,KAAK;AACpC,YAAM,SAAS,KAAK,OAAO,GAAG,iBAAiB,EAAE;AACjD,UAAI,UAAU,UAAU,WAAW,UAAU,CAAC,OAAO,SAAS,MAAM,GAAG;AACrE,aAAK,MAAM;AAAA,MACb;AAAA,IACF,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,mBAAmB;AAEjB,UAAM,WAAW,KAAK,SAAS,KAAK,KAAK;AACzC,UAAM,UAAU,KAAK,QAAQ;AAC7B,QAAI,YAAY,MAAM;AACpB,WAAK,eAAe;AAAA,IACtB,OAAO;AACL,YAAMA,SAAQ,KAAK,OAAO;AAC1B,YAAM,UAAU,KAAK;AACrB,YAAM,aAAa,KAAK,aAAa,QAAQ;AAC7C,YAAM,MAAMA,QAAO,IAAI,KAAK,QAAQ,QAAQ,QAAQ,MAAM,GAAG,GAAG,GAAG,CAAC;AACpE,YAAM,MAAMA,QAAO,IAAI,KAAK,QAAQ,QAAQ,QAAQ,MAAM,GAAG,IAAI,IAAI,CAAC;AACtE,YAAM,WAAW,WAAW,MAAM,QAAQ,OAAO,KAAK,UAAU,IAAI,MAAM,QAAQ,OAAO,KAAK,UAAU;AAExG,UAAI,aAAa,KAAK,kBAAkB;AACtC,aAAK,mBAAmB;AACxB,aAAK,eAAe,gBAAgB,SAAS,KAAK,cAAc,KAAK,KAAK,QAAQ;AAAA,MACpF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,OAAO,SAAS,UAAU;AAC3C,QAAI,cAAc;AAClB,eAAW,UAAU,SAAS;AAC5B,UAAI,SAAS,KAAK,aAAa,SAAS,OAAO,OAAO,KAAK,GAAG;AAC5D,eAAO,OAAO,KAAK;AACnB,6BAAqB,QAAQ,QAAQ;AACrC,kBAAU,MAAM,KAAK,YAAY,cAAc,MAAM,CAAC;AACtD,sBAAc;AAAA,MAChB,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAGA,QAAI,CAAC,aAAa;AAChB,UAAI,UAAU;AACZ,kBAAU,MAAM,KAAK,YAAY,cAAc,QAAQ,CAAC;AACxD,6BAAqB,UAAU,QAAQ;AAAA,MACzC,OAAO;AACL,kBAAU,MAAM,KAAK,YAAY,cAAc,EAAE,CAAC;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,UAAM,UAAU,MAAM;AACtB,QAAI,YAAY,KAAK;AACnB,WAAK,MAAM;AAAA,IACb,WAAW,YAAY,UAAU,CAAC,eAAe,KAAK,GAAG;AACvD,YAAM,eAAe;AACrB,WAAK,MAAM;AAAA,IACb,WAAW,YAAY,OAAO;AAC5B,YAAM,eAAe;AACrB,UAAI,KAAK,YAAY,YAAY;AAC/B,aAAK,aAAa,KAAK,YAAY,UAAU;AAAA,MAC/C,OAAO;AACL,aAAK,MAAM;AAAA,MACb;AAAA,IACF,OAAO;AACL,YAAM,iBAAiB,KAAK,YAAY;AACxC,WAAK,YAAY,UAAU,KAAK;AAChC,YAAM,gBAAgB,KAAK,YAAY;AACvC,UAAI,iBAAiB,kBAAkB,gBAAgB;AACrD,6BAAqB,eAAe,SAAS;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,uBAAuB;AAErB,SAAK,iBAAiB,KAAK,aAAa,cAAc,UAAU,MAAM;AACpE,WAAK,mBAAmB;AACxB,UAAI,KAAK,OAAO,GAAG;AACjB,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,2BAA2B;AACzB,WAAO,MAAM;AACX,YAAMA,SAAQ,KAAK,OAAO;AAC1B,YAAM,UAAU,KAAK,SAAS;AAC9B,UAAI,KAAK,QAAQ,KAAKA,QAAO;AAC3B,aAAK,mBAAmBA,OAAM,MAAM,GAAG,SAAS,IAAI;AAAA,MACtD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,WAAW,SAAS,oBAAoB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,kBAAkB,IAAI,gBAAgB,KAAK,CAAC;AAC/C,QAAG,kBAAkB,IAAI,UAAU,WAAW,CAAC;AAAA,MACjD;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,CAAC;AAAA,MACrB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,eAAe,CAAC,GAAG,eAAe;AAAA,MAClC,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,IACzD;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,eAAe;AAAA,IAC1B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,QAAQ,WAAW,GAAG,wBAAwB,GAAG,gBAAgB,IAAI,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,qBAAqB,OAAO,CAAC;AAAA,IACxJ,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,MAChH;AAAA,IACF;AAAA,IACA,cAAc,CAAC,SAAS;AAAA,IACxB,QAAQ,CAAC,+9CAA+9C;AAAA,IACx+C,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,SAAS;AAAA,MACnB,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,UAAU;AAAA,MACV,QAAQ,CAAC,+9CAA+9C;AAAA,IAC1+C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,SAAS,qBAAqB,QAAQ,UAAU;AAC9C,SAAO,gBAAgB,EAAE,eAAe;AAAA,IACtC,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACH;AAKA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA,EAC/B,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,kBAAkB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,aAAa,OAAO,gBAAgB;AAAA,IAClC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB,OAAO,KAAK;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,SAAS,MAAM;AACrC,UAAM,aAAa,KAAK,WAAW;AACnC,UAAM,SAAS,WAAW,OAAO;AACjC,UAAM,mBAAmB,WAAW,iBAAiB;AACrD,WAAO,UAAU,mBAAmB,mBAAmB;AAAA,EACzD,CAAC;AAAA;AAAA,EAED,gBAAgB,SAAS,MAAM,KAAK,WAAW,EAAE,OAAO,IAAI,EAAE;AAAA;AAAA,EAE9D,gBAAgB,SAAS,MAAM;AAC7B,UAAM,aAAa,KAAK,WAAW;AACnC,WAAO,WAAW,OAAO,IAAI,WAAW,UAAU;AAAA,EACpD,CAAC;AAAA;AAAA,EAED,QAAQ,MAAM,IAAI;AAAA;AAAA,EAElB,aAAa,MAAM,SAAS;AAAA,IAC1B,OAAO;AAAA,EACT,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,MAAM,MAAM;AAAA,IAChB,OAAO;AAAA,IACP,WAAW,WAAS,KAAK,oBAAoB,KAAK;AAAA,EACpD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,MAAM,MAAM;AAAA,IAChB,OAAO;AAAA,IACP,WAAW,WAAS,KAAK,oBAAoB,KAAK;AAAA,EACpD,CAAC;AAAA;AAAA,EAED,WAAW,SAAS,MAAM,KAAK,cAAc,KAAK,KAAK,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1E,gBAAgB,MAAM,OAAO;AAAA,IAC3B,WAAW;AAAA,IACX,OAAO;AAAA,EACT,CAAC;AAAA,EACD,cAAc;AACZ,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,sBAAgB,KAAK,cAAc,KAAK,YAAY;AAAA,IACtD;AACA,UAAM,WAAW,OAAO,SAAS;AACjC,SAAK,aAAa,KAAK,cAAc;AACrC,SAAK,uBAAuB;AAC5B,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB;AACzB,SAAK,sBAAsB,KAAK,aAAa,cAAc,UAAU,MAAM;AACzE,UAAI,CAAC,KAAK,UAAU,GAAG;AACrB,aAAK,aAAa,KAAK,MAAM,CAAC;AAAA,MAChC;AAAA,IACF,CAAC;AAGD,SAAK,gBAAgB,SAAS,OAAO,KAAK,iBAAiB,EAAE,eAAe,SAAS,KAAK,YAAY;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAIhB,UAAM,eAAe,KAAK,aAAa,YAAY,KAAK;AACxD,SAAK,MAAM,IAAI,KAAK,aAAa,mBAAmB,YAAY,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,IAAI;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,YAAY;AAC3B,SAAK,kBAAkB,IAAI,UAAU;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS;AAChB,WAAO,KAAK,WAAW,OAAO;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,IAAI;AAC5B,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,mBAAmB;AACjB,WAAO,KAAK,YAAY,0BAA0B,KAAK,KAAK;AAAA,EAC9D;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,YAAY,cAAc,MAAM;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,yBAAyB,YAAY;AAC1C,SAAK,oBAAoB,YAAY;AAAA,EACvC;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,YAAY,WAAW,KAAK;AAAA,EAC1C;AAAA;AAAA,EAEA,eAAe,MAAM;AACnB,QAAI,CAAC,KAAK,SAAS,GAAG;AACpB,WAAK,WAAW,EAAE,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,UAAM,eAAe,KAAK,MAAM;AAChC,UAAM,OAAO,KAAK,aAAa,UAAU,OAAO,KAAK,aAAa,MAAM,SAAS;AACjF,UAAM,aAAa,CAAC,KAAK,aAAa,SAAS,MAAM,YAAY;AACjE,QAAI,CAAC,QAAQ,cAAc,CAAC,EAAE,SAAS,CAAC,eAAe;AAErD,WAAK,qBAAqB,MAAM,IAAI;AAAA,IACtC,OAAO;AAGL,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,QAAQ,KAAK,MAAM;AAEzB,QAAI,SAAS,KAAK,SAAS,KAAK,GAAG;AACjC,WAAK,aAAa,KAAK;AAAA,IACzB;AACA,QAAI,CAAC,KAAK,WAAW,EAAE,OAAO,GAAG;AAC/B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,OAAO;AAEpB,QAAI,KAAK,WAAW,EAAE,OAAO,KAAK,KAAK,SAAS,GAAG;AACjD;AAAA,IACF;AACA,QAAI,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM;AAC/E,YAAM,eAAe;AACrB,WAAK,MAAM,IAAI,IAAI;AACnB,WAAK,aAAa,IAAI;AAAA,IACxB,WAAW,MAAM,YAAY,cAAc,MAAM,YAAY,UAAU;AACrE,YAAM,eAAe;AACrB,WAAK,WAAW,EAAE,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,yBAAyB;AACvB,WAAO,MAAM;AACX,YAAM,QAAQ,KAAK,aAAa,YAAY,KAAK,MAAM,CAAC;AACxD,YAAM,WAAW,KAAK;AACtB,WAAK,kBAAkB,KAAK,SAAS,KAAK;AAE1C,UAAI,CAAC,KAAK,UAAU,GAAG;AACrB,aAAK,aAAa,KAAK;AAAA,MACzB;AACA,UAAI,SAAS,KAAK,iBAAiB;AACjC,aAAK,iBAAiB;AAAA,MACxB;AAEA,UAAI,aAAa,KAAK,iBAAiB;AACrC,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB;AACpB,WAAO,MAAM;AACX,YAAM,aAAa,KAAK,WAAW;AACnC,iBAAW,cAAc,IAAI;AAC7B,iBAAW,OAAO,UAAU,MAAM,KAAK,aAAa,CAAC;AACrD,iBAAW,SAAS,UAAU,CAAC;AAAA,QAC7B;AAAA,MACF,MAAM;AACJ,YAAI,CAAC,KAAK,aAAa,SAAS,OAAO,KAAK,MAAM,CAAC,GAAG;AACpD,eAAK,qBAAqB,OAAO,IAAI;AACrC,eAAK,aAAa,KAAK;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,0BAA0B;AACxB,WAAO,MAAM;AAEX,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,qBAAqB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,WAAW,qBAAqB;AACnD,QAAI,aAAa,QAAQ,CAAC,KAAK,SAAS,SAAS,GAAG;AAClD,WAAK,MAAM,IAAI,SAAS;AAAA,IAC1B,OAAO;AAML,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,QAAQ,mBAAmB,KAAK,kBAAkB,KAAK,MAAM,CAAC;AAC7E,YAAM,QAAQ,QAAQ,SAAS,SAAS;AACxC,YAAM,UAAU,QAAQ,WAAW,SAAS;AAC5C,YAAM,UAAU,QAAQ,WAAW,SAAS;AAC5C,WAAK,MAAM,IAAI,SAAS,QAAQ,QAAQ,QAAQ,OAAO,SAAS,OAAO,IAAI,SAAS;AAAA,IACtF;AACA,QAAI,qBAAqB;AACvB,WAAK,YAAY,KAAK,MAAM,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,YAAQ,KAAK,aAAa,mBAAmB,KAAK;AAClD,SAAK,YAAY,cAAc,QAAQ,SAAS,OAAO,KAAK,KAAK,aAAa,OAAO,OAAO,KAAK,aAAa,QAAQ,SAAS;AAAA,EACjI;AAAA;AAAA,EAEA,SAAS,OAAO;AACd,WAAO,CAAC,SAAS,KAAK,aAAa,QAAQ,KAAK;AAAA,EAClD;AAAA;AAAA,EAEA,oBAAoB,OAAO;AACzB,UAAM,OAAO,OAAO,UAAU,WAAW,KAAK,aAAa,UAAU,OAAO,KAAK,aAAa,MAAM,SAAS,IAAI,KAAK,aAAa,YAAY,KAAK;AACpJ,WAAO,QAAQ,KAAK,aAAa,QAAQ,IAAI,IAAI,OAAO;AAAA,EAC1D;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,kCAAkC,MAAM,KAAK,YAAY;AAAA,EAClE;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,WAAW,QAAQ,CAAC,MAAM,KAAK,kBAAkB,OAAO;AAAA,MAC7D,sBAAsB;AAAA,QACpB,QAAQ,KAAK,YAAY,cAAc;AAAA,MACzC;AAAA,IACF,GAAG,aAAW;AACZ,YAAM,eAAe,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,QAAQ,KAAK,CAAC;AACtG,YAAM,MAAM,KAAK,IAAI;AACrB,aAAO,CAAC,OAAO,CAAC,gBAAgB,KAAK,aAAa,YAAY,KAAK,YAAY,KAAK,IAAI,OAAO;AAAA,QAC7F,oBAAoB;AAAA,UAClB,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,GAAG,aAAW;AACZ,YAAM,eAAe,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,QAAQ,KAAK,CAAC;AACtG,YAAM,MAAM,KAAK,IAAI;AACrB,aAAO,CAAC,OAAO,CAAC,gBAAgB,KAAK,aAAa,YAAY,KAAK,YAAY,KAAK,IAAI,OAAO;AAAA,QAC7F,oBAAoB;AAAA,UAClB,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,iBAAiB,EAAE,CAAC;AAAA,IAC1C,WAAW,CAAC,QAAQ,YAAY,QAAQ,QAAQ,iBAAiB,WAAW,GAAG,sBAAsB;AAAA,IACrG,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,SAAS,6CAA6C;AAC1E,iBAAO,IAAI,YAAY;AAAA,QACzB,CAAC,EAAE,SAAS,SAAS,4CAA4C,QAAQ;AACvE,iBAAO,IAAI,aAAa,OAAO,OAAO,KAAK;AAAA,QAC7C,CAAC,EAAE,WAAW,SAAS,8CAA8C,QAAQ;AAC3E,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,YAAY,IAAI,SAAS,CAAC;AAC5C,QAAG,YAAY,yBAAyB,IAAI,sBAAsB,CAAC,EAAE,iBAAiB,IAAI,cAAc,CAAC,EAAE,iBAAiB,IAAI,cAAc,CAAC,EAAE,sBAAsB,UAAU,IAAI,WAAW,MAAM,OAAO,OAAO,QAAQ,OAAO;AAAA,MACrO;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,YAAY,CAAC,GAAG,iBAAiB,YAAY;AAAA,MAC7C,KAAK,CAAC,GAAG,oBAAoB,KAAK;AAAA,MAClC,KAAK,CAAC,GAAG,oBAAoB,KAAK;AAAA,MAClC,eAAe,CAAC,GAAG,YAAY,eAAe;AAAA,IAChD;AAAA,IACA,SAAS;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gCAAgC;AAAA,QAChC,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,4BAA4B;AAAA,QAC5B,cAAc;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,oBAAoB,MAAM;AACxB,UAAM,QAAQ,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MACvD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,SAAS,OAAO,KAAK;AAC3B,WAAO,MAAM,MAAM,IAAI,OAAO;AAAA,EAChC,GAAG;AAAA,EACH,cAAc,SAAS,MAAM;AAC3B,UAAM,aAAa,KAAK,WAAW;AACnC,WAAO,KAAK,SAAS,KAAK,WAAW,SAAS;AAAA,EAChD,CAAC;AAAA;AAAA,EAED,aAAa,MAAM,SAAS;AAAA,IAC1B,OAAO;AAAA,EACT,CAAC;AAAA;AAAA,EAED,YAAY,MAAM,QAAW;AAAA,IAC3B,OAAO;AAAA,EACT,CAAC;AAAA;AAAA,EAED,iBAAiB,MAAM,QAAW;AAAA,IAChC,OAAO;AAAA,EACT,CAAC;AAAA;AAAA,EAED,oBAAoB;AAAA;AAAA,EAEpB,WAAW,MAAM,OAAO;AAAA,IACtB,WAAW;AAAA,IACX,OAAO;AAAA,EACT,CAAC;AAAA;AAAA,EAED,WAAW,MAAM,KAAK,gBAAgB;AAAA;AAAA,EAEtC,gBAAgB,MAAM,KAAK,gBAAgB,iBAAiB,OAAO;AAAA,IACjE,WAAW;AAAA,EACb,CAAC;AAAA;AAAA,EAED,MAAM,OAAO;AACX,QAAI,KAAK,WAAW,KAAK,CAAC,KAAK,YAAY,GAAG;AAC5C,WAAK,WAAW,EAAE,KAAK;AACvB,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK,eAAe,IAAI,OAAO,KAAK,UAAU,KAAK,KAAK;AAAA,EACjE;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,WAAW,CAAC,GAAG,uBAAuB;AAAA,IACtC,UAAU;AAAA,IACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,6CAA6C,QAAQ;AACnF,iBAAO,IAAI,MAAM,MAAM;AAAA,QACzB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,YAAY,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,OAAO,YAAY;AAAA,MACnC,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,MACvD,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,eAAe,CAAC,GAAG,eAAe;AAAA,IACpC;AAAA,IACA,UAAU,CAAC,qBAAqB;AAAA,IAChC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,mBAAmB,IAAI,QAAQ,UAAU,iBAAiB,WAAW,GAAG,YAAY,eAAe,GAAG,CAAC,UAAU,QAAQ,SAAS,QAAQ,WAAW,kBAAkB,QAAQ,gBAAgB,aAAa,SAAS,eAAe,QAAQ,GAAG,oCAAoC,GAAG,CAAC,KAAK,8VAA8V,CAAC;AAAA,IACroB,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,UAAU,CAAC;AAChC,QAAG,aAAa,GAAG,GAAG,MAAM,mDAAmD,GAAG,CAAC;AACnF,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,YAAY,IAAI,YAAY,CAAC,EAAE,iBAAiB,IAAI,cAAc,CAAC;AACjF,QAAG,YAAY,cAAc,IAAI,aAAa,CAAC,EAAE,mBAAmB,IAAI,eAAe,CAAC,EAAE,iBAAiB,IAAI,WAAW,EAAE,OAAO,CAAC,EAAE,YAAY,IAAI,YAAY,IAAI,KAAK,IAAI,SAAS,CAAC;AAAA,MAC3L;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,mBAAmB;AAAA;AAAA;AAAA;AAAA,QAInB,WAAW;AAAA,MACb;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,aAAa;AAAA,MACvB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe,oBAAoB,mBAAmB;AAAA,IAChE,SAAS,CAAC,qBAAqB,eAAe,oBAAoB,mBAAmB;AAAA,EACvF,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe,qBAAqB,mBAAmB;AAAA,EACnE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,oBAAoB,mBAAmB;AAAA,MAChE,SAAS,CAAC,qBAAqB,eAAe,oBAAoB,mBAAmB;AAAA,IACvF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["input"]}